{% import 'admin/layout.html' as layout with context -%}
{% import 'admin/cms_layout.html' as cms_layout with context -%}
{% import 'admin/static.html' as admin_static with context -%}
<!DOCTYPE html>
<html>
    <head>
        <title>{% block title %}{% if admin_view.category %}{{ admin_view.category }} - {% endif %}{{ admin_view.name }} - {{ admin_view.admin.name }}{% endblock %}</title>

        {% block head_meta %}
            <meta charset="UTF-8">
            <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
            <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta name="description" content="">
            <meta name="author" content="">
        {% endblock %}

        {% block head_css %}
            <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro&display=swap" rel="stylesheet">
             {% assets "css" %}
                <link rel="stylesheet" href="{{ ASSET_URL }}"></link>
            {% endassets %}
        {% endblock %}

        {% block head %}
        {% endblock %}

        {% block head_tail %}{% endblock %}
    </head>

    <body class="{% block skin %}skin-blue-light{% endblock %} layout-top-nav">
        {% block page_body %}
            <div class="wrapper">
                <header class="main-header">
                    <nav class="navbar navbar-static-top">
                       <div class="container">
                           <div class="collapse navbar-collapse pull-left" id="navbar-collapse">
                                <ul class="nav navbar-nav">
                                    {{ cms_layout.cms_top_menu() }}
                                </ul>
                           </div>
                       </div>
                    </nav>
                </header>

                <div class="content-wrapper">
                    {% set render_ctx = h.resolve_ctx() %}
                    {% block body %}{% endblock %}
                </div>
            </div>
        {% endblock %}

        {% block tail_js %}
            {% assets "js" %}
                <script type="text/javascript" src="{{ ASSET_URL }}"></script>
            {% endassets %}
        {% endblock %}

        {% block tail %}{% endblock %}
    </body>
</html>
