{% import 'admin/layout.html' as layout with context -%}
{% import 'admin/cms_layout.html' as cms_layout with context -%}
{% import 'admin/static.html' as admin_static with context -%}


<!DOCTYPE html>
<html>
    <head>
        <title>
            {% if current_user.is_authenticated  %}
                {% block title %}
                    {% if admin_view is defined %}
                        {% if admin_view.category %}{{ admin_view.category|striptags }} - {% endif %}{{ admin_view.name }} - {{ admin_view.admin.name }}
                    {% else %}
                        {% if title is defined %}{{title}}{% else %}CMS{% endif %}
                    {% endif %}
                {% endblock %}
            {% else %}
                {{ config.DEFAULT_TITLE }}
            {% endif %}
        </title>

        {% block head_meta %}
            <meta charset="UTF-8">
            <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
            <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta name="description" content="">
            <meta name="author" content="">
        {% endblock %}

        {% block head_css %}
            <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro&display=swap" rel="stylesheet">
            {% assets "css" %}
                <link rel="stylesheet" href="{{ ASSET_URL }}"></link>
            {% endassets %}
            <link rel="stylesheet" href="https://uicdn.toast.com/editor/latest/toastui-editor.min.css" />
        {% endblock %}

        {% block head %}
            <script type="text/javascript">
                function setThumbnail(event) {
                    var $field = $(event.target).parents('div.image-container');
                    var $img = $field.find('img');

                    var file = $field.find('input[type=file]').prop('files')[0];
                    var reader = new FileReader();

                    $field.find('.image-thumbnail').show();
                    $field.find('input[type="checkbox"]').prop('checked', false);
                    $field.attr('orig', $img.attr('src'));

                    reader.addEventListener('load', function () {
                        $field.attr('orig', $img.attr('src'));
                        $img.attr('src', reader.result);

                        $field.find('button').show();
                    }, false);

                    if (file) {
                        reader.readAsDataURL(file);
                    }
                }
            </script>
        {% endblock %}

        {% block head_tail %}
            {% if current_user and current_user.cms_color %}
                <style>
                    .main-header > * {
                        background-color: {{current_user.cms_color }}!important;
                    }
                </style>
            {% endif %}
        {% endblock %}
    </head>

    <body class="{% block skin %}skin-blue{% endblock %}">
        {% block page_body %}
            <div class="wrapper">
                <header class="main-header">
                    {% block brand %}
                    <a href="/" class="logo">
                        <span class="logo-mini">{{ config['SHORT_NAME'] }}</span>
                        <span class="logo-lg">{{ config['NAME'] }}</span>
                    </a>
                    {% endblock %}
                    <nav class="navbar navbar-static-top">
                        <a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
                            <span class="sr-only">Toggle navigation</span>
                        </a>
                        {% block access_control %}
                            <div class="navbar-custom-menu">
                                <ul class="nav navbar-nav navbar-right" style="margin: 0">
                                    {% if current_user and current_user.is_authenticated %}
                                        {% block additional_controls %}{% endblock %}
                                        <li class="dropdown user user-menu">
                                            <a class="dropdown-toggle" data-toggle="dropdown" href="#">
                                                <i class="glyphicon glyphicon-user"></i>&nbsp;<span>{{ current_user.name or current_user.email or current_user.login }}</span>
                                            </a>
                                            <ul class="dropdown-menu">
                                                <li class="user-header">
                                                    <p>
                                                        {{ current_user.name or '' }}
                                                        <small>{{ current_user.email or ''}}</small>
                                                    </p>
                                                    {% if current_user.is_admin %}<p class="text-red">superuser</p>{% endif %}
                                                    <p>{{ current_user.roles|join(', ') }}</p>
                                                <li/>
                                                <li class="user-body">
                                                    {% if config.ADMIN_TYPE == "CMS" and config.COMMENTS %}
                                                        <div class="row">
                                                            <div class="text-center">
                                                                <a href="{{ url_for('comments.index_view', own=1) }}">Comments</a>
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                    {% if current_user.is_allowed('security', 'change_password') %}
                                                        <div class="row">
                                                            <div class="text-center">
                                                                <a href="{{ url_for_security('change_password') }}">Change Password</a>
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                    {% if config.ADMIN_TYPE == "CMS" and config.USER_SETTINGS and current_user.is_allowed('user_settings', 'edit') and current_user.__class__.__name__ == "User" %}
                                                        <div class="row">
                                                            <div class="text-center">
                                                                <a href="{{ url_for('user_settings.edit_view', id=current_user.id, url=url_for(request.endpoint, **request.view_args)) }}">Settings</a>
                                                            </div>
                                                        </div>
                                                    {% elif current_user.is_allowed('ocp_user_settings', 'edit') and current_user.__class__.__name__ == "OCPUser" %}
                                                        <div class="row">
                                                            <div class="text-center">
                                                                <a href="{{ url_for('ocp_user_settings.edit_view', id=current_user.id, url=url_for(request.endpoint, **request.view_args)) }}">Settings</a>
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                </li>
                                                <li class="user-footer">
                                                    {% if current_user.is_allowed('user', 'view') %}
                                                        <div class="pull-left">
                                                            <a class="btn btn-default btn-flat" href="{{ url_for('user.edit_view', id=current_user.id) }}">Profile</a>
                                                        </div>
                                                    {% endif %}
                                                    {% if current_user.is_allowed('user_activity', 'timeline') %}
                                                        <div class="pull-left">
                                                            <a class="btn btn-default btn-flat" href="{{ url_for('user_activity.timeline') }}">Activity</a>
                                                        </div>
                                                    {% endif %}
                                                    <div class="pull-right">
                                                        <a class="btn btn-default btn-flat" href="/logout">Logout</a>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                    {% else %}
                                        <li><a href="/login" class="">Login</a></li>
                                    {% endif %}
                                </ul>
                            </div>
                        {% endblock %}
                    </nav>
                </header>
                <aside class="main-sidebar">
                    <section class="sidebar">
                        <div class="sidebar-search">
                            <div class="form-inline">
                                <div class="input-group sidebar-form" data-widget="sidebar-search">
                                    <input class="form-control form-control-sidebar" type="text" placeholder="Search" aria-label="Search">
                                    <div class="input-group-append">
                                        <button class="btn btn-sidebar">
                                            <i class="fa fa-search fa-fw"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% block main_menu %}
                            <ul class="nav-sidebar sidebar-menu">
                                <li class="header">MAIN NAVIGATION</li>
                                {{ cms_layout.cms_menu() }}
                            </ul>
                        {% endblock %}
                    </section>
                </aside>
                <div class="content-wrapper">
                    {% block messages %}
                        {{ layout.messages() }}
                    {% endblock %}

                    {% if current_user.is_authenticated %}
                        {% set render_ctx = h.resolve_ctx() %}
                    {% endif %}

                    {% block body %}{% endblock %}
                </div>
                <footer class="main-footer">
                    <div class="pull-right hidden-xs">
                        {% block version %}
                        <b>Version</b> {{ config.APP_VERSION }}
                        {% endblock %}
                    </div>
                    {% block copyright %}
                        {{ config.COPYRIGHT|safe }} {{ datetime.utcnow().year }}
                    {% endblock %}
                </footer>
            </div>
        {% endblock %}

        {% block tail_js %}
            <script src="https://uicdn.toast.com/editor/latest/toastui-editor-all.min.js"></script>

            {% assets "js" %}
                <script type="text/javascript" src="{{ ASSET_URL }}"></script>
            {% endassets %}

            <script type="text/javascript">
                $(document).ready(function() {
                    $('.form-control[hint]').popover({
                        html: true,
                        trigger: 'hover focus',
                        placement: 'bottom',
                        container: $('body'),
                        content: function() {
                            return $(this).attr('hint')
                        }
                    });

                    $('.tz-select').select2().on('change', function(event) {
                        let $select = $(event.target);
                        let $input = $select.closest('.row').find('input');

                        let tz = $select.val();
                        let fmt = $input.data('date-format');
                        let utc_dt = moment($input.val(), fmt);
                        let dt = utc_dt.tz($select.data('prev'), true);

                        $input.val(dt.tz(tz).format(fmt));
                        $select.data('prev', tz);
                    })
                })


                {% set view_prefix = (admin_view.endpoint if admin_view else endpoint or '') %}

                viewStorage = {

                    setItem: function(_key, value, skip_dispatching, prefix) {
                        let key = (prefix || '{{view_prefix}}') + '-' + _key;
                        localStorage.setItem(key, value);

                        if (!skip_dispatching) {
                            let event = new Event('storage');

                            event.value = value;
                            event.key = key;

                            window.dispatchEvent(event);
                        }
                    },

                    getItem: function(_key, prefix) {
                        let key = (prefix || '{{view_prefix}}') + '-' + _key;
                        return localStorage.getItem(key);
                    },

                    removeItem: function(_key, prefix) {
                        let key = (prefix || '{{view_prefix}}') + '-' + _key;

                        return localStorage.removeItem(key);
                    }
                }
            </script>

            <script>
                $(document).ready(function() {
                    $('.ellipsis-tip').each(function (_, el) {
                        var $el = $(el)
                        var content = $el.html();
                        var $el = $el.parent();

                        $el.attr('tabindex', 0);

                        $el.popover({
                            html: true,
                            trigger: 'focus',
                            placement: 'bottom',
                            container: $('body'),
                            content: function() {
                                return content
                            }
                        });
                    });

                });
            </script>

        {% endblock %}
    </body>
</html>
