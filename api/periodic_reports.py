from datetime import datetime as dt
from io import BytesIO

import fs
from flask import (
    Response,
    abort,
    current_app,
    jsonify,
    request,
)
from werkzeug.wsgi import FileWrapper

from api.common import TIME_FORMAT
from app.clickhouse.reports.models import (
    Report,
    ScheduledTemplate,
)


def init(api):
    @api.route("/clickhouse/periodic-reports", methods=["GET"])
    def periodic_reports_list():

        return jsonify(
            [
                {
                    "id": str(template.id),
                    "slug": template.slug,
                }
                for template in ScheduledTemplate.objects()
            ]
        )

    @api.route("/clickhouse/periodic-reports/<slug>", methods=["GET"])
    def periodic_reports_for_template(slug: str):

        generated_for_time_less = request.args.get("generated_for_time_less")
        generated_for_time_more = request.args.get("generated_for_time_more")
        limit = request.args.get("limit", 20, int)
        offset = request.args.get("offset", 0, int)
        conditions = {"template__slug": slug, "generated__exists": True}

        if generated_for_time_more:
            conditions["generated_for_time__gte"] = dt.strptime(generated_for_time_more, TIME_FORMAT)
        if generated_for_time_less:
            conditions["generated_for_time__lte"] = dt.strptime(generated_for_time_less, TIME_FORMAT)

        reports = Report.objects(**conditions).order_by("-created")
        if offset:
            reports = reports.skip(offset)

        reports = reports.limit(limit).all()

        return jsonify(
            [
                {
                    "report_id": str(report.id),
                    "generated_for_time": report.generated_for_time.isoformat("T", "seconds"),
                    "generated": report.generated.isoformat("T", "seconds"),
                    "created": report.created.isoformat("T", "seconds"),
                }
                for report in reports
            ]
        )

    @api.route("/clickhouse/periodic-reports/<slug>/report/<id>/download", methods=["GET"])
    def download_report(slug, id):
        report = Report.objects.get_or_404(id=id)
        reports_storage = fs.open_fs(current_app.config["REPORTS_STORAGE"])
        fname = f"{slug}-{report.generated_for_time.strftime(TIME_FORMAT)}"

        if report.path_formated:
            try:
                content = reports_storage.readbytes(report.path_formated)
            except fs.errors.ResourceNotFound:
                abort(404)
            w = FileWrapper(BytesIO(content))
            resp = Response(w, mimetype="application/excel", direct_passthrough=True)
            resp.headers["Content-Disposition"] = "attachment; filename=" + fname
            resp.headers["Content-type"] = report.content_type

            return resp

        content = reports_storage.readbytes(report.path)
        w = FileWrapper(BytesIO(content))
        resp = Response(w, mimetype="application/excel", direct_passthrough=True)
        resp.headers["Content-Disposition"] = "attachment; filename=" + fname
        resp.headers["Content-type"] = report.content_type

        return resp
