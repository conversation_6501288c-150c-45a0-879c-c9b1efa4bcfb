from __future__ import annotations

from datetime import datetime
from typing import (
    Generator,
    Type,
)

from flask import (
    Blueprint,
    current_app,
    jsonify,
    url_for,
)
from flask.views import MethodView

from app.abcvod.providers.amedia2.models import Amedia2ImportTracker
from app.abcvod.providers.etnomedia.models import EtnomediaImportTracker
from app.abcvod.providers.premier.cardgroup.models import (
    PremierCardgroupExportToClickhouseTracker,
    PremierCardgroupImportTracker,
)
from app.abcvod.providers.premier.showcase.models import (
    PremierShowcaseExportToClickhouseTracker,
    PremierShowcaseImportTracker,
)
from app.abcvod.providers.start2.models import Start2ImportTracker
from app.abcvod.providers.vipplay.models import VipPlayImportTracker
from app.abcvod.providers.wink.models import WinkImportTracker
from app.archive.models import (
    ArchiveEpgImportTracker,
    EpgCleanTracker,
)
from app.cms.models import (
    CeleryTaskTracker,
    TaskStatus,
)
from app.medias.models import EPGImportFromMediaTracker

# TrackerCls: "APP_CONFIG_PARAM_NAME" (if defined in config)
trackers_config: dict[Type[CeleryTaskTracker], str] = {
    Amedia2ImportTracker: "AMEDIA2",
    EtnomediaImportTracker: "ETNOMEDIA",
    WinkImportTracker: "WINK",
    PremierCardgroupExportToClickhouseTracker: "PREMIER",
    PremierCardgroupImportTracker: "PREMIER",
    PremierShowcaseExportToClickhouseTracker: "PREMIER_SHOWCASE",
    PremierShowcaseImportTracker: "PREMIER_SHOWCASE",
    Start2ImportTracker: "START2",
    VipPlayImportTracker: "VIPPLAY",
    ArchiveEpgImportTracker: "",
    EPGImportFromMediaTracker: "",
    EpgCleanTracker: "",
}

job_id_map: dict[str, Type[CeleryTaskTracker]] = {tracker_cls.job_id: tracker_cls for tracker_cls in trackers_config}


class ApiEndpoint(MethodView):
    @classmethod
    def get_description(cls):
        return {
            "doc": cls.__doc__,
            "urls": cls.get_urls_for_description(),
        }

    @classmethod
    def get_urls_for_description(cls) -> list[str]:
        raise NotImplementedError


class PeriodicTasksDiscovery(ApiEndpoint):
    """Get info about all possible periodic tasks for monitoring.

    Format example:
        {
            "jobs": [
                {
                    "job_id": "vod-import-moretv",
                    "job_group_id": "vod-import",
                    "title": "MoreTV VOD content import"
                },
                ...
            ],
        }
    """

    @classmethod
    def get_urls_for_description(cls):
        return [url_for("api.monitoring-regular-jobs-discovery")]

    def get(self):
        result = []
        for tracker, config_param_name in trackers_config.items():
            if config_param_name and not current_app.config.get(config_param_name):
                continue
            result.append(
                {
                    "title": tracker.job_title,
                    "job_id": tracker.job_id,
                    "job_group_id": tracker.job_group_id,
                }
            )
        return jsonify(result)


class PeriodicTasksMonitoring(ApiEndpoint):
    """Get monitoring info for periodic task.

    Format:
        {
            "current_timestamp": current timestamp at request time in RFC3339,
            "last_run_result": result for latest completed task.
                               0 - clean run
                               1 - productive run with error_ratio > 0
                               2 - failed run
                               10 - there were no run yet
            "last_run_status": verbose status for latest completed task,
            "last_run_duration_seconds": run duration for latest completed task (in seconds),
            "last_productive_run_error_ratio": error ratio for latest productive (not failed) task,
            "last_productive_run_error_count": amount of errors in latest productive task,
            "hours_since_productive_run": hours since completion of latest productive task,
            "hours_since_clean_run": hours since completion of latest clean task (task without errors),
        }
    """

    @classmethod
    def get_urls_for_description(cls) -> Generator[str, None, None]:
        for tracker_cls, config_param in trackers_config.items():
            if config_param and not current_app.config.get(config_param):
                continue  # Tracker is disabled.
            yield url_for("api.monitoring-regular-jobs", job_id=tracker_cls.job_id)

    def get_last_2_runs(
        self, tracker_class: Type[CeleryTaskTracker]
    ) -> tuple[CeleryTaskTracker | None, CeleryTaskTracker | None]:
        queryset = tracker_class.objects(status__in=TaskStatus.complete_statuses).order_by("-created_at")[-2:]
        queryset_len = len(queryset)
        latest: CeleryTaskTracker = queryset[0] if queryset_len else None
        previous: CeleryTaskTracker = queryset[1] if queryset_len > 1 else None
        return latest, previous

    def get_latest_complete_run(self, tracker_class: Type[CeleryTaskTracker]) -> CeleryTaskTracker | None:
        return tracker_class.objects(status__in=TaskStatus.complete_statuses).order_by("-created_at").first()

    def get_latest_productive_run(self, tracker_class: Type[CeleryTaskTracker]) -> CeleryTaskTracker | None:
        return tracker_class.objects(status__in=TaskStatus.success_statuses).order_by("-created_at").first()

    def get_latest_clean_run(self, tracker_class: Type[CeleryTaskTracker]) -> CeleryTaskTracker | None:
        return tracker_class.objects(status=TaskStatus.DONE).order_by("-created_at").first()

    def get_hours_since_run(self, current_time: datetime, tracker: CeleryTaskTracker | None) -> float:
        if not tracker:
            return -1
        total_seconds = (current_time - tracker.finished_at).total_seconds()
        return (total_seconds / 60) / 60

    def get_run_result(self, tracker: CeleryTaskTracker) -> int:
        """Get run result for completed task.

        0 - clean run
        1 - productive run with error_ratio > 0
        2 - failed run
        10 - there were no run yet
        """
        if not tracker:
            return 10
        if tracker.status == TaskStatus.DONE:
            return 0
        if tracker.status == TaskStatus.DONE_WITH_ERRORS:
            return 1
        return 2

    def get_run_duration(self, tracker: CeleryTaskTracker | None):
        if not tracker:
            return -1
        if not tracker.finished_at or not tracker.started_at:
            return -1
        return (tracker.finished_at - tracker.started_at).total_seconds()

    def get(self, job_id):
        if job_id not in job_id_map:
            return f"There is no known tracker with {job_id=}", 400
        tracker_class: Type[CeleryTaskTracker] = job_id_map[job_id]
        latest_completed = self.get_latest_complete_run(tracker_class)
        latest_productive = self.get_latest_productive_run(tracker_class)
        latest_clean = self.get_latest_clean_run(tracker_class)
        current_time = datetime.utcnow()
        result = {
            "current_timestamp": current_time.isoformat("T") + "Z",
            "last_run_result": self.get_run_result(latest_completed),
            "last_run_status": latest_completed.status if latest_completed else "",
            "last_run_duration_seconds": self.get_run_duration(latest_completed),
            "last_productive_run_error_ratio": latest_productive.error_ratio if latest_productive else -1,
            "last_productive_run_error_count": latest_productive.get_error_count() if latest_productive else -1,
            "hours_since_productive_run": self.get_hours_since_run(current_time, latest_productive),
            "hours_since_clean_run": self.get_hours_since_run(current_time, latest_clean),
            "hours_since_any_run": self.get_hours_since_run(current_time, latest_completed),
        }
        return jsonify(result)


def make_monitoring(api_blueprint: Blueprint):
    api_blueprint.add_url_rule(
        "/monitoring/regular-jobs/",
        endpoint="monitoring-regular-jobs-discovery",
        view_func=PeriodicTasksDiscovery.as_view("monitoring-discovery"),
    )
    api_blueprint.add_url_rule(
        "/monitoring/regular-jobs/<job_id>/",
        endpoint="monitoring-regular-jobs",
        view_func=PeriodicTasksMonitoring.as_view("monitoring"),
    )
