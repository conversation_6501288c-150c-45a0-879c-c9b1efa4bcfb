from datetime import datetime as dt

from flask import (
    current_app,
    jsonify,
)

from app.proto.services import (
    UpsaleService,
    make_seller_services,
)
from app.seller.proto.seller import UpsaleStatus


def init(api):
    make_seller_services(current_app)
    seller_service = UpsaleService.get_instance()

    @api.route("/upsale_errors", methods=["GET"])
    def upsale_errors():
        response = seller_service.get_requests(
            status=[UpsaleStatus.UPSALE_SYNC_ERROR, UpsaleStatus.UPSALE_PURCHASE_ERROR],
            out_of_retry_only=True,
        )

        now = dt.utcnow().timestamp()

        oldest = 0
        if response.requests:
            oldest = round(now - min(req.created_at.timestamp() for req in response.requests if req.created_at))

        return jsonify(
            {
                "error_requests_count": response.total_requests_count,
                "oldest_error_request_age": oldest,
            }
        )
