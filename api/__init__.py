import os
import sys

from flask import (
    Blueprint,
    Flask,
    redirect,
)
from flask_mongoengine import MongoEngine


def _fix_sys_path():
    # Copy-paste from `app.__init__.py`
    # Imports in generated code do not use . in 'from .relative_module import something' statements.
    # Thus, we must add them to pythonpath.
    sys.path.insert(0, "app/vod/proto_megogo")
    sys.path.insert(0, "app/vod/vod/proto")
    sys.path.insert(0, "app/authhistory/proto")
    sys.path.insert(0, "app/linkshortener/proto")
    sys.path.insert(0, "app/promo/proto")
    sys.path.insert(0, "app/purchases/proto")
    sys.path.insert(0, "app/seller/proto")
    sys.path.insert(0, "app/sesskeeper/proto")
    sys.path.insert(0, "app/stores/proto")
    sys.path.insert(0, "app/stores/proto/models")


def make_api():
    _fix_sys_path()
    current_directory = os.path.dirname(os.path.abspath(__file__))
    api = Flask(__name__, template_folder=os.path.join(current_directory, "templates"))

    api.config.from_object("config.DefaultConfig")
    api.config.from_pyfile("../config.cfg", silent=True)

    with api.app_context():
        api.extensions["db"] = MongoEngine(api)

        api_blueprint = Blueprint("api", __name__, url_prefix="/api")
        api.add_url_rule("/", view_func=lambda: redirect("/api"))

        from api import channels

        channels.init(api_blueprint)

        from api import periodic_reports

        periodic_reports.init(api_blueprint)

        from api import monitoring

        monitoring.make_monitoring(api_blueprint)

        from api import upsale

        upsale.init(api_blueprint)

        from api.index import IndexView

        api_blueprint.add_url_rule("", endpoint="", view_func=IndexView.as_view("index"))
        api_blueprint.add_url_rule("/", endpoint="/", view_func=IndexView.as_view("index"))

        api.register_blueprint(api_blueprint)

    return api
