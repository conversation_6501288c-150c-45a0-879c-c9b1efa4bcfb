from flask import render_template
from flask.views import MethodView

from api.monitoring import (
    PeriodicTasksDiscovery,
    PeriodicTasksMonitoring,
)


class IndexView(MethodView):
    template_name = "index.html"

    def get(self):
        return render_template(self.template_name, **self.get_context())

    def get_context(self) -> dict:
        result = {}
        endpoints = {
            "Periodic tasks monitoring": PeriodicTasksMonitoring.get_description(),
            "Monitoring discovery": PeriodicTasksDiscovery.get_description(),
        }
        result["endpoints"] = endpoints
        return result
