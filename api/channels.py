from urllib.parse import urlparse

from flask import jsonify

from app.live.models import (
    DEPLOYMENT_STABLE,
    Channel,
)


def init(api):
    @api.route("/channels-deployment-environments/v1", methods=["GET"])
    def deployment_environments():
        def media(channel):
            if channel.media:
                try:
                    return urlparse(channel.media.video).path.split("/")[1]
                except Exception:
                    pass

            return ""

        return jsonify(
            [
                {
                    "channelId": str(channel.id),
                    "channelName": channel.title,
                    "deploymentEnv": channel.deployment_environment,
                    "mainMedia": media(channel),
                }
                for channel in Channel.objects()
                if channel.deployment_environment not in (DEPLOYMENT_STABLE, "", None)
            ]
        )
