image: registry.gitlab.lfstrm.tv/server-side/cms/cmsci:v5

variables:
  GIT_SUBMODULE_STRATEGY: none
  PIP_CACHE_DIR: $CI_PROJECT_DIR/.cache/pip
  POETRY_CONFIG_DIR: $CI_PROJECT_DIR/.cache/poetry_config
  POETRY_DATA_DIR: $CI_PROJECT_DIR/.cache/poetry_data
  POETRY_CACHE_DIR: $CI_PROJECT_DIR/.cache/poetry_cache

workflow:
  auto_cancel:
    on_new_commit: interruptible

stages:
  - setup
  - test
  - build
  - deploy

cache: &cache
  key:
    files:
      - poetry.lock
  paths:
    - .venv
  policy: pull-push

.cms-job:
  tags:
    - ssdev

.build-job:
  tags:
    - docker_builder
  cache:
    <<: *cache
    policy: pull

setup:
  interruptible: true
  extends:
    - .cms-job
  stage: setup
  cache:
    <<: *cache
    policy: pull-push
  before_script:
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.lfstrm.tv".insteadOf "ssh://********************:2002"
  script:
    - python -m venv .venv
    - poetry install

lint:
  stage: test
  interruptible: true
  extends:
    - .cms-job
  cache:
    <<: *cache
    policy: pull
  #  allow_failure: true  # uncomment in case of dire need
  script:
    - make lint-flake8
    - make lint-black
    - make lint-isort

unit_tests:
  stage: test
  interruptible: true
  extends:
    - .cms-job
  services:
    - mongo:5.0
    - name: yandex/clickhouse-server:latest
      alias: clickhouse
  variables:
    CMS_MONGODB_URI: "mongodb://mongo"
    CMS_CLICKHOUSE_URL: "http://clickhouse:8123"
  cache:
    <<: *cache
    policy: pull
  before_script:
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.lfstrm.tv".insteadOf "ssh://********************:2002"
  script:
    - make _test
  coverage:
    /(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/
  artifacts:
    reports:
      junit: pytest_report.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml

release-notes:
  stage: test
  extends:
    - .cms-job
#  allow_failure: true  # uncomment in case of dire need
  interruptible: true
  before_script:
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.lfstrm.tv".insteadOf "ssh://********************:2002"
    # Fix TypeError: HEAD is a detached symbolic reference as it points to 'f73b7a7d241a2cb71d71371272bc5c54ae867da3'
    # Took from https://stackoverflow.com/questions/69267025/detached-head-in-gitlab-ci-pipeline-how-to-push-correctly
    - git switch $CI_COMMIT_REF_NAME
    - git fetch
    - git reset --hard $CI_COMMIT_SHA
  script:
    - make release-notes-validate
  except:
    - tags
    - develop
    - master
    - release/*

localization:
  stage: test
  extends:
    - .cms-job
#  allow_failure: true  # uncomment in case of dire need
  interruptible: true
  script:
    - make babel-test
  except:
    - tags
    - develop
    - master
    - release/*

build_feature_image:
  stage: build
  image: quay.io/buildah/stable:v1.31
  # Disable caching
  cache: []
  before_script:
    - CMS_VERSION="${CI_COMMIT_TAG} (${CI_COMMIT_SHA:0:8})"
    - echo "$CI_REGISTRY_PASSWORD" | buildah login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - dnf -y install openssh-clients
  script:
    - >
      buildah build
      --layers
      --label "org.opencontainers.image.title=${CI_PROJECT_TITLE}"
      --label "org.opencontainers.image.url=${CI_PROJECT_URL}"
      --label "org.opencontainers.image.created=${CI_JOB_STARTED_AT}"
      --label "org.opencontainers.image.revision=${CI_COMMIT_SHA}"
      --label "org.opencontainers.image.version=${CI_COMMIT_REF_NAME}"
      --tag "${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_SLUG}"
      --tag "${CI_REGISTRY_IMAGE}:latest"
      --build-arg CMS_VERSION="${CMS_VERSION}"
      --build-arg CI_JOB_TOKEN=$CI_JOB_TOKEN
      -f Dockerfile
    - buildah push $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG
    - buildah push $CI_REGISTRY_IMAGE:latest
  when: always
  interruptible: true
  only:
    - develop
    - master
  except:
    - tags

build_release_image:
  stage: build
  image: quay.io/buildah/stable:v1.31
  # Disable caching
  cache: []
  before_script:
    - CMS_VERSION="${CI_COMMIT_TAG} (${CI_COMMIT_SHA:0:8})"
    - echo "$CI_REGISTRY_PASSWORD" | buildah login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - dnf -y install openssh-clients
  script:
    - >
      buildah build
      --layers
      --label "org.opencontainers.image.title=${CI_PROJECT_TITLE}"
      --label "org.opencontainers.image.url=${CI_PROJECT_URL}"
      --label "org.opencontainers.image.created=${CI_JOB_STARTED_AT}"
      --label "org.opencontainers.image.revision=${CI_COMMIT_SHA}"
      --label "org.opencontainers.image.version=${CI_COMMIT_REF_NAME}"
      --tag "${CI_REGISTRY_IMAGE}:${CI_COMMIT_TAG}"
      --build-arg CMS_VERSION="${CMS_VERSION}"
      --build-arg CI_JOB_TOKEN=$CI_JOB_TOKEN
      -f Dockerfile
    - buildah push $CI_REGISTRY_IMAGE:${CI_COMMIT_TAG}
  interruptible: true
  only:
    - tags

.deploy-stand:
  extends:
    - .build-job
  variables:
    BUILD_REPO: ssh://********************:2002/server-side/cms/cms-build.git
    STAND_HOST: cms@b612-app4-devb
  script:
    - BUILD_DIR="/www/cms-stands/stands/${CI_COMMIT_REF_SLUG}/build"
    - |
      ssh $STAND_HOST <<EOT
      if [ ! -d ${BUILD_DIR} ]; then
        mkdir -p ${BUILD_DIR}
        git clone ${BUILD_REPO} ${BUILD_DIR}
      fi
      if [ ! -f ${BUILD_DIR}/extra_vars.json ]; then
        cd ${BUILD_DIR}
        ansible-playbook playbooks/init.yml --extra-vars='makefile_target_hosts=stand makefile_branch_or_tag=${CI_COMMIT_REF_NAME}'
        make update-build
        ansible-playbook playbooks/init.yml --extra-vars='makefile_target_hosts=stand makefile_branch_or_tag=${CI_COMMIT_REF_NAME}'
      fi
      make -C ${BUILD_DIR} update-build
      CI_JOB_URL=$CI_JOB_URL make -C ${BUILD_DIR} install configure link
      EOT

.deploy-docker-stand:
  extends:
    - .build-job
  variables:
    BUILD_REPO: ssh://********************:2002/server-side/cms/cms-build.git
    STAND_HOST: cms@b612-app4-devb
  script:
    - BUILD_DIR="/www/cms-stands/stands/${CI_COMMIT_REF_SLUG}/build"
    - |
      ssh $STAND_HOST <<EOT
      rm -rf ${BUILD_DIR}
      mkdir -p ${BUILD_DIR}
      git clone ${BUILD_REPO} ${BUILD_DIR}
      cd ${BUILD_DIR}
      ansible-playbook playbooks/configure_docker.yml --extra-vars='target_hosts=stand branch_or_tag=${CI_COMMIT_REF_SLUG}'
      docker login -u "${CI_REGISTRY_USER}" -p "${CI_REGISTRY_PASSWORD}" "${CI_REGISTRY}"
      cd /srv/cms/"${CI_COMMIT_REF_SLUG}"
      docker compose pull && docker compose up -d
      EOT

deploy-main-docker-stand:
  extends:
    - .deploy-docker-stand
  dependencies:
    - build_feature_image
  stage: deploy
  when: always
  allow_failure: true
  interruptible: true
  only:
    - develop
    - master

deploy-stand:
  extends:
    - .deploy-stand
  stage: setup
  when: manual
  allow_failure: true
  interruptible: true
  except:
    - develop
    - master

deploy-main-stand:
  extends:
    - .deploy-stand
  stage: setup
  when: always
  interruptible: true
  allow_failure: true
  only:
    - develop
    - master
