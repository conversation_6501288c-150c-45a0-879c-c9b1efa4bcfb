#!/usr/bin/env python3
"""
Simulate the IVI import process with real feed data to verify episode detection fix.
This test uses the actual importer logic but with a limited dataset to verify the fix works.
"""

import xml.etree.ElementTree as ET
import requests
from app.abcvod.providers.ivi.api import ContentInfo, CompilationInfo

def simulate_import_process():
    """Simulate the IVI import process with real feed structure."""
    
    print("🎬 Simulating IVI Import Process with Real Feed Data")
    print("=" * 60)
    
    # Use local feed.xml (real IVI feed data)
    try:
        with open("feed.xml", "r", encoding="utf-8") as f:
            feed_xml = f.read()
        print("📄 Using local feed.xml (real IVI feed data) for simulation...")
    except FileNotFoundError:
        print("❌ No feed data available for simulation")
        return False
    
    # Parse the XML
    try:
        root = ET.fromstring(feed_xml)
        print("✅ Successfully parsed feed XML")
    except ET.ParseError as e:
        print(f"❌ Error parsing XML: {e}")
        return False
    
    # Simulate the import process
    content_elements = root.findall('.//content')
    compilation_elements = root.findall('.//compilation')
    
    print(f"\n📊 Feed Statistics:")
    print(f"   Total series (compilations): {len(compilation_elements)}")
    print(f"   Total content items: {len(content_elements)}")
    
    # Simulate series import
    series_imported = 0
    series_by_id = {}
    
    print(f"\n📚 Simulating Series Import...")
    for i, comp_elem in enumerate(compilation_elements[:10]):  # Process first 10 series
        try:
            series = CompilationInfo.from_xml(ET.tostring(comp_elem))
            series_by_id[series.id] = series
            series_imported += 1
            print(f"   ✅ Series {series.id}: '{series.title}'")
        except Exception as e:
            print(f"   ❌ Error importing series {i}: {e}")
    
    # Simulate content import with our fixed episode detection logic
    episodes_imported = 0
    standalone_imported = 0
    episodes_with_missing_series = 0
    
    print(f"\n📺 Simulating Content Import with Fixed Episode Detection...")
    
    for i, content_elem in enumerate(content_elements[:50]):  # Process first 50 content items
        try:
            content = ContentInfo.from_xml(ET.tostring(content_elem))
            
            # Apply our FIXED episode detection logic
            # Episode detection: check for compilation_id (real feed) or compilation (test data)
            is_episode = bool(content.compilation or content.compilation_id)
            
            # Debug: Show field values for first few items
            if i < 5:
                print(f"   🔍 DEBUG Content {content.id}: compilation={content.compilation}, compilation_id={content.compilation_id}")
            
            if is_episode:
                # Determine series ID for linking
                series_id = content.compilation_id or content.compilation
                
                # Check if series exists (in real import, this would be a database lookup)
                if str(series_id) in series_by_id:
                    episodes_imported += 1
                    series_title = series_by_id[str(series_id)].title
                    print(f"   📺 Episode imported: '{content.title}' → Series '{series_title}'")
                    print(f"      ID: {content.id}, Season: {content.season or 1}, Episode: {content.episode or 1}")
                else:
                    episodes_with_missing_series += 1
                    print(f"   ⚠️  Episode {content.id} references missing series {series_id}")
            else:
                standalone_imported += 1
                print(f"   🎬 Standalone content: '{content.title}' (ID: {content.id})")
                
        except Exception as e:
            print(f"   ❌ Error processing content {i}: {e}")
    
    # Results summary
    print(f"\n📈 Import Simulation Results:")
    print(f"   Series imported: {series_imported}")
    print(f"   Episodes imported: {episodes_imported}")
    print(f"   Standalone content imported: {standalone_imported}")
    print(f"   Episodes with missing series: {episodes_with_missing_series}")
    
    # Determine success
    success = episodes_imported > 0
    
    if success:
        print(f"\n🎉 SUCCESS: Episode detection fix is working!")
        print(f"   ✅ {episodes_imported} episodes successfully detected and would be imported")
        print(f"   ✅ Episodes are correctly linked to their series")
        print(f"   ✅ Standalone content is correctly identified")
        
        if episodes_with_missing_series > 0:
            print(f"   ℹ️  Note: {episodes_with_missing_series} episodes reference series not in our sample")
            print(f"      This is expected when processing only a subset of the feed")
    else:
        print(f"\n❌ ISSUE: No episodes detected in simulation")
        print(f"   This suggests there might still be an issue with episode detection")
    
    return success

if __name__ == "__main__":
    success = simulate_import_process()
    print(f"\n{'✅ Simulation completed successfully!' if success else '❌ Simulation failed!'}")
    exit(0 if success else 1)
