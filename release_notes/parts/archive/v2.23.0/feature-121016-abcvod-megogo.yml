---
task_id: 121016

migrations:
  - name: 028__imdb_id
    description: Изменит тип данных в поля `imdb_id` с integer на string во всех базах провайдеров из ABCVOD

commands: __IGNORED__  # команда CLI для ручного импорта Мегого, предназначена для отладки, админам про неё знать не обязательно.

pyproject_toml:
  - name: pydantic-xml = "^2.17.1"
    type: new
    description: Необходимо для парсинга файлов от Megogo.

config: |
  Добавлены параметры для ABCVOD/Megogo:

  * `MEGOGO` - включатель Meg<PERSON>, по умолчанию выключен. Допустимые значения: True, False 
  * `MEGOGO_XML_URL` - ссылка на XML с каталогом мегого для импорта
  * `MEGOGO_MAX_WORKERS` - Количество потоков для загрузки картинок от Megogo, по умолчанию 5, лучше не менять. 
  * `MEGOGO_IMPORT_SCHEDULE` - расписание импорта, по аналогии с другими расписаниями.
  * `MEGOGO_ALLOWED_CONTRACTS` - список ID разрешенных контрактов, если требуется. Например: [1001, 1002, 1003]
  * `MEGOGO_ALLOWED_DISTRIBUTIONS` - список разрешенных моделей распространения, если требуется. Например: ["svod", "advod"]
