## CMS v2.23.0 release notes

### Правки в config

[\#121016](https://dev.tightvideo.com/issues/121016) - Добавлены параметры для ABCVOD/Megogo:

- `MEGOGO` - включа<PERSON>е<PERSON><PERSON>, по умолчанию выключен. Допустимые значения: True, False
- `MEGOGO_XML_URL` - ссылка на XML с каталогом мегого для импорта
- `MEGOGO_MAX_WORKERS` - Количество потоков для загрузки картинок от Megogo, по умолчанию 5, лучше не менять.
- `MEGOGO_IMPORT_SCHEDULE` - расписание импорта, по аналогии с другими расписаниями.
- `MEGOGO_ALLOWED_CONTRACTS` - список ID разрешенных контрактов, если требуется. Например: [1001, 1002, 1003]
- `MEGOGO_ALLOWED_DISTRIBUTIONS` - список разрешенных моделей распространения, если требуется. Например: ["svod", "advod"]

[\#122255](https://dev.tightvideo.com/issues/122255) - Новый параметр конфигурации:

`PROVIDER_REPORTS_SETTINGS_DISABLE_AGENT_FEE = False` — отключает использование агентского вознаграждения из настроек провайдера в отчетах  
[\#124491](https://dev.tightvideo.com/issues/124491) - Добавлены параметры для управления подписками:

- `SUBSCRIPTION_ALLOW_DISABLE_AUTORENEW` - список провайдеров, для которых возможна отмена автопродления подписки.
  По умолчанию - `["smartpay", "uztelecom"]`
- `SUBSCRIPTION_ALLOW_ENABLE_AUTORENEW` - список провайдеров, для которых возможно возобновление автопродления подписки.  
  По умолчанию: `["uztelecom"]`

### Изменения в зависимостях от сервисов

[\#124778](https://dev.tightvideo.com/issues/124778), [\#122152](https://dev.tightvideo.com/issues/122152) - требуют версии [promo](https://gitlab.lfstrm.tv/server-side/backend/promo) >= [2.3](https://dev.tightvideo.com/versions/2389) для поддержки новых возможностей.

### Изменения в зависимостях

[\#121016](https://dev.tightvideo.com/issues/121016) - **[NEW]** `pydantic-xml = "^2.17.1"`  
Необходимо для парсинга файлов от Megogo.

[\#122512](https://dev.tightvideo.com/issues/122512) - **[MODIFIED]** `grpcio = "^1.73.1"`  
Обновление grpcio до версии 1.73.1

[\#122512](https://dev.tightvideo.com/issues/122512) - **[MODIFIED]** `grpcio-tools = "^1.73.1"`  
Обновление grpcio-tools до версии 1.73.1

[\#122512](https://dev.tightvideo.com/issues/122512) - **[DELETED]** `bjoern="^3.2.1"`  
Удаление bjoern из зависимостей проекта, так как больше не используется.

### Миграции

[\#120718](https://dev.tightvideo.com/issues/120718) - `030__fix_abcvod_collection_references`  
Удалит возможный мусор из аз кинотеатров группы ABCVOD - возможны бbтые ссылки в поле collections в модели title

[\#121016](https://dev.tightvideo.com/issues/121016) - `028__imdb_id`  
Изменит тип данных в поля `imdb_id` с integer на string во всех базах провайдеров из ABCVOD

[\#122382](https://dev.tightvideo.com/issues/122382) - `031__epg_export_add_bounds_received_at`  
Добавляет поле bounds_received_at в ClickHouse таблицу epg_events для отслеживания времени получения точных границ EPG событий через API

Миграции выполняются командой `migrate all [--save]` или выборочно - `migrate xxx [--save]` где xxx - номер миграции или полное имя миграции.

### Прочее

[\#121332](https://dev.tightvideo.com/issues/121332) - Добавилась возможность настроить цвет CMS (только топ-бар) для вашего пользователя.
Это может быть полезно, если вы имеете доступ к нескольким CMS.
Как найти:

- Клик по имени пользователя в правом-верхнем углу
- Пункт Settings
- На форме поле "Cms Color"

Теперь вы можете для себя (и только для себя) покрасить PROD CMS в красный - это поможет не перепутать
CMS при переключении между вкладками и не сделать что-то там, где делать этого не стоило :)
