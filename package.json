{"name": "cms", "scripts": {"build": "webpack --progress --profile --colors", "build-prod": "NODE_ENV=production webpack --profile", "watch": "webpack-dev-server --hot --inline --progress --colors", "hot-watch": "HOT_RELOAD=1 webpack-dev-server --hot --inline --progress --colors"}, "dependencies": {"@babel/plugin-proposal-class-properties": "^7.16.5", "@babel/plugin-transform-runtime": "^7.16.5", "@swc/core": "^1.2.123", "ace-builds": "1.2.6", "babel-core": "6.25.0", "babel-loader": "^8.2.3", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-react-constant-elements": "^6.23.0", "babel-plugin-transform-react-inline-elements": "^6.22.0", "babel-plugin-transform-react-jsx": "6.24.1", "bootbox": "5.5.2", "bootstrap-datetimepicker.js": "^1.0.0", "bootstrap-duration-picker": "2.1.3", "bootstrap-multiselect": "^1.1.0", "canvas2svg": "^1.0.16", "chart.js": "^2.9.4", "chartjs-plugin-annotation": "0.5.7", "chartjs-plugin-zoom": "0.7.4", "copy-webpack-plugin": "^10.2.0", "croppie": "2.6.2", "esbuild": "^0.14.8", "html5sortable": "~0.6.3", "inputmask": "4.0.6", "jquery-sortable": "~0.9.13", "jsoneditor": "^9.5.9", "lodash": "^4.17.15", "moment": "^2.29.1", "moment-timezone": "^0.5.32", "node-fetch": "^3.1.0", "pnotify": "^5.2.0", "react": "<16.0.0", "react-addons-pure-render-mixin": "^15.6.2", "react-addons-update": "^15.6.3", "react-dom": "<16.0.0", "serialize-javascript": "^6.0.0", "terser": "3.14.1", "urijs": "^1.19.2", "vis": "4.21.0", "xlsx": "^0.17.4"}, "devDependencies": {"@babel/cli": "^7.16.0", "@babel/core": "^7.16.5", "@babel/preset-env": "^7.16.5", "@babel/preset-react": "^7.16.5", "expose-loader": "0.7.1", "react-hot-loader": "^1.2.8", "resolve-cwd": "^3.0.0", "terser-webpack-plugin": "^5.3.0", "webpack": "^5.65.0", "webpack-cli": "^4.9.1", "webpack-dev-middleware": "^5.3.0", "webpack-dev-server": "^4.7.1", "webpack-hot-middleware": "^2.25.1"}, "babel": {"presets": ["@babel/preset-env", "@babel/preset-react"]}}