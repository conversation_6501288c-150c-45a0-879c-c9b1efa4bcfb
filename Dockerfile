FROM python:3.10-bookworm AS env

RUN curl --silent --location https://deb.nodesource.com/setup_20.x | bash -
RUN curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | apt-key add --no-tty -
RUN echo "deb https://dl.yarnpkg.com/debian/ stable main" | tee /etc/apt/sources.list.d/yarn.list

RUN apt-get update -yq  && apt-get install -yq \
    git  \
    build-essential \
    openssh-client \
    ca-certificates \
    libev-dev \
    nodejs \
    yarn

#RUN node -v && yarn -v && npm -v

RUN echo "Host *\n\tStrictHostKeyChecking no" >> /etc/ssh/ssh_config

ADD ./ /app
WORKDIR /app

ARG CI_JOB_TOKEN
RUN if [ -n "$CI_JOB_TOKEN" ]; then \
        git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.lfstrm.tv".insteadOf "ssh://********************:2002"; \
    fi

ENV PATH="${PATH}:/root/.local/bin"
RUN /usr/local/bin/pip install poetry
RUN mkdir -p .venv
RUN --mount=type=ssh poetry config virtualenvs.in-project true && poetry install --without dev

# Crutch, because this code, for some unknown reason, does not run in container:
# https://github.com/lex009/flask-admin-lte/blob/7aa7b7bc222ca088ca5e2f67db57533948a96f82/setup.py#L20
RUN yarn --cwd /app/.venv/src/flask-admin-lte && \
    yarn && NODE_ENV=production PATH=node_modules/.bin:${PATH} webpack && \
    rm -r ./node_modules

# Compile translations. Copy of `make _babel-compile`
RUN .venv/bin/pybabel compile -d app/translations

FROM docker.io/nginx/unit:1.28.0-python3.10

RUN apt update -yq && apt-get -yq install libev-dev libxml2 uwsgi-plugin-python3 git

ARG CMS_VERSION
ARG PORT=5000

RUN mkdir /etc/cms /cms/ /cms/reports/ /cms/clickhouse_report_templates /cms/uploads /cms/archive-images /cms/vod_reports /tmp/cms/

ENV CMS_CONFIG=/etc/cms/config.cfg

COPY --from=env /app /app

ENV PATH="/app/.venv/bin:$PATH"

WORKDIR /app

ENV CMS_VERSION=${CMS_VERSION}
ENV LISTEN_PORT=${PORT}

RUN chmod u+x ./entrypoint.sh
ENTRYPOINT ["./entrypoint.sh"]

EXPOSE ${PORT}
