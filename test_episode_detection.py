#!/usr/bin/env python3
"""
Test script to validate episode detection logic with real IVI feed data.
This script tests our episode detection fix without requiring MongoDB.
"""

import xml.etree.ElementTree as ET
import requests
import sys
from dataclasses import dataclass
from typing import Optional

@dataclass
class ContentInfo:
    """Simple content info class for testing."""
    id: Optional[str] = None
    title: Optional[str] = None
    compilation: Optional[str] = None
    season: Optional[int] = None
    episode: Optional[int] = None

def analyze_real_feed_structure():
    """Deeply analyze the real IVI feed structure to understand episode/series relationships."""
    
    print("🔍 Deep Analysis of Real IVI Feed Structure")
    print("=" * 60)
    
    # Download a small sample of the real IVI feed
    try:
        print("📡 Downloading real IVI feed sample...")
        response = requests.get("https://api.ivi.ru/mobileapi/catalogue/v5/", timeout=30)
        if response.status_code != 200:
            print(f"❌ Failed to download IVI feed: HTTP {response.status_code}")
            return False
            
        # Parse the XML
        root = ET.fromstring(response.content)
        print(f"✅ Successfully downloaded and parsed IVI feed")
        
    except Exception as e:
        print(f"❌ Error downloading IVI feed: {e}")
        print("📄 Using local feed.xml for testing...")
        
        # Fallback to local feed.xml if available
        try:
            with open('feed.xml', 'r', encoding='utf-8') as f:
                content = f.read()
            root = ET.fromstring(content)
            print(f"✅ Successfully loaded local feed.xml")
        except Exception as e2:
            print(f"❌ Error loading local feed: {e2}")
            return False
    
    # Analyze content elements for episode detection
    content_elements = root.findall('.//content')
    compilation_elements = root.findall('.//compilation')
    
    print(f"\n📊 Feed Analysis:")
    print(f"   Total <content> elements: {len(content_elements)}")
    print(f"   Total <compilation> elements: {len(compilation_elements)}")
    
    # Deep structure analysis
    print(f"\n🔍 Deep Structure Analysis...")
    
    # Analyze compilation elements (series) structure
    print(f"\n📚 Series Structure Analysis:")
    series_by_id = {}
    for i, comp_elem in enumerate(compilation_elements[:10]):
        series_info = {}
        for child in comp_elem:
            if child.text and child.text.strip():
                series_info[child.tag] = child.text.strip()
        
        series_id = series_info.get('id', f'unknown_{i}')
        series_by_id[series_id] = series_info
        print(f"   Series {series_id}: {series_info.get('title', 'No title')}")
        print(f"      Fields: {list(series_info.keys())}")
    
    # Analyze content elements structure and look for potential episode indicators
    print(f"\n📺 Content Structure Analysis:")
    content_with_compilation = 0
    content_with_season_episode = 0
    potential_episodes = []
    
    # Look for various potential episode indicators
    episode_indicators = {
        'compilation': 0,
        'compilation_id': 0,
        'parent_id': 0,
        'series_id': 0,
        'season': 0,
        'episode': 0,
        'episode_number': 0,
        'season_number': 0
    }
    
    # Track episodes detected by our fixed logic
    episodes_detected_by_fixed_logic = 0
    
    for i, content_elem in enumerate(content_elements[:100]):  # Analyze more items
        try:
            # Extract all child elements to understand structure
            content_info = {}
            for child in content_elem:
                if child.text and child.text.strip():
                    content_info[child.tag] = child.text.strip()
            
            # Count episode indicators
            for indicator in episode_indicators:
                if indicator in content_info:
                    episode_indicators[indicator] += 1
            
            # Look for potential episodes (content with season/episode info)
            has_season = 'season' in content_info or 'season_number' in content_info
            has_episode = 'episode' in content_info or 'episode_number' in content_info
            
            if has_season or has_episode:
                potential_episodes.append({
                    'id': content_info.get('id', f'unknown_{i}'),
                    'title': content_info.get('title', 'No title'),
                    'season': content_info.get('season') or content_info.get('season_number'),
                    'episode': content_info.get('episode') or content_info.get('episode_number'),
                    'all_fields': list(content_info.keys())
                })
            
            # Test our FIXED episode detection logic
            # Episode detection: check for compilation_id (real feed) or compilation (test data)
            has_compilation = 'compilation' in content_info
            has_compilation_id = 'compilation_id' in content_info
            
            if has_compilation or has_compilation_id:
                episodes_detected_by_fixed_logic += 1
                
        except Exception as e:
            print(f"   ⚠️  Error parsing content {i}: {e}")
            continue
    
    # Report episode indicators found
    print(f"\n📊 Episode Indicators Found:")
    for indicator, count in episode_indicators.items():
        if count > 0:
            print(f"   {indicator}: {count} content items")
    
    # Show potential episodes
    print(f"\n📺 Potential Episodes Found: {len(potential_episodes)}")
    for ep in potential_episodes[:10]:  # Show first 10
        print(f"   ID {ep['id']}: '{ep['title']}'")
        print(f"      Season: {ep['season']}, Episode: {ep['episode']}")
        print(f"      All fields: {ep['all_fields']}")
        print()
    
    # Look for patterns in titles that might indicate episodes
    print(f"\n🔍 Title Pattern Analysis:")
    episode_title_patterns = []
    for i, content_elem in enumerate(content_elements[:200]):
        title_elem = content_elem.find('title')
        if title_elem is not None and title_elem.text:
            title = title_elem.text.strip()
            # Look for common episode patterns
            if any(pattern in title.lower() for pattern in ['серия', 'episode', 'эпизод', 'часть']):
                episode_title_patterns.append({
                    'id': content_elem.find('id').text if content_elem.find('id') is not None else f'unknown_{i}',
                    'title': title
                })
    
    print(f"   Found {len(episode_title_patterns)} titles with episode-like patterns:")
    for pattern in episode_title_patterns[:10]:
        print(f"      ID {pattern['id']}: '{pattern['title']}'")
    
    # Analysis Summary
    print(f"\n📈 Real IVI Feed Structure Summary:")
    print(f"   Total series (compilation elements): {len(compilation_elements)}")
    print(f"   Total content items: {len(content_elements)}")
    print(f"   Content with season/episode fields: {len(potential_episodes)}")
    print(f"   Content with episode-like titles: {len(episode_title_patterns)}")
    print(f"   Episodes detected by FIXED logic: {episodes_detected_by_fixed_logic}")
    
    # Determine if episodes exist and how they're structured
    has_episodes = len(potential_episodes) > 0 or len(episode_title_patterns) > 0
    has_series_linkage = any(count > 0 for indicator, count in episode_indicators.items() 
                           if indicator in ['compilation', 'compilation_id', 'parent_id', 'series_id'])
    
    print(f"\n🎯 Key Findings:")
    if episodes_detected_by_fixed_logic > 0:
        print(f"   🎉 SUCCESS: Fixed episode detection logic now works!")
        print(f"   ✅ {episodes_detected_by_fixed_logic} episodes detected using compilation_id linkage")
        print(f"   ✅ Series linkage mechanism: compilation_id → series id")
    elif has_episodes:
        print(f"   ✅ Episodes likely exist in the feed ({len(potential_episodes)} with season/episode fields)")
        if has_series_linkage:
            print(f"   ✅ Series linkage mechanism found")
        else:
            print(f"   ❌ No clear series linkage mechanism found")
            print(f"   📝 Episodes may be standalone or use a different linking method")
    else:
        print(f"   ❌ No clear episode structure found in the feed")
        print(f"   📝 This feed may only contain standalone content")
    
    print(f"\n💡 Status:")
    if episodes_detected_by_fixed_logic > 0:
        print(f"   🚀 Ready for production: Episode detection fix is working!")
        print(f"   📋 Next step: Run full import with real IVI feed to verify results")
    elif has_episodes and has_series_linkage:
        print(f"   • Use the identified linkage mechanism for episode import")
    elif has_episodes and not has_series_linkage:
        print(f"   • Import content with season/episode fields as episodes")
        print(f"   • Create seasons based on season numbers")
        print(f"   • Consider title-based series grouping if needed")
    else:
        print(f"   • Current behavior (importing as standalone titles) is correct")
    
    return episodes_detected_by_fixed_logic > 0

if __name__ == "__main__":
    success = analyze_real_feed_structure()
    print(f"\n{'✅ Analysis complete!' if success else '📝 Analysis complete - no episodes found.'}")
    sys.exit(0)
