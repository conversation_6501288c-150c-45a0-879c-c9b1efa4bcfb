import os
import warnings

from app import make_app

print("Loading app...")
# Here we mute all warnings, which are fired during app init.
# To run command without mute, set any value for "VERBOSE" environment variable. Example:
# VERBOSE=1 flask cli-command
if os.getenv("VERBOSE"):
    app = make_app(skip_add_views=True)
else:
    with warnings.catch_warnings(record=True) as w:
        print("Warnings are turned off during app init.")
        print("To turn warnings on, type your command like `VERBOSE=1 flask your flask command`")
        app = make_app(skip_add_views=True)
print("App loaded.")


from app.abcvod.cli import init_abcvod_cli  # noqa: E402
from app.activity.commands import activity_commands  # noqa: E402
from app.ads.commands import ads_commands  # noqa: E402
from app.app_settings.commands import app_settings_commands  # noqa: E402
from app.archive.commands import archive_commands  # noqa: E402
from app.audiences.commands import audiences_commands  # noqa: E402
from app.clickhouse.commands import clickhouse_commands  # noqa: E402
from app.cms.commands import (  # noqa: E402
    cms_commands,
    make_migrations_cli,
)
from app.cms.file_utils.commands import file_commands  # noqa: E402
from app.common.commands import common_commands  # noqa: E402
from app.live.commands import live_commands  # noqa: E402
from app.medias.commands import medias_commands  # noqa: E402
from app.purchases.commands import purchases_commands  # noqa: E402
from app.purchases.report.commands import provider_commands  # noqa: E402

init_abcvod_cli(app)
app.cli.add_command(ads_commands)
app.cli.add_command(archive_commands)
app.cli.add_command(live_commands)
app.cli.add_command(purchases_commands)
app.cli.add_command(activity_commands)
app.cli.add_command(medias_commands)
app.cli.add_command(clickhouse_commands)
app.cli.add_command(audiences_commands)
app.cli.add_command(provider_commands)
app.cli.add_command(file_commands)
app.cli.add_command(cms_commands)
app.cli.add_command(common_commands)
app.cli.add_command(app_settings_commands)
make_migrations_cli(app)
