from flask import request
from flask_babelex import (
    Babel,
    npgettext,
    pgettext,
)
from flask_security import current_user


def init(app):
    # This is used for locales setup for content not for CMS interface.
    # As list of available locales for content may differ from the list
    # of translations available for CMS interface.
    import cmf.core.form.fields

    cmf.core.form.fields.locales_list = app.config["LOCALES"]
    cmf.core.form.fields.locales = frozenset(dict(cmf.core.form.fields.locales_list))
    our_locales = tuple(locale for locale in cmf.core.form.fields.locales if locale != "default")

    app.extensions["babel"] = babel = Babel(app)

    @babel.localeselector
    def get_locale():
        return current_user.locale or request.accept_languages.best_match(our_locales)

    @babel.timezoneselector
    def get_timezone():
        return current_user.timezone

    app.jinja_env.globals.update(
        {
            "pgettext": pgettext,
            "npgettext": npgettext,
        }
    )
