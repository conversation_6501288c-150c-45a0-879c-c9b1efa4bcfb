import logging

from flask import (
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from flask_security import current_user
from prebilling.accounts import Account

from app.auth import expose
from app.proto.services import UpsaleService
from app.purchases.models import Provider
from app.seller.proto.seller import UpsaleRequestResponse

from .base import Component

logger = logging.getLogger(__name__)


class Upsale(Component):
    """Component for managing upsale requests."""

    extra_permissions = {"view_upsale_requests", "manage_upsale_requests"}

    template = "accounts/account_upsale_requests.html"
    request_details_template = "accounts/upsale_request_details.html"

    page_size = 10

    def __init__(self, prebilling_service=None, *args, **kwargs):
        self._prebilling_service = prebilling_service
        self.upsale_service = UpsaleService.get_instance()

        super().__init__(*args, **kwargs)

    @property
    def is_allowed(self):
        return current_user.is_allowed(self.endpoint, "manage_upsale_requests")

    def widget_args(self, account: Account) -> dict:
        try:
            page = request.args.get("upsale_page", 0, int)
            skip = page * self.page_size
            limit = self.page_size + 1  # Request +1 record from the next page, so we know if there is next page :)

            response = self.upsale_service.get_requests(account_id=account.id, skip=skip, limit=limit)
            data = response.requests or []
            more = len(data) > self.page_size
            requests = data[:-1] if more else data
        except Exception as e:
            logger.error(f"Seller service error: {str(e)}")
            return dict(account=account, service_error=True)

        return dict(
            requests=requests,
            account=account,
            upsale_page=page,
            upsale_more=more,
            extra_info=self.extra,
        )

    def extra(self, request_details: UpsaleRequestResponse):
        data = {}

        if request_details.provider_id:
            data["provider"] = Provider.objects(id=request_details.provider_id).first()

            offers = self._prebilling_service.offers.list(request_details.provider_id)
            offers_map = {o.id: o for o in offers}

            if request_details.offer_id:
                data["offer"] = offers_map.get(request_details.offer_id)

            if request_details.trial and request_details.trial.offer_id:
                data["trial"] = offers_map.get(request_details.trial.offer_id)

        return data

    @expose("/details", perm="manage_upsale_requests", methods=("GET",))
    def request_details(self):
        account = self.account_or_404()
        request_id = request.values.get("r_id")

        try:
            request_details: UpsaleRequestResponse = self.upsale_service.get_request(id=request_id)
            return self.render(
                self.request_details_template,
                account=account,
                request_details=request_details,
                **self.extra(request_details),
            )
        except Exception as e:
            flash("{}: {}".format(_("Service error"), e), "error")
            return redirect(request.args.get("url") or self.get_url(".info", id=account.id))

    @expose("/retry", perm="manage_upsale_requests", methods=("GET",))
    def retry_request(self):
        account = self.account_or_404()
        request_id = request.values.get("r_id")

        try:
            self.upsale_service.retry_request(id=request_id)
        except Exception as e:
            flash("{}: {}".format(_("Error sending request"), e), "error")
        else:
            flash(_("Retry request has been sent"), "success")

        return redirect(
            request.args.get("url") or self.get_url(".upsale_request_details", id=account.id, r_id=request_id)
        )

    @expose("/archive", perm="manage_upsale_requests", methods=("GET",))
    def archive_request(self):
        account = self.account_or_404()
        request_id = request.values.get("r_id")

        try:
            self.upsale_service.archive_request(id=request_id)
        except Exception as e:
            flash("{}: {}".format(_("Error archiving request"), e), "error")
        else:
            flash(_("The request was successfully archived"), "success")

        return redirect(request.args.get("url") or self.get_url(".info", id=account.id))
