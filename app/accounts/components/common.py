from datetime import datetime
from typing import Callable

import requests
from flask import (
    abort,
    request,
)
from flask_security import current_user
from prebilling.accounts import Account
from prebilling.rest.offers import Offer
from prebilling.rest.providers import PROVIDER_STATE_APPROVED

from app.purchases.models import Provider


def password_url(base_url, account: Account, code: str) -> str:
    """Helper for building password restoring url."""
    return "{}/restore-password?id={}&accountID={}&code={}&new=new".format(
        base_url,
        str(account.users[0].id),
        str(account.id),
        code,
    )


class CommonMixin(object):
    """Mixin for adding set of helper functions and services to view or component."""

    def account_or_404(self) -> Account:
        """Extracts account's id from request, queries and returns account object.

        Raise 404 error if account doesn't exist.
        """
        id = request.args.get("id")

        if not id:
            abort(404)

        try:
            account = self._prebilling_service.accounts.get(id)
        except requests.exceptions.HTTPError as err:
            if "404" in str(err):
                abort(404)

        if not account:
            abort(404)

        if self.provider and account.provider["id"] != self.provider.id:
            abort(404)

        return account

    @staticmethod
    def prepare_provider(p: dict) -> dict:
        """Extracts providers's data."""
        if not p:
            return {}

        provider_id = p["id"]
        provider = Provider.objects.only("name", "subdomain", "region").filter(id=provider_id).first()

        if not provider:
            return {}

        return dict(
            provider_id=provider_id,
            provider_name=provider.name,
            provider_region=provider.region,
            provider_subdomain=provider.subdomain,
        )

    @staticmethod
    def purchase_sort_key(offers_map: dict) -> Callable:
        """Helper for sorting subscriptions by creation date and offer name."""
        return (
            lambda s: (s.created, offers_map[s.offer_id].name)
            if offers_map.get(s.offer_id)
            else (datetime.utcnow(), "")
        )

    def cancelling_allowed(self, offer: Offer) -> bool:
        """Helper checking if it is possible to cancel the subscription."""
        if offer.typ.value == 3 and not current_user.is_allowed(self.endpoint, "disable_trials"):
            return False

        return (
            current_user.is_allowed(self.endpoint, "disable_subscription")
            and not self._disable_cancel_subscription
            and not self._view_only
        )

    def format_offer(self, offer):
        if offer:
            if getattr(self, "_cms", None):
                return f"{ offer.name } ({ offer._name })"

            return f"{ offer.name }"

        return "n/a"


def get_account_type(account, provider):
    account_type = "IPTV Candidate"

    if provider:
        if provider.is_ott or account.license == "OTT":
            account_type = "OTT"
        elif (account.provider or {}).get("state") == PROVIDER_STATE_APPROVED:
            account_type = "IPTV"

    return account_type
