from flask import (
    abort,
    flash,
    redirect,
    request,
)
from flask_admin.helpers import get_redirect_target
from flask_babelex import lazy_gettext as _
from flask_security import current_user
from prebilling.accounts import Account

from app.accounts.activity.events import BaseAccountActionEvent
from app.auth import expose
from app.yakassa.service import YakassaService

from .base import Component


class Yakassa(Component):
    """Yakassa component for Yakassa integration.

    It is an interface for rendering and managing Yakassa subscriptions for an account.
    Actual integration with Yandex.Kassa is done through
    `yakassa <https://dev.tightvideo.com/projects/server-side/wiki/Yakassa-project>`_
    backend services.

    Required permission to use the component is `yakassa`.
    """

    extra_permissions = {"yakassa_archive_subscription"}

    payments_page_size = 5  # Page size of payments list in subscriptions details
    archived_subscriptions_page_size = 20

    # Templates configuration
    template = "accounts/account_yakassa.html"
    payment_method_template = "accounts/yakassa_payment_method.html"
    archived_subscriptions_template = "accounts/account_yakassa_archived.html"
    refund_history_template = "accounts/account_refund_history.html"
    subscription_details_template = "accounts/yakassa_subscription_details.html"
    payment_template = "accounts/yakassa_payment_details.html"

    def __init__(
        self, prebilling_service=None, yakassa_service: YakassaService = None, view_only=False, *args, **kwargs
    ):

        self._prebilling_service = prebilling_service
        self._yakassa_service = yakassa_service
        self._view_only = view_only

        super().__init__(*args, **kwargs)

    @property
    def is_allowed(self):
        return current_user.is_allowed(self.endpoint, "yakassa")

    def widget_args(self, account: Account) -> dict:
        """Returns agrs for render a widget on account's page.

        It contains current payment method and active subscriptions.
        """
        if current_user.is_allowed(self.endpoint, "yakassa"):
            payment_method = self._yakassa_service.payment_method(account.id)

            offers = self._prebilling_service.offers.list(account.provider["id"])
            offers_map = {o.id: o for o in offers}

            return dict(
                payment_method=payment_method,
                subscriptions=self._yakassa_service.subscriptions(account.id) or [],
                offers_map=offers_map,
                account=account,
            )

        return dict(account=account)

    @expose("/yakassa/payment-method-view", perm="yakassa", methods=("GET",))
    def payment_method_view(self):
        """Handler for saved payment method details view."""
        return_url = get_redirect_target()
        account = self.account_or_404()

        pm = self._yakassa_service.payment_method(account.id)

        if not pm:
            abort(404)

        return self.render(self.payment_method_template, payment_method=pm, return_url=return_url, account=account)

    @expose("/yakassa/payment-method-remove", perm="yakassa_payment_method_remove", methods=("GET",))
    def payment_method_remove(self):
        """Handler that removes payment method for an account."""
        account = self.account_or_404()

        if self._yakassa_service.remove_payment_method(account.id) is None:
            flash(_("Error deleting payment method"), "error")
        else:
            self.set_user_action_event(
                BaseAccountActionEvent(account.id, description="Yakassa payment method is deleted")
            )
            flash(_("Payment method deleted successfully"), "success")

        return redirect(self.get_url(".info", id=account.id))

    @expose("/yakassa/archived-subscriptions", perm="yakassa", methods=("GET",))
    def archived_subscriptions(self):
        """Handler for list of archived subscriptions."""
        account = self.account_or_404()

        offers = self._prebilling_service.offers.list(account.provider["id"])
        offers_map = {o.id: o for o in offers}

        page = request.args.get("yakassa_page", 0, int)
        page_size = self.archived_subscriptions_page_size
        frm = page * page_size

        data = self._yakassa_service.archived_subscriptions(account.id, skip=frm, limit=page_size + 1) or []

        more = len(data) > page_size
        subscriptions = data[:-1] if more else data

        return self.render(
            self.archived_subscriptions_template,
            subscriptions=subscriptions,
            offers_map=offers_map,
            yakassa_page=page,
            yakassa_frm=frm,
            yakassa_more=more,
            archived=True,
            account=account,
        )

    @expose("/yakassa/refund-history", perm="yakassa", methods=("GET",))
    def refund_history(self):
        account = self.account_or_404()

        refund_history_items = self._yakassa_service.refund_history(account.id)

        return self.render(
            self.refund_history_template,
            refund_history_items=refund_history_items,
            account=account,
        )

    @expose("/yakassa/subscription-details", perm="yakassa", methods=("GET",))
    def subscription_details(self):
        """Handler for subscription's details.

        The page contains a particular subscription's details as well as a list
        of payments made for this subscripion.
        """
        account = self.account_or_404()

        subscription_id = request.args.get("s_id")
        provider_id = account.provider["id"]

        offers = self._prebilling_service.offers.list(provider_id)
        offers_map = {o.id: o for o in offers}

        subscription = self._yakassa_service.subscription(account.id, subscription_id)

        if not subscription:
            abort(404)

        page = request.args.get("page", 0, int)
        page_size = self.payments_page_size
        frm = page * page_size

        [payment] = self._yakassa_service.payments(account.id, subscription_id, skip=0, limit=1) or [None]

        data = self._yakassa_service.payments(account.id, subscription_id, skip=frm, limit=page_size + 1) or []

        more = len(data) > page_size
        payments = data[:-1] if more else data

        return self.render(
            self.subscription_details_template,
            view_only=self._view_only,
            subscription=subscription,
            payment=payment,
            payments=payments,
            offers_map=offers_map,
            page=page,
            frm=frm,
            more=more,
            account=account,
        )

    @expose("/yakassa/finish-subscription", perm="yakassa_finish_subscriptions", methods=("GET",))
    def finish_subscription(self):
        """Handler to finish subscription.

        Difference between "cancel" and "finish" is: cancelled subscription is active until it ends,
        but "finish" makes subscription inactive immediately.

        It expects subscription's id on query parameter `s_id`.
        """
        account = self.account_or_404()

        subscription_id = request.args.get("s_id")

        if self._yakassa_service.finish_subscription(subscription_id) is None:
            flash(_("Subscription termination error"), "error")
        else:
            self.set_user_action_event(
                BaseAccountActionEvent(account.id, description=f"Yakassa subscription {subscription_id} is finished")
            )
            flash(_("Subscription terminated"), "success")

        return redirect(self.get_url(".info", id=account.id))

    @expose("/yakassa/refund-subscription", perm="yakassa_refund_subscriptions", methods=("GET",))
    def refund_subscription(self):
        """Ask refund for subscription."""
        account = self.account_or_404()
        subscription_id = request.args.get("s_id")
        initiator = current_user.login

        if self._yakassa_service.refund_subscription(subscription_id, initiator) is None:
            flash(_("Subscription refund error"), "error")
        else:
            flash(_("The refund was successful"), "success")

        return redirect(self.get_url(".info", id=account.id))

    @expose("/yakassa/cancel-subscription", perm="yakassa_manage_subscriptions", methods=("GET",))
    def cancel_subscription(self):
        """Handler to cancel a subscriptions.

        It expects subscuption's id on query parameter ``s_id``
        """
        account = self.account_or_404()

        subscription_id = request.args.get("s_id")

        if self._yakassa_service.cancel_subscription(account.id, subscription_id) is None:
            flash(_("Error cancelling subscription"), "error")
        else:
            self.set_user_action_event(
                BaseAccountActionEvent(
                    account.id, description=f"Yakassa subscription {subscription_id} subscription is cancelled"
                )
            )
            flash(_("Subscription cancelled"), "success")

        return redirect(self.get_url(".info", id=account.id))

    @expose(
        "/yakassa/activate-subscription",
        perm="yakassa_manage_subscriptions",
        methods=("GET",),
    )
    def activate_subscription(self):
        """Handler that activates auto prolong for a subscription.

        It expects subscription's id on query parameter ``s_id``
        """
        account = self.account_or_404()

        subscription_id = request.args.get("s_id")

        if self._yakassa_service.activate_subscription(account.id, subscription_id) is None:
            flash(_("Subscription activation error"), "error")
        else:
            self.set_user_action_event(
                BaseAccountActionEvent(account.id, description=f"Yakassa subscription {subscription_id} is activated")
            )
            flash(_("Subscription activated"), "success")

        return redirect(self.get_url(".info", id=account.id))

    @expose("/yakassa/payment", perm="yakassa", methods=("GET",))
    def payment(self):
        """Handler for a payment's details.

        It expects id of the payment in ``payment_id`` query parameter, and
        subscription's id in ``subscription_id`` query paramenter.
        """
        account = self.account_or_404()

        subscription_id = request.args.get("subscription_id")
        payment_id = request.args.get("payment_id")

        payment = self._yakassa_service.payment(account.id, payment_id)
        payment_info = self._yakassa_service.payment_info(account.id, subscription_id, payment_id)

        if not payment:
            abort(404)

        return self.render(
            self.payment_template,
            subscription_id=subscription_id,
            payment=payment,
            payment_info=payment_info,
            account=account,
        )

    @expose("/yakassa/sync-payment", perm="yakassa", methods=("GET",))
    def sync_payment(self):
        """Handler for syncing payment.

        It makes request to yakassa service to sync payments between Yakassa and local database.

        It expects subscription id and payment id in ``s_id`` and ``p_id`` query parameters.
        """
        account = self.account_or_404()

        subscription_id = request.args.get("s_id")
        payment_id = request.args.get("p_id")

        if self._yakassa_service.sync_payment(account.id, subscription_id, payment_id) is None:
            flash(_("Payment synchronization error"), "error")
        else:
            self.set_user_action_event(
                BaseAccountActionEvent(
                    account.id,
                    description=f"Yakassa payment {payment_id} for subscription\
                        {subscription_id} subscription is synchronized",
                )
            )
            flash(_("Payment synchronized"), "success")

        return redirect(
            self.get_url(self.endpoint + ".yakassa_subscription_details", id=account.id, s_id=subscription_id)
        )

    @expose(
        "/yakassa/archive-subscription",
        perm="yakassa_manage_subscriptions",
        methods=("GET",),
    )
    def archive_subscription(self):
        """Handler that archives auto prolong for a subscription.

        It expects subscription's id on query parameter ``s_id``
        """
        if not current_user.is_allowed(self.endpoint, "yakassa_archive_subscription"):
            abort(403)

        account = self.account_or_404()
        subscription_id = request.args.get("s_id")

        if subscription_id is None:
            abort(404)

        if self._yakassa_service.archive_subscription(subscription_id, current_user.login) is None:
            flash(_("Subscription archiving error"), "error")
        else:
            self.set_user_action_event(
                BaseAccountActionEvent(account.id, description=f"Yakassa subscription {subscription_id} is archived")
            )
            flash(_("Subscription archived"), "success")

        return redirect(self.get_url(".info", id=account.id))
