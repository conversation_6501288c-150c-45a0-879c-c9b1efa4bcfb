from typing import Generator

from flask import (
    Blueprint,
    request,
)
from markupsafe import Markup
from prebilling.accounts import Account

from app.accounts.activity.events import BaseAccountActionEvent
from app.activity.service import UserActivityService
from app.common.views import ProviderMixin
from cmf.core.view import BasicView

from .common import CommonMixin


def noattr(obj, attr_name):
    def fn(*args, **kwargs):
        raise AttributeError(f"'{obj.__class__.__name__}' attribute '{attr_name}'")

    return fn


class Component(CommonMixin, ProviderMixin, BasicView):
    """Base class for account's page components.

    A component has a widget: a block that is rendered on
    the account's page.

    Components may define handlers as regular
    flask admin views.
    """

    is_allowed = True
    skip_account = False  # Skip requesting account

    method_name_prefix = None
    template = None  # Right column widget template
    account_template = None  # Left column widget template

    view_only_excluded = set()

    @property
    def url_rules(self):
        """Returns url rules for blueprint."""
        rules = []
        for url, name, methods in self._urls:
            rules.append(
                {
                    "url": url,
                    "endpoint": self._create_method_name(name),
                    "view_func": getattr(self, name),
                    "methods": methods,
                }
            )

        return rules

    def __init__(self, *args, method_name_prefix=None, default_view="info", view_only=False, **kwargs):
        """Creates a new component.

        :param method_name_prefix:
            Prefix to add to handlers name to avoid conflicts.
            For example if a method has a name `get_list` it will be
            registered on a blueprint as `prefils_get_list`.
            If the param is None or not specified, lowered class name
            will be used.
        :param default_view: Default view name.
        """
        self.method_name_prefix = method_name_prefix
        self._default_view = default_view

        if view_only:
            for attr in self.view_only_excluded:
                setattr(self, attr, noattr(self, attr))

        super().__init__()

    def widget_args(self, account: Account) -> dict:
        """Should provide arguments for rendering a widget on account's main page.

        Returned dict is used as templates args in `render_widget`
        method.
        """
        return dict(account=account)

    def render_widget(self, account: Account) -> str:
        """Renders component's widget on AccountView index page.

        Components should override `widget_args` method to pass additional data
        to a template of the widget.
        """
        if not self.is_allowed:
            return ""

        return Markup(
            super().render(self.template or self.account_template, endpoint=self.endpoint, **self.widget_args(account))
        )

    def _create_method_name(self, name: str) -> str:
        if self.method_name_prefix == "":
            return name

        prefix = self.method_name_prefix if self.method_name_prefix else self.__class__.__name__.lower()
        return f"{prefix}_{name}"

    def register_on_blueprint(self, blueprint: Blueprint) -> None:
        """Registers a component on the given blueprint.

        It adds to each component's handler name a lowercased class name or
        `method_name_prefix` if it's set to let different components have
        not unique method names.
        """
        for url, name, methods in self._urls:
            blueprint.add_url_rule(
                url,
                self._create_method_name(name),
                getattr(self, name),
                methods=methods,
            )

    def render(self, template: str, account=None, **kwargs) -> str:
        """Adds account and provider to the widget and every related page."""
        if not self.skip_account and account:
            kwargs.setdefault("provider", self.prepare_provider(account.provider))

        return super().render(template, endpoint=self.endpoint, account=account, **kwargs)

    def set_user_action_event(self, event: BaseAccountActionEvent):
        user_action_service: UserActivityService = getattr(self.admin.app, "user_activity", None)
        if not user_action_service:
            return

        user_action_service.set_user_action_event(event)

    def make_user_action_event(self, response):
        if request.method == "POST":
            return BaseAccountActionEvent(request.values.get("id", ""))


class ComponentHostMixin(BasicView):
    """Mixin for view with 'components' views."""

    user_action_event_makers: dict

    def __init__(self, *args, **kwargs):
        self.user_action_event_makers = {}
        self.build_components()
        super().__init__(*args, **kwargs)

    def build_components(self) -> Generator[Component, None, None]:
        """Special place for components instantiation.

        Must yield every component.
        """
        pass

    def register_component(self, component: Component):
        """Register component on the view's blueprint.

        Adds component's exposed endpoints to the view's blueprint.
        """
        for rule in component.url_rules:
            self.user_action_event_makers[rule["endpoint"]] = component.make_user_action_event

        component.component_endpoint = component.endpoint
        component.endpoint = component.parent_view = self.endpoint
        component.category = self.category
        component.admin = self.admin
        component.name = self.name

        self.extra_permissions |= component.extra_permissions

        component.register_on_blueprint(self.blueprint)

    def create_blueprint(self, admin) -> Blueprint:
        self.blueprint = super().create_blueprint(admin)

        components = self.build_components()

        for component in components:
            self.register_component(component)
        return self.blueprint

    def make_user_action_event(self, response):
        if self.endpoint in self.user_action_event_makers:
            # FIXME: This line is obvious mistake and this code has never worked.
            #        I intentionally left it here for review and future correction or deletion.
            return self.user_action_event_makers(self.endpoint)(response)

        if request.method == "POST":
            return BaseAccountActionEvent(
                request.args.get("id", ""),
                description=f"{self.endpoint} {request.values.get('id', '')}",
            )
