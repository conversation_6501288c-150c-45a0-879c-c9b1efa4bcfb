from flask_security import current_user
from prebilling.accounts import Account

from .base import Component


class UTMInfo(Component):
    """Component for representing UTM info of an account."""

    extra_permissions = {"utm_info"}

    account_template = "accounts/account_utm_info.html"

    def __init__(self, prebilling_service=None, *args, **kwargs):
        self._prebilling_service = prebilling_service
        super().__init__(*args, **kwargs)

    @property
    def is_allowed(self):
        return current_user.is_allowed(self.endpoint, "utm_info")

    def widget_args(self, account: Account) -> dict:
        return dict(account=account)
