from flask import (
    abort,
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from flask_security import current_user
from prebilling.accounts import Account

from app.accounts.activity.events import BaseAccountActionEvent
from app.auth import expose
from app.purchases.models import Provider

from .base import Component
from .forms import create_provider_info_form


class Extra(Component):
    """Component for managing provider info of an account."""

    extra_permissions = {"manage_provider_info"}

    account_template = "accounts/account_extra.html"
    provider_info_template = "accounts/provider_info.html"

    def __init__(self, prebilling_service=None, *args, **kwargs):
        self._prebilling_service = prebilling_service
        super().__init__(*args, **kwargs)

    @property
    def is_allowed(self) -> bool:
        return not request.path.endswith("/provider_info") and current_user.is_allowed(self.endpoint, "provider_info")

    @staticmethod
    def provider_info_fields(account: Account) -> dict:
        provider = Provider.objects.get(id=account.provider["id"])
        return {v["name"]: v["title"] for v in provider.user_form} if provider.user_form else {}

    def widget_args(self, account: Account) -> dict:
        return dict(provider_info_fields=self.provider_info_fields, account=account)

    @expose("/provider_info", perm="provider_info", methods=("GET", "POST"))
    def provider_info(self):
        if not current_user.is_allowed(self.endpoint, "manage_provider_info"):
            abort(403)

        account = self.account_or_404()

        provider_id = account.provider["id"]
        provider = Provider.objects.get(id=provider_id)

        info = account.provider_info or {}
        form_cls = create_provider_info_form(provider)

        if request.method == "POST":
            try:
                form = form_cls(request.form)

                if form.validate():
                    if self._prebilling_service.accounts.update_provider_info(account, form.data):
                        self.set_user_action_event(
                            BaseAccountActionEvent(account.id, description="Provider info is updated")
                        )

                        flash(_("Provider information saved"), "success")  # Provider info is updated
                    else:
                        flash(_("Error saving provider information"), "error")  # Error editing provider info

            except Exception as e:
                flash("Error editing provider info: {}".format(e), "error")

            return redirect(self.get_url(".info", id=account.id))

        return self.render(self.provider_info_template, form=form_cls(**info), account=account)
