import string
import uuid

from flask import (
    abort,
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from jinja2 import Template
from prebilling.accounts import Account
from prebilling.misc import (
    encrypt_password,
    generate_password,
)
from prebilling.providers import EMPTY_PROVIDER
from prebilling.rest.accounts import (
    UpdateAccountRequest,
    UpdateUserRequest,
)
from prebilling.rest.providers import PROVIDER_STATE_APPROVED
from prebilling.rest.purchases import PurchaseRequest

from app.accounts.activity.events import BaseAccountActionEvent
from app.auth import expose
from app.purchases.models import Provider
from notifications import notify

from .base import Component
from .common import (
    get_account_type,
    password_url,
)
from .forms import (
    ContactsForm,
    create_phone_form,
)


class AccountInfo(Component):

    extra_permissions = {"view_updated"}

    info_template = "accounts/info.html"

    def __init__(self, prebilling_service=None, *args, **kwargs):
        self._prebilling_service = prebilling_service
        super().__init__(*args, **kwargs)

    @expose("/info", perm="info", methods=("GET",))
    def info(self):
        """Handler for individual account.

        It expects account's id on query parameter ``id``.
        """
        account = self.account_or_404()

        return self.render(self.info_template, account=account)

    @expose("/info", perm="info", methods=("GET",))
    def edit_view(self):
        return self.info()


class Account(Component):
    """Base account's info and operations component.

    It allows to manage password phone, contact details,
    and also to delete or unlink account.
    """

    account_template = "accounts/account_info.html"
    phone_template = "accounts/account_phone.html"
    set_phone_template = "accounts/account_set_phone.html"
    contacts_template = "accounts/contacts.html"
    reset_password_template = "accounts/reset_password.html"
    remove_template = "accounts/account_delete.html"
    pin_template = "accounts/show_pin.html"

    def __init__(
        self,
        prebilling_service=None,
        disable_user_deletion=False,
        hide_provider=False,
        view_only=False,
        web_url="",
        notification_email="",
        phone_regexp="",
        password_length=6,
        password_alphabet=string.ascii_letters + string.digits,
        cms=False,
        *args,
        **kwargs,
    ):

        self._prebilling_service = prebilling_service
        self._disable_user_deletion = disable_user_deletion
        self._hide_provider = hide_provider
        self._view_only = view_only

        self._web_url = web_url
        self._notification_email = notification_email

        self._phone_regexp = phone_regexp
        self._password_length = password_length
        self._password_alphabet = password_alphabet
        self._cms = cms

        super().__init__(*args, **kwargs)

    def widget_args(self, account: Account) -> dict:
        """Should provide arguments for rendering a widget on account's main page.

        Returned dict is used as templates args in `render_widget` method.
        """
        not account and abort(404)

        provider = None
        if account.provider:
            provider = Provider.objects.get(id=account.provider.get("id"))

        return dict(
            view_only=self._view_only,
            hide_provider=self._hide_provider,
            disable_user_deletion=self._disable_user_deletion,
            provider=provider,
            account=account,
            account_type=get_account_type(account, provider),
            cms=self._cms,
        )

    @expose("/phone-manage", perm="phone_manage", methods=("GET", "POST"))
    def phone_manage(self):
        self._view_only and abort(403)
        account = self.account_or_404()

        form = create_phone_form(account.is_phone_verified, self._phone_regexp)(
            request.form,
            data={"phone": account.phone},
        )

        if request.method == "POST" and form.validate():
            try:
                if account.is_phone_verified:
                    flash(_("Verified phone number cannot be changed"), "error")
                else:
                    req = UpdateAccountRequest()
                    form.populate_obj(req)

                    if self._prebilling_service.accounts.update_account(account.id, req):
                        self.set_user_action_event(
                            BaseAccountActionEvent(account.id, description=f"Phone changed to {form.phone.data}")
                        )
                        flash(_("Phone number has been saved"), "success")
                    else:
                        flash(_("Error saving phone number"), "error")

                return redirect(self.get_url(".account_phone_manage", id=account.id))
            except Exception as e:
                flash("{}: {}".format(_("Error saving phone number"), e), "error")

        return self.render(self.phone_template, form=form, account=account)

    @expose("/set-verified-phone", perm="set_verified_phone", methods=("GET", "POST"))
    def set_verified_phone(self):
        self._view_only and abort(403)
        account = self.account_or_404()

        form = create_phone_form(regexp=self._phone_regexp)(request.form)

        if request.method == "POST" and form.validate():
            try:
                provider = Provider.objects.get(id=account.provider["id"])

                req = UpdateAccountRequest()
                form.populate_obj(req)

                password = generate_password(
                    provider.password_length or self._password_length,
                    provider.password_alphabet or self._password_alphabet,
                )

                req.password, req.salt = encrypt_password(password)
                req.is_phone_verified = True

                account, error = self._prebilling_service.accounts.update_account(account.id, req)

                if error:
                    flash("{}: {}".format(_("Error setting phone number"), error), "error")
                else:
                    self.set_user_action_event(
                        BaseAccountActionEvent(
                            account.id, description=f"Phone set to {form.phone.data} and verified, password is reset"
                        )
                    )
                    flash(_("Phone number has been set"), "success")

                message = Template(provider.reset_password_text).render(
                    username=account.provider_login or account.login, password=password
                )

                notify(account, provider.reset_password_subject, message, notifier=provider.notifier)

                return redirect(self.get_url(".account_set_verified_phone", id=account.id))
            except Exception as e:
                flash("{}: {}".format(_("Error setting phone number"), e), "error")
        elif request.method == "GET":
            form.phone.data = account.phone
        return self.render(self.set_phone_template, form=form, account=account)

    @expose("/phone-verification-toggle", perm="phone_verification_toggle", methods=("GET",))
    def phone_verification_toggle(self):
        self._view_only and abort(403)
        account = self.account_or_404()

        if account.phone:
            try:
                is_verified = not account.is_phone_verified
                self._prebilling_service.accounts.set_phone_verification(account.id, is_verified)
                self.set_user_action_event(
                    BaseAccountActionEvent(
                        account.id, description=f"Phone set to be {'' if is_verified else 'not'}verified"
                    )
                )
                if is_verified:
                    flash(_("Phone number status has been changed to 'verified'"), "success")
                else:
                    flash(_("Phone number status has been changed to 'not verified'"), "success")

            except Exception:
                flash(_("Error changing phone number status"), "error")
        else:
            flash(_("Phone number not specified"), "error")

        return redirect(self.get_url(".account_phone_manage", id=account.id))

    @expose("/phone-remove", perm="phone_remove", methods=("GET",))
    def phone_remove(self):
        self._view_only and abort(403)
        account = self.account_or_404()

        if account.phone:
            try:
                resp = self._prebilling_service.accounts.detach_phone(account.id, False)
                if resp and "error" in resp:
                    flash(
                        "{}: {}".format(_("Failed to delete number"), resp["error"]),
                        _("make sure that login does not match phone number"),
                        "error",
                    )
                else:
                    self.set_user_action_event(BaseAccountActionEvent(account.id, description="Phone deleted"))
                    flash(_("The phone number has been unlinked and deleted"), "success")
            except Exception:
                flash(_("Error deleting phone number"), "error")
        else:
            flash(_("Phone number not specified"), "error")

        return redirect(self.get_url(".account_phone_manage", id=account.id))

    @expose("/manage_contacts", perm="manage_contacts", methods=("GET", "POST"))
    def manage_contacts(self):
        account = self.account_or_404()

        form_cls = ContactsForm
        form = form_cls(request.form)

        if request.method == "POST":
            self._view_only and abort(403)

            if form.validate():
                try:
                    req = UpdateAccountRequest()
                    form.populate_obj(req)

                    res, error = self._prebilling_service.accounts.update_account(account.id, req)

                    if res:
                        self.set_user_action_event(
                            BaseAccountActionEvent(account.id, description="Account contacts updated")
                        )
                        flash(_("Account contact information saved"), "success")  # Account contacts has been saved

                    if error:
                        flash(
                            "{}: {}".format(_("Error saving account contact information"), error),
                            "error",
                        )  # Account contacts saving failed: {}

                    return redirect(self.get_url(".info", id=account.id))
                except Exception as e:
                    flash(
                        "{}: {}".format(_("Error saving account contact information"), e),
                        "error",
                    )  # Error updating account contacts: {}
        else:
            form = form_cls(obj=account)

        return self.render(self.contacts_template, form=form, account=account)

    def reset_password_helper(self, account: Account, provider: Provider) -> Account:
        """Helper for account's password resetting."""
        provider_login = account.provider_login
        if provider_login is None:
            provider_login = account.login

        tmpl = Template(provider.reset_password_text)
        req = UpdateUserRequest()

        if provider.generate_password is True:
            password = generate_password(
                provider.password_length or self._password_length, provider.password_alphabet or self._password_alphabet
            )

            hsh, salt = encrypt_password(password)

            req.password = hsh
            req.salt = salt

            message = tmpl.render(username=provider_login, password=password)
        else:
            code = uuid.uuid4().hex
            req.restore_password_code = code
            url = password_url(self._web_url, account, code)

            message = tmpl.render(url=url, username=provider_login)

        account = self._prebilling_service.accounts.update_user(account.id, account.master_user.id, req)
        notify(account, provider.reset_password_subject, message, notifier=provider.notifier)

        return account

    @expose("/reset_password", perm="reset_password", methods=("GET", "POST"))
    def reset_password(self):
        self._view_only and abort(403)
        account = self.account_or_404()

        provider_id = account.provider["id"]
        provider = Provider.objects.get(id=provider_id)

        subject = provider.reset_password_subject
        tmpl = Template(provider.reset_password_text)

        if request.method == "POST":
            try:
                self.reset_password_helper(account, provider)
                self.set_user_action_event(BaseAccountActionEvent(account.id, description="Password reset"))

                flash(_("Password reset successfully"), "success")  # Account password is reset
            except Exception as e:
                flash("{}: {}".format(_("Password reset error"), e), "error")  # Error reseting account password: {}

            return redirect(self.get_url(".info", id=account.id))

        login = account.login

        if account.provider_login is not None:
            login = account.provider_login

        if provider.generate_password:
            message = tmpl.render(username=login, password="[password]")
        else:
            message = tmpl.render(username=login, url="http://url-to-set-password")

        return self.render(
            self.reset_password_template,
            subject=subject,
            message=message,
            frm=self._notification_email,
            account=account,
        )

    def remove(self, unlink=False):
        redir = True

        self._view_only and abort(403)

        account = self.account_or_404()

        provider_id = account.provider["id"]

        if request.method == "POST":
            try:
                purchases = self._prebilling_service.purchases.subscriptions(account.id, provider_id) or []

                skip_integration = request.values.get("skip-integrations", False)
                skip_notification = request.values.get("skip-notifications", False)

                if purchases and any(map(lambda p: p.state == "ACTIVE", purchases)):
                    flash("{}: {}".format(_("The account has active purchases"), account.id), "error")
                    redir = False
                else:
                    if unlink:
                        for purchase in purchases:
                            req = PurchaseRequest()
                            req.account_id = account.id
                            req.provider_id = provider_id
                            req.offer_id = purchase.offer_id

                            req.skip_integration = skip_integration
                            req.skip_notification = skip_notification

                            (
                                resp,
                                error,
                            ) = self._prebilling_service.purchases.disable_purchase(req)

                        update_request = UpdateAccountRequest()
                        update_request.skip_integration = skip_integration
                        update_request.skip_notification = skip_notification

                        update_request.provider = {
                            "id": EMPTY_PROVIDER,
                            "state": PROVIDER_STATE_APPROVED,
                        }

                        resp, error = self._prebilling_service.accounts.update_account(account.id, update_request)
                    else:
                        resp, error = self._prebilling_service.accounts.delete_account(account.id, force=True)

                    if resp:
                        description = "Account has been deleted"
                        if unlink:
                            description = "Account has been unlinked"
                        self.set_user_action_event(BaseAccountActionEvent(account.id, description=description))

                        flash(
                            "{}: {}".format(_("The account has been successfully deactivated"), account.id), "success"
                        )

                    error and flash(
                        "{} {}: {}".format(_("Account deactivation error"), account.id, error),
                        "error",
                    )

            except Exception as e:
                flash("{} {}: {}".format(_("Account deactivation error"), account.id, e), "error")

            if redir:
                return redirect(self.get_url(".index"))

        return self.render(self.remove_template, unlink=unlink, account=account)

    @expose("/delete", perm="delete", methods=("GET", "POST"))
    def delete(self):
        return self.remove()

    @expose("/unlink", perm="unlink", methods=("GET", "POST"))
    def unlink(self):
        return self.remove(unlink=True)

    @expose("/show-pin", perm="pin", methods=("GET", "POST"))
    def show_pin(self):
        account = self.account_or_404()
        self.set_user_action_event(BaseAccountActionEvent(account.id, description="PIN viewed"))
        return self.render(self.pin_template, account=account, profiles=account.profiles)
