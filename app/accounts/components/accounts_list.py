import functools
from datetime import timed<PERSON>ta

from dateutil.parser import parse
from flask import (
    flash,
    request,
)
from flask_babelex import lazy_gettext as _
from prebilling.accounts import Account
from prebilling.rest.response import PrebillingError

from app.auth import expose

from .base import Component


class AccountsList(Component):
    """Base account's info and operations component.

    It allows to manage password phone, contact details,
    and also to delete or unlink account.
    """

    skip_account = True

    list_page_size = 20  # Size of accounts list page

    list_template = "accounts/accounts.html"

    _list_columns = (
        ("login", _("Login")),
        ("email", _("E-mail")),
        ("purchases", _("Subscriptions")),
        ("region", _("Region")),
        ("provider", _("Provider")),
    )

    def __init__(
        self,
        prebilling_service=None,
        disable_cancel_subscription=False,
        disable_group_operations=False,
        hide_provider_columns=False,
        view_only=False,
        list_query=None,
        list_columns=None,
        cms=False,
        *args,
        **kwargs,
    ):

        self._prebilling_service = prebilling_service
        self._disable_cancel_subscription = disable_cancel_subscription
        self._disable_group_operations = disable_group_operations
        self._hide_provider_columns = hide_provider_columns
        self._view_only = view_only

        self._list_query = list_query

        self._cms = cms

        if list_columns:
            self._list_columns = list_columns

        super().__init__(*args, **kwargs)

    def widget_args(self, account: Account) -> dict:
        """Should provide arguments for rendering a widget on account's main page.

        Returned dict is used as templates args in `render_widget`
        method.
        """
        return dict(
            hide_provider=self._hide_provider,
            disable_user_deletion=self._disable_user_deletion,
            account=account,
        )

    @property
    def list_query(self):
        params = {}

        if self.provider:
            params["provider"] = self.provider.id

        return dict(**params, **self._list_query)

    @property
    def list_columns(self):

        if callable(self._list_columns):
            return self._list_columns()

        return self._list_columns

    @expose("/", methods=("GET",))
    def index(self):
        """Handler for accounts list page.

        It expects the following optional parameters: ``page``, ``page_size``,
        ``search``, ``search_field``, ``reg_from``, ``reg_to`` and ``offer``.
        """
        provider = self.provider
        params = request.args.to_dict()
        page = int(params.pop("page", "0"))
        page_size = int(params.pop("page_size", self.list_page_size))

        search = params.pop("search", "")
        search_field = params.pop("search_field", "login")

        sort = params.get("sort", "")

        exact = False if params.get("exact") in ("false", "0", "False") else True
        if "exact" in params:
            del params["exact"]

        if search and search_field:
            search = search.strip()

            search_term = search

            if search_field == "mac":
                params["device_id"] = search_term
            else:
                if exact:
                    search_term = "exact:" + search
                params["search"] = {search_field: search_term}

            if not sort:
                sort = search_field

        registred_from = params.pop("registred_from", "")
        registred_to = params.pop("registred_to", "")

        if registred_from:
            params["registred_from"] = registred_from = parse(registred_from)

        if registred_to:
            params["registred_to"] = registred_to = parse(registred_to)

        params["frm"] = page * page_size

        offer = params.pop("offer", "")
        if offer:
            params["offers"] = [offer]

        offers = self._prebilling_service.offers.list(provider.id if provider else None) or []
        offers_map = {o.id: o for o in offers}

        params["to"] = params["frm"] + page_size - 1

        params["return_count"] = True

        list_params = dict(params)

        for key in list(list_params.keys()):
            if key not in set(
                [
                    "frm",
                    "to",
                    "provider",
                    "offers",
                    "search",
                    "include_purchases",
                    "sort",
                    "registred_from",
                    "registred_to",
                    "return_count",
                    "provider_state",
                    "device_id",
                ]
            ):
                list_params.pop(key)

        if "registred_to" in list_params:
            list_params["registred_to"] += timedelta(hours=24)

        try:
            accounts, count = self._prebilling_service.accounts.list(**list_params, **self.list_query)
        except PrebillingError as e:
            accounts, count = [], 0
            flash(str(e), "error")

        pages = (count or 0) // page_size + 1

        def _url(p):
            _params = params.copy()
            _params["offer"] = offer
            _params["exact"] = exact
            _params["search"] = search
            _params["page"] = p or None
            _params["page_size"] = page_size
            _params["search_field"] = search_field
            return self.get_url(".index", **_params)

        providers = {acc.id: self.prepare_provider(acc.provider) for acc in accounts or []}

        default_fields_for_search = [
            {"type": "string", "title": "Email", "name": "email"},
            {"type": "string", "title": "Login", "name": "login"},
            {"type": "string", "title": "Phone", "name": "users.phone"},
            {"type": "string", "title": "MAC", "name": "mac"},
        ]

        sort_by_creation = functools.partial(sorted, key=self.purchase_sort_key(offers_map))

        return self.render(
            self.list_template,
            search_fields=provider.fields_for_search if provider else default_fields_for_search,
            provider_columns=provider.list_fields if provider else None,
            group_operations=not self._disable_group_operations,
            hide_provider_columns=self._hide_provider_columns,
            sort_by_creation=sort_by_creation,
            registred_from=registred_from,
            list_columns=self.list_columns,
            registred_to=registred_to,
            filters=provider is not None,
            list_offers_map=offers_map,
            search_field=search_field,
            providers=providers,
            page_size=page_size,
            accounts=accounts,
            uri=request.url,
            num_pages=pages,
            offer_id=offer,
            offers=offers,
            search=search,
            count=count,
            page=page,
            exact=exact,
            sort=sort,
            url=_url,
            cms=self._cms,
        )
