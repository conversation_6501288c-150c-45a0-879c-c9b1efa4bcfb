from flask_security import current_user
from prebilling.accounts import Account

from .base import Component


class Comments(Component):
    """Comments component for managing comments to accounts."""

    template = "comments/widget.html"

    def __init__(self, comments=None, view_only=None, *args, **kwargs):
        self._view_only = view_only
        self._comments = comments

        super().__init__(view_only=view_only, *args, **kwargs)

    @property
    def is_allowed(self):
        return current_user.is_allowed(self.endpoint, ("comments_view"))

    def widget_args(self, account: Account) -> dict:
        return dict(
            model=account,
            comments=self._comments(account.id).limit(3),
            return_url=self.get_url(".info", id=account.id),
        )
