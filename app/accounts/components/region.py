from flask import (
    abort,
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from flask_security import current_user
from prebilling.accounts import Account
from prebilling.rest.accounts import UpdateAccountRequest

from app.accounts.activity.events import BaseAccountActionEvent
from app.auth import expose
from app.regions import Regions

from .base import Component
from .forms import create_region_form


class Region(Component):
    """Region component for managing region of an account."""

    extra_permissions = {"manage_region_view"}

    template = "accounts/account_region.html"
    manage_region_template = "accounts/region_manage.html"

    view_only_excluded = {"manage_region"}

    def __init__(self, prebilling_service=None, view_only=False, *args, **kwargs):
        self._prebilling_service = prebilling_service
        self._view_only = view_only

        super().__init__(view_only=view_only, *args, **kwargs)

    @property
    def is_allowed(self):
        return current_user.is_allowed(self.endpoint, ("manage_region_view", "manage_region_edit"))

    def widget_args(self, account: Account) -> dict:
        view_only = self._view_only or not current_user.is_allowed(self.endpoint, "manage_region_edit")
        return dict(view_only=view_only, account=account)

    @expose("/manage_region", perm="manage_region_edit", methods=("GET", "POST"))
    def manage_region(self):
        self._view_only and abort(403)

        account = self.account_or_404()

        form_cls = create_region_form(Regions.list())
        form = form_cls(request.form)

        if request.method == "POST" and form.validate():
            try:
                req = UpdateAccountRequest()
                form.populate_obj(req)

                if self._prebilling_service.accounts.update_account(account.id, req):
                    self.set_user_action_event(
                        BaseAccountActionEvent(
                            account.id, description=f"Region is changed from {account.region} to {req.region}"
                        )
                    )

                    flash(_("Region has been saved"), "success")
                else:
                    flash(_("Error saving region"), "error")

                return redirect(self.get_url(".info", id=account.id))
            except Exception as e:
                flash("{}: {}".format(_("Error saving region"), e), "error")

        form.region.data = account.region

        return self.render(self.manage_region_template, form=form, account=account)
