import re

from flask_admin.model.fields import (
    AjaxSelectField,
    AjaxSelectMultipleField,
)
from flask_babelex import lazy_gettext as _
from flask_wtf.form import FlaskForm
from wtforms import (
    Form,
    ValidationError,
    fields,
    validators,
)

from app.common.fields import (
    GroupedSelectField,
    TZAwareDateTimeField,
)
from app.common.widgets import (
    LTEHorizontalAjaxSelect2Widget,
    LTEHorizontalCheckbox,
    LTEHorizontalSelect,
    LTEHorizontalSelect2,
    LTEHorizontalSelect2Grouped,
    LTEHorizontalTextArea,
    LTEHorizontalTextInput,
    SelectProvidersWidget,
)
from app.purchases.models import Provider

DEFAULT_LOGIN_PATTERN = re.compile("^([a-zA-Z0-9_@\\.\\+\\-\\//]+)$")


class ContactsForm(Form):
    email = fields.StringField(
        widget=LTEHorizontalTextInput(),
        validators=[validators.Email(message=_("Incorrect email format"))],
        label=_("Email address"),
    )


def create_new_subscription_form(
    available_offers,
    show_integration=False,
    show_notification=False,
    offer_types={},
    timezone="utc",
    cms=False,
):
    class NewSubscriptionForm(Form):
        groups = sorted(set(map(lambda o: o.typ.value, available_offers)))

        offer_id = GroupedSelectField(
            choices=[
                (
                    offer_types.get(g),
                    [(o.id, f"{o.name} ({o._name})" if cms else o.name) for o in available_offers if o.typ.value == g],
                )
                for g in groups
            ],
            validators=[validators.InputRequired()],
            widget=LTEHorizontalSelect2Grouped(),
            label=_("Offer"),
        )

        expire_time = TZAwareDateTimeField(
            widget=LTEHorizontalTextInput(),
            validators=[validators.Optional()],
            timezone=timezone,
            format="%d.%m.%y %H:%M",
            label=_("Expiration time"),
        )

    if show_integration:
        skip_integration = fields.BooleanField(widget=LTEHorizontalCheckbox(), label=_("Ignore integrations"))
        setattr(NewSubscriptionForm, "skip_integration", skip_integration)

    if show_notification:
        skip_notification = fields.BooleanField(
            widget=LTEHorizontalCheckbox(),
            default=True,
            label=_("Do not send notifications"),
        )
        setattr(NewSubscriptionForm, "skip_notification", skip_notification)

    return NewSubscriptionForm


def create_disable_subscription_form(
    available_offers,
    show_integration=False,
    show_notification=False,
    default_offer=None,
    show_force_disable=False,
):
    class DisableSubscriptionForm(Form):
        kwargs = {
            "choices": [(o.id, f"{o.name} ({o._name})") for o in available_offers],
            "widget": LTEHorizontalSelect(),
            "validators": [validators.InputRequired()],
        }

        if default_offer is not None:
            kwargs["default"] = default_offer

        offer_id = fields.SelectField(**kwargs, label=_("Offer"))

    if show_integration:
        skip_integration = fields.BooleanField(
            widget=LTEHorizontalCheckbox(),
            default=True,
            label=_("Ignore integrations"),
        )
        setattr(DisableSubscriptionForm, "skip_integration", skip_integration)

    if show_notification:
        skip_notification = fields.BooleanField(
            widget=LTEHorizontalCheckbox(),
            default=True,
            label=_("Do not send notifications"),
        )
        setattr(DisableSubscriptionForm, "skip_notification", skip_notification)

    if show_force_disable:
        DisableSubscriptionForm.force_disable = fields.BooleanField(
            widget=LTEHorizontalCheckbox(),
            default=True,
            label=_("Force disable"),
        )

    return DisableSubscriptionForm


def create_device_form():
    class DeviceForm(Form):
        id = fields.StringField(
            widget=LTEHorizontalTextInput(),
            label="ID",
            validators=[validators.InputRequired()],
        )

        id_type = fields.SelectField(
            widget=LTEHorizontalSelect(),
            label=_("ID Type"),
            validators=[validators.InputRequired()],
            choices=[("mac", "mac")],
        )

        name = fields.StringField(widget=LTEHorizontalTextInput(), label=_("Name"))
        description = fields.StringField(widget=LTEHorizontalTextArea(), label=_("Description"))

    return DeviceForm


def create_purchase_expiration_form(timezone="utc"):
    class PurchaseExpirationForm(Form):
        purchase_id = fields.HiddenField()

        expire_time = TZAwareDateTimeField(
            widget=LTEHorizontalTextInput(),
            validators=[validators.Optional()],
            timezone=timezone,
            format="%d.%m.%y %H:%M",
            label=_("Expiration time"),
        )

    return PurchaseExpirationForm


def create_region_form(regions):
    class RegionForm(Form):
        region = fields.SelectField(
            widget=LTEHorizontalSelect2(),
            label=_("Region"),
            choices=[("", "---")] + [(r, r) for r in regions],
        )

    return RegionForm


def create_phone_form(verified=False, regexp="^[+]?[0-9]*$"):
    class PhoneForm(Form):
        phone = fields.StringField(
            widget=LTEHorizontalTextInput(),
            label=_("Phone number"),
            render_kw={"readonly": verified},
            validators=[
                validators.Regexp(regexp, message="{}, regexp = {}".format(_("Incorrect phone number"), regexp)),
                validators.InputRequired(),
            ],
        )

    return PhoneForm


def create_batch_subscription_form(
    show_integration=False,
    show_notification=False,
    action="create",
    timezone="utc",
):
    class BatchNewSubscriptionForm(Form):
        pass
        # action = fields.HiddenField()
        # offer_id = fields.HiddenField()
        # account_ids = fields.FieldList(fields.HiddenField(), widget=HiddenFieldList())

    if action == "create":
        setattr(
            BatchNewSubscriptionForm,
            "expire_time",
            TZAwareDateTimeField(
                widget=LTEHorizontalTextInput(),
                validators=[validators.Optional()],
                timezone=timezone,
                format="%d.%m.%y %H:%M",
                label=_("Expiration time"),
            ),
        )

    if show_integration:
        skip_integration = fields.BooleanField(widget=LTEHorizontalCheckbox(), label=_("Skip integration"))
        setattr(BatchNewSubscriptionForm, "skip_integration", skip_integration)

    if show_notification:
        skip_notification = fields.BooleanField(
            widget=LTEHorizontalCheckbox(),
            default=True,
            label=_("Do not notify the user"),
        )
        setattr(BatchNewSubscriptionForm, "skip_notification", skip_notification)

    return BatchNewSubscriptionForm


def create_provider_info_form(provider):
    class ProviderInfoForm(Form):
        pass

    for form_field in provider.user_form:
        setattr(
            ProviderInfoForm,
            form_field["name"],
            fields.StringField(label=form_field["title"], widget=LTEHorizontalTextInput()),
        )

    return ProviderInfoForm


class LoginValidator(validators.Regexp):
    def __init__(self, regex, extra=False, **kwargs):
        super(LoginValidator, self).__init__(regex, **kwargs)

        self.regex = regex or DEFAULT_LOGIN_PATTERN
        self.extra = extra

    def __call__(self, form, field, message=None):
        if self.extra and field.data.startswith("+") and "@" not in field.data:
            raise ValidationError(message or self.message or "Invalid login")

        return super(LoginValidator, self).__call__(form, field, message)


providers_loader = Provider.get_ajax_loader()


def create_new_account_form(
    *,
    email_required=True,
    phone_required=False,
    phone_regexp=None,
    show_password=False,
    password_length=5,
    show_integration=False,
    show_notification=False,
    login_regex=None,
    login_extra_validation=False,
    show_phone_confirmation=False,
    show_phone=False,
    show_provider=False,
):
    login_validators = []

    login_validators.append(
        LoginValidator(
            login_regex,
            message=_("Incorrect login format"),
            extra=login_extra_validation,
        )
    )
    login_validators.append(validators.InputRequired(message=_("Login required")))

    email_validators = [validators.Email(message=_("Incorrect email format"))]
    if email_required:
        email_validators.append(validators.InputRequired(message=_("Email required")))

    phone_validators = []
    if phone_required:
        phone_validators.append(validators.InputRequired(message=_("Phone number required")))

    class NewAccountForm(Form):
        if show_provider:
            provider = AjaxSelectField(
                render_kw={"data-placeholder": "Select provider"},
                widget=LTEHorizontalAjaxSelect2Widget(),
                validators=[validators.InputRequired()],
                loader=providers_loader,
                label=_("Provider"),
            )

        login = fields.StringField(validators=login_validators, widget=LTEHorizontalTextInput(), label=_("Login"))

        email = fields.StringField(
            validators=email_validators,
            widget=LTEHorizontalTextInput(),
            label=_("Email"),
        )

        if show_password:
            password = fields.StringField(
                validators=[
                    validators.Optional(),
                    validators.EqualTo("confirm_password", message=_("Passwords do not match")),
                    validators.Length(min=password_length),
                ],
                widget=LTEHorizontalTextInput(),
                label=_("Password"),
            )
            confirm_password = fields.StringField(widget=LTEHorizontalTextInput(), label=_("Confirm password"))

        if show_phone:
            phone = fields.StringField(
                validators=phone_validators,
                widget=LTEHorizontalTextInput(),
                label=_("Phone"),
            )

        if show_phone_confirmation:
            is_phone_verified = fields.BooleanField(
                widget=LTEHorizontalCheckbox(), default=True, label=_("Phone number confirmed")
            )

        if show_integration:
            skip_integration = fields.BooleanField(
                widget=LTEHorizontalCheckbox(), default=True, label=_("Ignore integrations")
            )

        if show_notification:
            skip_notification = fields.BooleanField(
                widget=LTEHorizontalCheckbox(), default=True, label=_("Do not send notifications")
            )

        def validate_phone(self, field):
            if not field.data:
                return True
            if not phone_regexp:
                return True
            if not re.match(phone_regexp, field.data):
                raise ValidationError("{}, regexp = {}".format(_("Incorrect phone number"), phone_regexp))

    return NewAccountForm


class ExportAccountsForm(FlaskForm):
    providers = AjaxSelectMultipleField(
        render_kw={"data-placeholder": "Select provider"},
        widget=SelectProvidersWidget(multiple=True),
        validators=[validators.InputRequired()],
        loader=providers_loader,
    )
