from __future__ import annotations

import logging
from typing import Literal

from flask import (
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from flask_security import current_user
from markupsafe import Markup

from app.accounts.activity.events import BaseAccountActionEvent
from app.auth import expose
from app.proto.services import (
    AuthhistoryService,
    SessionsService,
)
from app.sesskeeper.proto.sessions import (
    Session,
    SessionsQuery,
)

from .base import Component

logger = logging.getLogger(__name__)


class Sessions(Component):
    """Sessions component for Sesskeeper and Authhistory services integration.

    It is an interface for rendering and managing sessions for an account.

    Required permission to use the component is `sessions`.
    """

    extra_permissions = {"sessions"}

    template = "accounts/account_sessions.html"
    authhistory_template = "accounts/account_authhistory.html"

    authhistory_page_size = 20

    def __init__(
        self,
        prebilling_service=None,
        view_only=False,
        *args,
        **kwargs,
    ):
        self._prebilling_service = prebilling_service
        self.authhistory_service = AuthhistoryService.get_instance()
        self.sessions_service = SessionsService.get_instance()
        self._view_only = view_only

        super().__init__(*args, **kwargs)

    @property
    def is_allowed(self):
        return current_user.is_allowed(self.endpoint, "sessions")

    def widget_args(self, account):
        return dict(
            sessions=self._get_sessions(account.id),
            render_client_info_labels=self.render_client_info_labels,
            view_only=self._view_only,
            account=account,
        )

    def _get_sessions(self, account_id: str) -> list[Session] | None:
        if current_user.is_allowed(self.endpoint, "sessions"):
            try:
                return self.sessions_service.get_sessions(account=account_id).sessions
            except Exception as e:
                logger.error(f"Sesskeeper service error: {str(e)}")

    @staticmethod
    def render_client_info_labels(client_info: dict[str, str], client_type: Literal["app", "device"]) -> str:
        return Markup(
            " ".join(
                f'<span class="label label-default" title="{key.split(".")[1]}">{value}</span>'
                for key, value in client_info.items()
                if key.startswith(client_type) and value
            )
        )

    @expose("/sessions", perm="sessions", methods=("GET",))
    def _sessions(self) -> list[Session]:
        account = self.account_or_404()
        return self._get_sessions(account.id)

    @expose("/delete_session", perm="delete_sessions", methods=("GET",))
    def delete_session(self):
        account = self.account_or_404()

        session_id = request.args.get("session_id")
        all = request.args.get("all")

        try:
            if all:
                self.sessions_service.del_sessions(query=SessionsQuery(account=account.id))
                self.set_user_action_event(
                    BaseAccountActionEvent(account.id, description=f"Session {session_id} is deleted")
                )
                flash(_("All sessions have been successfully deleted"), "success")
            else:
                self.sessions_service.delete(id=session_id)
                self.set_user_action_event(BaseAccountActionEvent(account.id, description="All sessions are deleted"))
                flash(_("Session successfully deleted"), "success")
        except Exception:
            flash(_("Error deleting session"), "error")

        return redirect(self.get_url(".info", id=account.id))

    @expose("/delete_sessions", perm="delete_sessions", methods=("GET",))
    def delete_sessions(self):
        account = self.account_or_404()

        try:
            self.sessions_service.del_sessions(query=SessionsQuery(account=account.id))
            flash(_("Sessions successfully deleted"), "success")
        except Exception:
            flash(_("Error deleting sessions"), "error")

        return redirect(self.get_url(".info", id=account.id))

    @expose("/authhistory", perm="authhistory", methods=("GET",))
    def authhistory(self):
        account = self.account_or_404()
        sessions = self._get_sessions(account.id)
        session_ids = {s.id for s in sessions} if sessions else None

        page = request.args.get("page", 0, int)
        page_size = self.authhistory_page_size
        skip = page * page_size

        try:
            response = self.authhistory_service.get(account_id=account.id, skip=skip, limit=page_size + 1)
            authhistory_items = response.authhistory_items
            more = len(authhistory_items) > page_size

            authhistory_items = authhistory_items[:-1] if more else authhistory_items
        except Exception as e:
            logger.error(f"Authhistory service error: {str(e)}")
            authhistory_items = None
            more = False

        return self.render(
            self.authhistory_template,
            render_client_info_labels=self.render_client_info_labels,
            account=account,
            sessions=session_ids,
            history=authhistory_items,
            page=page,
            more=more,
            frm=skip,
        )
