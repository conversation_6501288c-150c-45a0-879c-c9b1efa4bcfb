import datetime

import requests
from flask import (
    abort,
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from flask_security import current_user
from prebilling.accounts import Account
from prebilling.providers import Provider
from prebilling.rest.providers import PROVIDER_STATE_APPROVED
from prebilling.rest.purchases import PurchaseRequest

from app.accounts.activity.events import BaseAccountActionEvent
from app.auth import expose
from app.common.views import get_channels_for_offer

from .base import Component
from .forms import (
    create_batch_subscription_form,
    create_disable_subscription_form,
    create_new_subscription_form,
    create_purchase_expiration_form,
)


def check_provider(provider: Provider, account: Account) -> None:
    """Checks account's provider state or raise 403 error if it is invalid."""
    if provider and account.provider.get("state") != PROVIDER_STATE_APPROVED:
        abort(403)


class Subscriptions(Component):
    """Account subscriptions component.

    It is an interface for rendering and managing subscriptions for an account.
    """

    extra_permissions = {"force_disable_subscription"}

    list_page_size = 20

    template = "accounts/account_purchases.html"
    subscriptions_history_template = "accounts/subscriptions_history.html"
    subscription_details_template = "accounts/subscription_details.html"
    manage_subscription_template = "accounts/subscription_manage.html"
    disable_subscription_template = "accounts/subscription_disable.html"
    set_subscription_expiration_time_template = "accounts/subscription_set_expiration_time.html"

    def __init__(
        self,
        prebilling_service=None,
        purchases_service=None,
        disable_cancel_subscription=False,
        hide_subscription_details=False,
        view_only=False,
        show_integration=False,
        show_notification=False,
        timezone="utc",
        offer_types={},
        cms=False,
        *args,
        **kwargs,
    ):
        self._prebilling_service = prebilling_service
        self._purchases_service = purchases_service
        self._disable_cancel_subscription = disable_cancel_subscription
        self._hide_subscription_details = hide_subscription_details
        self._view_only = view_only

        self._show_integration = show_integration
        self._show_notification = show_notification
        self._offer_types = offer_types
        self._timezone = timezone

        self._cms = cms

        super().__init__(*args, **kwargs)

    def subs(self, account_id, provider_id):
        return [
            s
            for s in self._prebilling_service.purchases.subscriptions(account_id, provider_id) or []
            if s.state in ("ACTIVE", "SUSPENDED") and not (not self._cms and s.is_uncommitted)
        ]

    def widget_args(self, account):
        provider_id = account.provider["id"]

        offers = self._prebilling_service.offers.list(provider_id)
        offers_map = {o.id: o for o in offers}

        subscriptions = sorted(self.subs(account.id, provider_id), key=self.purchase_sort_key(offers_map))

        return dict(
            offers_map=offers_map,
            subscriptions=subscriptions,
            hide_subscription_details=self._hide_subscription_details,
            view_only=self._view_only,
            account=account,
            cms=self._cms,
        )

    def check_for_commit(self, purchase_id=None, purchase=None):
        if not self._cms:
            if purchase_id:
                purchase = self._prebilling_service.purchases.subscription_details(purchase_id)

            purchase and purchase.is_uncommitted and abort(403)

    @expose("/subscriptions_history", perm="subscription_history", methods=("GET",))
    def subscriptions_history(self):
        account = self.account_or_404()

        provider_id = account.provider["id"]

        page = request.args.get("page", 0, int)
        page_size = self.list_page_size
        frm = page * page_size
        to = page * page_size + page_size - 1

        subscriptions, pagination = self._prebilling_service.purchases.history(
            account.id, provider_id, frm=frm, to=to, committed_only=not self._cms
        )
        if subscriptions is None:
            subscriptions = []

        offers = self._prebilling_service.offers.list(provider_id)
        offers_map = {o.id: o for o in offers}

        return self.render(
            self.subscriptions_history_template,
            subscriptions=subscriptions,
            hide_subscription_details=self._hide_subscription_details,
            offers_map=offers_map,
            pagination=pagination,
            frm=frm,
            to=to,
            count=pagination.total_filtered,
            uri=request.url,
            page=page,
            page_size=page_size,
            account=account,
            cms=self._cms,
        )

    def _flash_warning(self, service: str):
        """Shortcut."""
        flash(_("Parameters of %(service)s subscription does not match the offer").format(service=service), "warning")

    @expose("/subscription_details", perm="subscription_details", methods=("GET",))
    def subscription_details(self):
        if self._hide_subscription_details or self._view_only:
            abort(403)

        account = self.account_or_404()
        s_id = request.args.get("s_id")

        purchase = self._prebilling_service.purchases.subscription_details(s_id)
        purchase or abort(404)

        self.check_for_commit(purchase=purchase)

        offer = self._prebilling_service.offers.one(purchase.offer_id)

        if purchase.amedia_purchase and purchase.amedia_purchase.get("megogo"):
            if not offer.amedia or purchase.amedia_purchase["megogo"].get("serviceId") != offer.amedia.get(
                "service_id"
            ):
                self._flash_warning("Amedia")

        if purchase.amedia_2_purchase:
            if not offer.amedia2 or purchase.amedia_2_purchase.get("tariffId") != offer.amedia2.get("tariff_id"):
                self._flash_warning("Amedia2")

        if purchase.ivi_purchase:
            if (
                not offer.ivi
                or purchase.ivi_purchase.get("productId") != offer.ivi.get("product_id")
                or offer.megogo
                and purchase.megogo_purchase
                and purchase.megogo_purchase.get("paymentKey") != offer.megogo.get("payment_key")
            ):
                self._flash_warning("IVI")

        if purchase.kazvod_purchase:
            if not offer.kazvod or not offer.kazvod.get("is_enabled"):
                self._flash_warning("KazVOD")

        if purchase.megogo_purchase:
            if not offer.megogo or purchase.megogo_purchase.get("serviceId") != offer.megogo.get("service_id"):
                self._flash_warning("Megogo")

        if purchase.moretv_purchase and purchase.moretv_purchase.get("megogo"):
            if not offer.wink or purchase.moretv_purchase["megogo"].get("serviceId") != offer.wink.get("service_id"):
                self._flash_warning("Wink")

        if purchase.start_purchase:
            if not offer.start or not offer.start.get("is_enabled"):
                self._flash_warning("Start")

        if purchase.vipplay_purchase:
            if not offer.vipplay or not offer.vipplay.get("is_enabled"):
                self._flash_warning("VIP Play")

        next_offer = None
        if purchase.change_info:
            next_offer = self._prebilling_service.offers.one(purchase.change_info.get("offerId", ""))

        return self.render(
            self.subscription_details_template,
            purchase=purchase,
            next_offer=next_offer,
            uri=request.url,
            offer=offer,
            account=account,
            states=self._purchases_service.PURCHASE_STATES,
            channels=get_channels_for_offer(offer),
            cms=self._cms,
        )

    @expose("/manage_subscription", perm="manage_subscription", methods=("GET", "POST"))
    def manage_subscription(self):
        account = self.account_or_404()

        check_provider(self.provider, account)

        provider_id = account.provider["id"]

        offers = self._prebilling_service.offers.list(provider_id)
        subscriptions_offers_id = [s.offer_id for s in self.subs(account.id, provider_id)]
        to_create = [o for o in offers if o.id not in subscriptions_offers_id]

        form = create_new_subscription_form(
            to_create,
            show_integration=self._show_integration,
            show_notification=self._show_notification,
            offer_types=self._offer_types,
            timezone=self._timezone,
            cms=self._cms,
        )(request.form)

        if request.method == "POST":
            self._view_only and abort(403)

            if form.validate():
                try:
                    req = PurchaseRequest()
                    req.provider_id = provider_id
                    req.account_id = account.id
                    req.skip_integration = True
                    req.skip_notification = True
                    form.populate_obj(req)

                    # Weird crutch from #96694
                    # Bring back timezone to 'expireTime' field.
                    # Timezone was originally dropped here:
                    # https://gitlab.lfstrm.tv/server-side/cms/cms/-/commit/b76bbe17abe702a6590470a62e14b17997c5c88a
                    if req.expire_time:
                        req.expire_time = req.expire_time.replace(tzinfo=datetime.timezone.utc)

                    resp, error = self._prebilling_service.purchases.create_purchase(req)

                    if resp:
                        self.set_user_action_event(
                            BaseAccountActionEvent(
                                account.id, description=f"Purchase {form.offer_id.data} has been created"
                            )
                        )
                        flash(_("Purchase saved"), "success")

                    error and flash("{}: {}".format(_("Error saving purchase"), error), "error")

                    return redirect(self.get_url(".info", id=account.id))

                except Exception as e:
                    flash("Error when editing subscription: {}".format(e), "error")
        return self.render(self.manage_subscription_template, form=form, account=account, new=True)

    @expose("/disable_subscription", perm="disable_subscription", methods=("GET", "POST"))
    def disable_subscription(self):
        account = self.account_or_404()

        check_provider(self.provider, account)

        s_id = request.args.get("s_id")
        offer_id = request.args.get("offer_id")
        provider_id = account.provider["id"]

        self.check_for_commit(purchase_id=s_id)

        offers = self._prebilling_service.offers.list(provider_id)
        offer = next(o for o in offers if o.id == offer_id)

        if not offer:
            abort(404)

        if not self.cancelling_allowed(offer):
            abort(403)

        purchased_offers = {s.offer_id for s in self.subs(account.id, provider_id)}
        available_for_disable_offers = [o for o in offers if o.id in purchased_offers and self.cancelling_allowed(o)]

        force_disable_allowed = current_user.is_allowed(self.endpoint, "force_disable_subscription")

        form = create_disable_subscription_form(
            available_for_disable_offers,
            show_integration=self._show_integration,
            show_notification=self._show_notification,
            show_force_disable=force_disable_allowed,
            default_offer=offer_id,
        )(request.form)

        if request.method == "POST":
            self._view_only and abort(403)

            if form.validate():
                try:
                    req = PurchaseRequest()
                    req.account_id = account.id
                    req.provider_id = provider_id
                    req.skip_integration = True
                    req.skip_notification = True

                    form.populate_obj(req)

                    if not force_disable_allowed:
                        req.force_disable = False

                    __, error = self._prebilling_service.purchases.disable_purchase(req)

                    if not error:
                        self.set_user_action_event(
                            BaseAccountActionEvent(
                                account.id, description=f"Purchase {form.offer_id.data} has been disabled"
                            )
                        )
                        flash(_("Purchase cancelled successfully"), "success")

                    error and flash("{}: {}".format(_("Error saving purchase"), error), "error")

                    return redirect(self.get_url(".info", id=account.id))

                except Exception as e:
                    flash("{}: {}".format(_("Error cancelling the purchase"), e), "error")

        return self.render(self.disable_subscription_template, form=form, account=account)

    @expose("/retry_vod_purchases", perm="retry_vod_purchases", methods=("GET", "POST"))
    def retry_vod_purchases(self):
        account = self.account_or_404()

        check_provider(self.provider, account)
        provider_id = account.provider["id"]

        s_id = request.args.get("s_id")
        subscription = self._prebilling_service.purchases.subscription_details(s_id)
        if not subscription or subscription.state == "DISABLED":
            abort(404)

        self.check_for_commit(purchase_id=s_id)

        offer_id = request.args.get("offer_id")
        offers = self._prebilling_service.offers.list(provider_id)
        offer = next(o for o in offers if o.id == offer_id)
        not offer and abort(404)

        try:
            self._purchases_service.retry_enable_vod(account.id, offer.id)
        except Exception as e:
            flash("{}: {}".format(_("Error trying to recreate VOD subscriptions"), e), "error")
        else:
            self.set_user_action_event(BaseAccountActionEvent(account.id, description="Retry VOD purchases requested"))
            flash(_("Request to recreate VOD subscriptions successfully sent"), "success")

        return redirect(self.get_url(".subscriptions_subscription_details", id=account.id, s_id=subscription.id))

    @expose("/set_purchase_expiration", perm="set_purchase_expiration", methods=("GET", "POST"))
    def set_purchase_expiration(self):
        account = self.account_or_404()

        check_provider(self.provider, account)

        s_id = request.args.get("s_id")
        subscription = self._prebilling_service.purchases.subscription_details(s_id)
        if not subscription or subscription.state == "DISABLED":
            abort(404)

        self.check_for_commit(purchase_id=s_id)

        form_class = create_purchase_expiration_form(timezone=self._timezone)

        form = form_class(
            expire_time=subscription.expire_time,
            purchase_id=subscription.id,
        )

        if request.method == "POST":
            form = form_class(request.form)

            self._view_only and abort(403)

            if form.validate():
                try:
                    self._purchases_service.set_purchase_expiration(form.purchase_id.data, form.expire_time.data)
                except Exception as e:
                    flash("{}: {}".format(_("Error occurred while trying to change expiration date"), e), "error")
                else:
                    self.set_user_action_event(
                        BaseAccountActionEvent(account.id, description="Set purchase expiration")
                    )

                    flash(_("Expiration date successfully changed"), "success")

                    return redirect(
                        self.get_url(".subscriptions_subscription_details", id=account.id, s_id=subscription.id)
                    )

        return self.render(self.set_subscription_expiration_time_template, form=form, account=account)


class BatchSubscriptions(Component):
    """Account subscriptions component.

    It is an interface for rendering and managing subscriptions for an account.
    """

    skip_account = True

    PAGE_SIZE = 20

    batch_subscriptions_template = "accounts/batch_subscriptions.html"

    def __init__(
        self,
        prebilling_service=None,
        disable_cancel_subscription=False,
        view_only=False,
        show_integration=False,
        show_notification=False,
        timezone="utc",
        cms=False,
        *args,
        **kwargs,
    ):
        self._prebilling_service = prebilling_service
        self._disable_cancel_subscription = disable_cancel_subscription
        self._view_only = view_only

        self._show_integration = show_integration
        self._show_notification = show_notification

        self._timezone = timezone

        super().__init__(*args, **kwargs)

    def subs(self, account_id, provider_id):
        return [
            s
            for s in self._prebilling_service.purchases.subscriptions(account_id, provider_id) or []
            if s.state in ("ACTIVE", "SUSPENDED") and not (not self._cms and s.is_uncommitted)
        ]

    def widget_args(self, account):
        provider_id = account.provider["id"]

        offers = self._prebilling_service.offers.list(provider_id)
        offers_map = {o.id: o for o in offers}

        subscriptions = sorted(self.subs(account.id, provider_id), key=self.purchase_sort_key(offers_map))

        return dict(offers_map=offers_map, subscriptions=subscriptions, account=account)

    @expose("/batch_subscriptions", perm="batch_subscription", methods=("GET", "POST"))
    def batch_subscriptions(self):
        action = request.args.get("action")
        offer_id = request.args.get("offer_id")
        account_ids = request.args.getlist("account_ids[]") or []

        offers = self._prebilling_service.offers.list(self.provider.id if self.provider else None)
        offer = next(o for o in offers if o.id == offer_id)

        if not offer:
            abort(404)

        if action == "disable" and not self.cancelling_allowed(offer):
            abort(403)

        form = create_batch_subscription_form(
            show_integration=self._show_integration,
            show_notification=self._show_notification,
            action=action,
            timezone=self._timezone,
        )(request.form)

        if request.method == "POST":
            self._view_only and abort(403)

            if form.validate():
                try:

                    def take_action(account_id):
                        account = self._prebilling_service.accounts.get(account_id)

                        check_provider(self.provider, account)

                        req = PurchaseRequest()
                        req.skip_integration = True
                        req.skip_notification = True

                        form.populate_obj(req)

                        req.provider_id = account.provider["id"]
                        req.account_id = account_id
                        req.offer_id = offer_id

                        if action == "create":
                            return self._prebilling_service.purchases.create_purchase(req)

                        return self._prebilling_service.purchases.disable_purchase(req)

                    failed_ids = []
                    success_ids = []

                    for account_id in account_ids:
                        try:
                            resp, error = take_action(account_id)
                            error and failed_ids.append(account_id)
                        except requests.exceptions.HTTPError:
                            failed_ids.append(account_id)
                        else:
                            success_ids.append(account_id)

                    if failed_ids:
                        flash(
                            "{}: {}".format(_("Error saving purchases for accounts"), ", ".join(failed_ids)),
                            "error",
                        )
                    else:
                        flash(
                            "{}: {}".format(_("Purchases saved for accounts"), ", ".join(success_ids)),
                            "success",
                        )

                    return redirect(self.get_url(".index"))

                except Exception as e:
                    raise (e)
                    flash("{}: {}".format(_("Error saving purchase"), e), "error")

        return self.render(
            self.batch_subscriptions_template, account_ids=account_ids, action=action, offer=offer, form=form
        )
