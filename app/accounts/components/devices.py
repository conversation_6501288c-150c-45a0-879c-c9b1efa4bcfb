from flask import (
    abort,
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from flask_security import current_user
from prebilling.accounts import Account
from prebilling.rest.devices import DeviceRequest

from app.accounts.activity.events import BaseAccountActionEvent
from app.auth import expose

from .base import Component
from .forms import create_device_form


class Devices(Component):
    """Devices component for managing devices of an account."""

    extra_permissions = {"manage_device_view"}

    template = "accounts/account_devices.html"
    manage_device_template = "accounts/device_manage.html"

    def __init__(self, prebilling_service=None, view_only=False, *args, **kwargs):
        self._prebilling_service = prebilling_service
        self._view_only = view_only
        super().__init__(*args, **kwargs)

    @property
    def is_allowed(self):
        return current_user.is_allowed(self.endpoint, ("manage_device_view", "manage_device_edit"))

    def widget_args(self, account: Account) -> dict:
        view_only = self._view_only or not current_user.is_allowed(self.endpoint, "manage_device_edit")
        return dict(devices=account.devices, view_only=view_only, account=account)

    @expose("/manage_device", perm="manage_device_edit", methods=("GET", "POST"))
    def manage_device(self):
        account = self.account_or_404()

        device_id = request.args.get("device_id", None)

        form_cls = create_device_form()
        form = form_cls(request.form)

        if request.method == "POST":
            self._view_only and abort(403)

            if form.validate():
                try:
                    new = "new" in request.form

                    req = DeviceRequest()
                    form.populate_obj(req)

                    account_id = account.id
                    user_id = account.master_user.id

                    if new:
                        res, error = self._prebilling_service.devices.new(account_id, user_id, req)
                    else:
                        res, error = self._prebilling_service.devices.update(account_id, user_id, req)

                    if res:
                        self.set_user_action_event(
                            BaseAccountActionEvent(
                                account_id,
                                description=f"Device {req.id} added" if new else f"Device {device_id} edited",
                            )
                        )

                        flash(_("The device has been saved"), "success")
                        return redirect(self.get_url(".info", id=account.id))

                    flash("{}: {}".format(_("Error saving device"), error), "error")

                except Exception as e:
                    flash("{}: {}".format(_("Error saving device"), e), "error")
        else:
            device = next((d for d in account.devices if d.id == device_id), None)
            form = form_cls(obj=device) if device else form_cls()

        return self.render(self.manage_device_template, form=form, new=not device_id, account=account)

    @expose("/delete_device", perm="delete_device", methods=("GET",))
    def delete_device(self):
        self._view_only and abort(403)
        account = self.account_or_404()

        device_id = request.args.get("device_id", None)

        account_id = account.id
        user_id = account.master_user.id

        if self._prebilling_service.devices.delete(account_id, user_id, device_id):
            self.set_user_action_event(BaseAccountActionEvent(account_id, description=f"Device {device_id} deleted"))
            flash(_("The device has been removed"), "success")
        else:
            flash(_("Error deleting device"), "error")

        return redirect(self.get_url(".info", id=account.id))
