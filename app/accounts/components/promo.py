import logging

from flask import (
    abort,
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from flask_security import current_user
from prebilling.accounts import Account

from app.accounts.activity.events import BaseAccountActionEvent
from app.auth import expose
from app.promo.proto.promo import (
    ExhaustingDetails,
    GrantDetails,
)
from app.proto.services import (
    AccountService,
    ResourcesService,
)
from app.purchases.models import Offer

from .base import Component

logger = logging.getLogger(__name__)


class Promo(Component):
    """Component for managing promo resources of an account."""

    extra_permissions = {"promo_resources_view", "promo_resources_edit"}

    template = "accounts/account_promo_resources.html"

    page_size = 10

    def __init__(self, prebilling_service=None, view_only=False, *args, **kwargs):
        self._prebilling_service = prebilling_service
        self._account_service = AccountService.get_instance()
        self._resources_service = ResourcesService.get_instance()
        self._view_only = view_only

        super().__init__(view_only=view_only, *args, **kwargs)

    @property
    def is_allowed(self):
        return current_user.is_allowed(self.endpoint, "promo_resources_view")

    def widget_args(self, account: Account) -> dict:
        view_only = self._view_only or not current_user.is_allowed(self.endpoint, "promo_resources_edit")

        try:
            resources = self._account_service.get_promo_resources_state_for_account(account_id=str(account.id))
            all_resources = {
                resource.resource_id: resource for resource in self._resources_service.list_resources().resources
            }

            available = resources.available_resources
            exhausted = resources.exhausted_resources
        except Exception as e:
            logger.error(f"Promo service error: {str(e)}")
            return dict(account=account, service_error=True, all_resources={})

        offers_ids = [
            r.details.exhausted_for_offer_id
            for r in list(exhausted)
            if hasattr(r, "details") and getattr(r.details, "exhausted_for_offer_id", None)
        ]
        offers = {str(o.id): o for o in Offer.objects(id__in=offers_ids)}

        search = request.values.get("promo_resources_search", "").lower()

        def apply_search(lst):
            return [r for r in lst if search in (r.resource_id or "").lower()]

        pagination = {}
        for rstate in ["available", "exhausted"]:
            page = request.args.get(f"{rstate}_page", 0, int)
            pagination[f"{rstate}_page"] = page

        return dict(
            offers=offers,
            account=account,
            view_only=view_only,
            available=apply_search(available),
            exhausted=apply_search(exhausted),
            all_resources=all_resources,
            promo_resources_search=search,
            page_size=self.page_size,
            **pagination,
        )

    @expose("/rollback_resource", perm="promo_resources_edit", methods=("GET",))
    def rollback_resource(self):
        self._view_only and abort(403)

        account = self.account_or_404()
        resource_id = request.values.get("resource_id")

        try:
            self._account_service.rollback_resources_exhaust(account_id=account.id, resources=[resource_id])
            self.set_user_action_event(
                BaseAccountActionEvent(
                    account.id, description=f"Promo resource {resource_id} is rollbacked for account {account.id}"
                )
            )

            flash(_("Promo-resource exhaustion is cancelled"), "success")
        except Exception as e:
            flash("{}: {}".format(_("Error cancelling promo-resource exhaustion"), e), "error")

        return redirect(self.get_url(".info", id=account.id))

    @expose("/exhaust_for_account", perm="promo_resources_edit", methods=("GET",))
    def exhaust_for_account(self):
        self._view_only and abort(403)

        account = self.account_or_404()
        resource_id = request.values.get("resource_id")

        try:
            self._account_service.exhaust_resources(
                account_id=account.id,
                resources_to_exhaust=[resource_id],
                details=ExhaustingDetails(exhaust_initiator=current_user.login),
            )
            self.set_user_action_event(
                BaseAccountActionEvent(
                    account.id, description=f"Promo resource {resource_id} is exhausted for account {account.id}"
                )
            )

            flash(_("Promo-resource is exhausted"), "success")
        except Exception as e:
            flash("{}: {}".format(_("Error exhausting promo-resource"), e), "error")

        return redirect(self.get_url(".info", id=account.id))

    @expose("/grant_for_account", perm="promo_resources_edit", methods=("GET",))
    def grant_for_account(self):
        self._view_only and abort(403)

        account = self.account_or_404()
        resource_id = request.values.get("resource_id")

        try:
            if resource_id:
                to_grant = [resource_id]
            else:
                resources = self._resources_service.list_resources().resources
                to_grant = [r.resource_id for r in resources if r.resource_type == "manual"]

            self._account_service.grant_resources(
                account_id=account.id,
                resources_to_grant=to_grant,
                details=GrantDetails(grant_initiator=current_user.login),
            )

            self.set_user_action_event(
                BaseAccountActionEvent(account.id, description=f"Promo resources are granted for account {account.id}")
            )
            flash("{}: {}".format(_("Promo-resources provided"), ", ".join(to_grant)), "success")
        except Exception as e:
            flash("{}: {}".format(_("Error providing promo-resource"), e), "error")

        return redirect(self.get_url(".info", id=account.id))
