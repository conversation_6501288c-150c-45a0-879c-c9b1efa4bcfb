from __future__ import annotations

import logging
from enum import Enum
from functools import lru_cache
from typing import Generator

from flask import (
    Response,
    current_app,
    request,
)
from flask_login import current_user
from prebilling.accounts import Account as PrebillingAccount
from requests import HTTPError

from app.accounts.components import Component
from app.auth import (
    ajax_expose,
    expose,
)
from app.cms.utils import (
    CmsJsonResponse,
    JsonAbort,
)
from app.proto.services import (
    EventsService,
    GenericSubscriptionsService,
    NotificationsService,
    SubscriptionsHistoryService,
    SubscriptionsService,
)
from app.stores.proto.stores import (
    AutoRenewStatus,
    EventRecord,
    HistoryRecord,
    Notification,
    RequestOptions,
    SortOptions,
    Subscription,
)

logger = logging.getLogger(__name__)


class _CommonUtils(Component):
    _notifications_service: NotificationsService  # Must be defined in final composition.
    _subscriptions_service: SubscriptionsService  # Must be defined in final composition.
    _subscriptions_history_service: SubscriptionsHistoryService  # Must be defined in final composition.
    _events_service: EventsService  # Must be defined in final composition.
    _generic_subscriptions_service: GenericSubscriptionsService  # Must be defined in final composition.

    def account_or_404(self) -> PrebillingAccount:
        """Same method, but with JsonAbort wrapper in case of fail."""
        account_id = request.args.get("id")
        if not account_id:
            raise JsonAbort(400, "'id' argument is required (Account ID).")

        try:
            account = self._prebilling_service.accounts.get(account_id)
        except HTTPError as err:
            if "404" in str(err):
                raise JsonAbort(404, f"Prebilling service responded with 404 on account request (id = {account_id}).")
            raise JsonAbort(500, f"Prebilling error: {err}")

        if not account:
            raise JsonAbort(404, f"Account with id='{account_id}' not found in prebilling service.")

        if self.provider and account.provider["id"] != self.provider.id:
            raise JsonAbort(403, "Account found, but it's provider ID does not match.")

        return account

    def subscription_or_404(self, account_id: str) -> Subscription:
        s_id = request.values.get("s_id")
        if not s_id:
            raise JsonAbort(404, "Argument 's_id' not provided.")

        all_subs = self._subscriptions_service.get_subscriptions(
            account_id=account_id,
            options=RequestOptions(limit=100, offset=0, sorting=SortOptions.DESC),
        ).subscriptions
        try:
            return next(s for s in all_subs if s.id == s_id)
        except StopIteration:
            raise JsonAbort(404, f"Subscription id = '{s_id}' for account id = '{account_id}' not found.")


class CommonTemplatesMixin(Component):
    no_data_template_name = "accounts/components/boxes/no_data.html"
    fail_template_name = "accounts/components/boxes/fail.html"
    pagination_template_name = "cmf/views/basic_ajax_pagination.html"

    @property
    @lru_cache
    def fail_response(self):
        return CmsJsonResponse.response(data={"html": self.render_fail()})

    @property
    @lru_cache
    def no_data_response(self):
        return CmsJsonResponse.response(data={"html": self.render_no_data()})

    def render_no_data(self) -> str:
        return self.render(self.no_data_template_name)

    def render_fail(self, debug_info: str = "") -> str:
        if current_app.config.get("DEBUG") and debug_info:
            return self.render(self.fail_template_name, debug_info=debug_info)
        return self.render(self.fail_template_name)

    def fail_json_response(self, debug_info: str = "") -> Response:
        """Shortcut."""
        return CmsJsonResponse.response(data={"html": self.render_fail(debug_info)})

    def render_pagination(
        self, *, endpoint: str, url_params: dict | None = None, current_page: int, has_more: bool
    ) -> str:
        """Render simple pagination with 'previous' and 'next' button, if possible.

        :param endpoint: endpoint for generating "next" and "prev" urls.
        :param url_params: any extra url parameters for generating "next" and "prev" urls.
        :param current_page:
        :param has_more:
        :return: rendered html
        """
        url_params = url_params or {}

        prev_url = self.get_url(endpoint, **url_params, page_number=current_page - 1) if current_page > 1 else ""
        next_url = self.get_url(endpoint, **url_params, page_number=current_page + 1) if has_more else ""

        if prev_url or next_url:
            return self.render(self.pagination_template_name, prev_url=prev_url, next_url=next_url)
        return ""


class _HistoryMixin(_CommonUtils, CommonTemplatesMixin, Component):
    __page_size = 5
    __page_template_name = "accounts/components/stores/subscription/history_table.html"

    def __get_data(self, subscription_id: str, page_number: int = 1) -> list[HistoryRecord]:
        options = RequestOptions(
            limit=self.__page_size + 1,  # request one extra, so we have an idea if there is more data after the page.
            offset=(page_number - 1) * self.__page_size,
            sorting=SortOptions.DESC,
        )
        result = self._subscriptions_history_service.get_subscription_history(
            subscription_id=subscription_id,
            options=options,
        )
        return result.records

    @expose("get_history_table", methods=("GET",))
    def get_history_table(self):
        s_id = request.args.get("s_id")
        page_number = request.args.get("page_number", 1, int)

        if not current_user.is_allowed(self.endpoint, "stores"):
            return self.fail_response

        # Get data or render fail page.
        try:
            data = self.__get_data(subscription_id=s_id, page_number=page_number)
        except Exception as e:
            logger.error(f"Stores service error: {str(e)}")
            return self.fail_json_response(debug_info=str(e))

        if has_more := len(data) > self.__page_size:
            data = data[:-1]

        html = self.render(self.__page_template_name, items=data)
        pagination = self.render_pagination(
            endpoint=".stores_get_history_table",
            url_params={"s_id": s_id},
            current_page=page_number,
            has_more=has_more,
        )
        return CmsJsonResponse.response(data={"html": html, "pagination": pagination})


class _NotificationsMixin(_CommonUtils, CommonTemplatesMixin, Component):
    __page_size = 5
    __page_template_name = "accounts/components/stores/subscription/notifications_table.html"

    def __get_data(self, subscription: Subscription, page_number: int = 1) -> list[Notification]:
        return self._notifications_service.get_notifications(
            transaction_id=subscription.transaction_id,
            payment_system_type=subscription.payment_system_type,
            options=RequestOptions(
                # Request one extra, so we have an idea if there is more data after the page.
                limit=self.__page_size + 1,
                offset=(page_number - 1) * self.__page_size,
                sorting=SortOptions.DESC,
            ),
        ).notifications

    @expose("get_notifications_table", methods=("GET",))
    def get_notifications_table(self):
        if not current_user.is_allowed(self.endpoint, "stores"):
            return self.fail_response
        account = self.account_or_404()
        subscription = self.subscription_or_404(account.id)

        try:
            page_number = request.args.get("page_number", 1, int)
            data = self.__get_data(subscription=subscription, page_number=page_number)
        except Exception as e:
            logger.error(f"Stores service error: {str(e)}")
            return self.fail_json_response(debug_info=str(e))

        if has_more := len(data) > self.__page_size:
            data = data[:-1]

        html = self.render(self.__page_template_name, items=data)
        pagination = self.render_pagination(
            endpoint=".stores_get_notifications_table",
            url_params={"s_id": subscription.id, "id": account.id},
            current_page=page_number,
            has_more=has_more,
        )
        return CmsJsonResponse.response(data={"html": html, "pagination": pagination})


class _EventsMixin(_CommonUtils, CommonTemplatesMixin, Component):
    __page_size = 5
    __page_template_name = "accounts/components/stores/subscription/events_table.html"

    def __get_data(self, s_id: str, page_number: int = 1) -> list[EventRecord]:
        return self._events_service.get_events_by_subscription_i_d(
            subscription_id=s_id,
            options=RequestOptions(
                # Request one extra, so we have an idea if there is more data after the page.
                limit=self.__page_size + 1,
                offset=(page_number - 1) * self.__page_size,
                sorting=SortOptions.DESC,
            ),
        ).records

    @expose("get_events_table", methods=("GET",))
    def get_events_table(self):
        s_id = request.args.get("s_id")
        page_number = request.args.get("page_number", 1, int)

        if not current_user.is_allowed(self.endpoint, "stores"):
            return self.render_fail()

        # Get data or render fail page.
        try:
            data = self.__get_data(s_id, page_number=page_number)
        except Exception as e:
            logger.error(f"Stores service error: {str(e)}")
            return self.fail_json_response(debug_info=str(e))

        if has_more := len(data) > self.__page_size:
            data = data[:-1]

        html = self.render(self.__page_template_name, items=data)
        pagination = self.render_pagination(
            endpoint=".stores_get_events_table",
            url_params={"s_id": s_id},
            current_page=page_number,
            has_more=has_more,
        )
        return CmsJsonResponse.response(data={"html": html, "pagination": pagination})


class SubscriptionActions(str, Enum):
    """Known actions with store subscription."""

    disable_renew = "disable_renew"
    enable_renew = "enable_renew"


class _SubscriptionsMixin(_CommonUtils, CommonTemplatesMixin, Component):
    __page_size = 5
    __page_template_name = "accounts/components/stores/account_store_subscriptions_table.html"

    def __get_data(self, account_id: str, page_number: int = 1) -> list[Subscription]:
        return self._subscriptions_service.get_subscriptions(
            account_id=account_id,
            options=RequestOptions(
                limit=self.__page_size + 1,
                offset=(page_number - 1) * self.__page_size,
                sorting=SortOptions.DESC,
            ),
        ).subscriptions

    @ajax_expose("get_subscriptions_table", methods=("GET",))
    def get_subscriptions_table(self):
        if not current_user.is_allowed(self.endpoint, "stores"):
            return self.render_fail()

        account = self.account_or_404()
        page_number = request.args.get("page_number", 1, int)

        # Get data or render fail page.
        try:
            data = self.__get_data(account.id, page_number=page_number)
        except Exception as e:
            logger.error(f"Stores service error: {str(e)}")
            return self.fail_json_response(debug_info=str(e))

        if has_more := len(data) > self.__page_size:
            data = data[:-1]

        provider_id = account.provider["id"]
        offers = self._prebilling_service.offers.list(provider_id)
        offers_map = {o.id: o for o in offers}
        html = self.render(
            self.__page_template_name,
            account=account,
            subscriptions=data,
            offers_map=offers_map,
            SubscriptionActions=SubscriptionActions,
        )

        pagination = self.render_pagination(
            endpoint=".stores_get_subscriptions_table",
            url_params={"id": account.id},
            current_page=page_number,
            has_more=has_more,
        )

        return CmsJsonResponse.response(data={"html": html, "pagination": pagination})

    def _get_allowed_actions(self, sub: Subscription) -> Generator[SubscriptionActions, None, None]:
        if sub.auto_renew_status == AutoRenewStatus.AR_STATUS_ACTIVE:
            if sub.payment_system_type in (current_app.config.get("SUBSCRIPTION_ALLOW_DISABLE_AUTORENEW") or []):
                yield SubscriptionActions.disable_renew

        if sub.auto_renew_status == AutoRenewStatus.AR_STATUS_NOT_ACTIVE:
            if sub.payment_system_type in (current_app.config.get("SUBSCRIPTION_ALLOW_ENABLE_AUTORENEW") or []):
                yield SubscriptionActions.enable_renew

    def has_allowed_actions(self, sub: Subscription):
        # This method will be used in template code.
        for _ in self._get_allowed_actions(sub):
            return True

    def is_action_allowed(self, sub: Subscription, action: SubscriptionActions) -> bool:
        # This method will be used in template code.
        return action in self._get_allowed_actions(sub)

    def can_be_re_enabled(self, sub: Subscription):
        # This method will be used in template code.
        return sub.payment_system_type in (current_app.config.get("SUBSCRIPTION_ALLOW_ENABLE_AUTORENEW") or [])

    @ajax_expose("subscription-disable-autorenew", perm="stores_manage_subscriptions", methods=("POST",))
    def disable_autorenew(self):
        account = self.account_or_404()
        s_id = request.values.get("s_id") or JsonAbort(403, "Argument 's_id' not provided.")

        subscription = self.subscription_or_404(account.id)
        if not self.is_action_allowed(subscription, SubscriptionActions.disable_renew):
            return CmsJsonResponse.error(message="Automatic renew for this subscription cannot be disabled.")

        self._generic_subscriptions_service.disable_autorenew(subscription_id=s_id, account_id=account.id)
        return CmsJsonResponse.success(message="Subscription automatic renew has been disabled.")

    @ajax_expose("subscription-enable-autorenew", perm="stores_manage_subscriptions", methods=("POST",))
    def enable_autorenew(self):
        account = self.account_or_404()
        s_id = request.values.get("s_id") or JsonAbort(403, "Argument 's_id' not provided.")

        subscription = self.subscription_or_404(account.id)
        if not self.is_action_allowed(subscription, SubscriptionActions.enable_renew):
            return CmsJsonResponse.error(message="Automatic renew for this subscription cannot be (re)enabled.")

        self._generic_subscriptions_service.enable_autorenew(subscription_id=s_id, account_id=account.id)
        return CmsJsonResponse.success(message="Subscription automatic renew has been (re)enabled.")
