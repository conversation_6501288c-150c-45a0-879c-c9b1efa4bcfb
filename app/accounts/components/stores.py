from __future__ import annotations

import logging

from flask import (
    abort,
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from flask_security import current_user

from app.accounts.activity.events import BaseAccountActionEvent
from app.auth import expose
from app.common.protobuf import to_dict as pb2dict
from app.proto.services import (
    EventsService,
    GenericSubscriptionsService,
    NotificationsService,
    SubscriptionsHistoryService,
    SubscriptionsService,
)

from .base import Component
from .stores_boxes import (
    _CommonUtils,
    _EventsMixin,
    _HistoryMixin,
    _NotificationsMixin,
    _SubscriptionsMixin,
)

logger = logging.getLogger(__name__)


class Stores(
    _SubscriptionsMixin,
    _EventsMixin,
    _HistoryMixin,
    _NotificationsMixin,
    _CommonUtils,
    Component,
):
    """Component for Stores service integration.

    It is an interface for rendering android subscriptions for an account.

    Required permission to use the component is `stores`.
    """

    extra_permissions = {"stores"}

    template = "accounts/components/stores/account_store_subscriptions_box.html"
    subscription_template = "accounts/components/stores/subscription/subscription_page.html"
    event_template = "accounts/components/stores/account_stores_event.html"

    subscriptions_page_size = 5

    def __init__(self, prebilling_service=None, view_only=False, *args, **kwargs):
        self._prebilling_service = prebilling_service
        self._notifications_service = NotificationsService.get_instance()
        self._subscriptions_service = SubscriptionsService.get_instance()
        self._subscriptions_history_service = SubscriptionsHistoryService.get_instance()
        self._generic_subscriptions_service = GenericSubscriptionsService.get_instance()
        self._events_service = EventsService.get_instance()
        self._view_only = view_only
        super().__init__(*args, **kwargs)

    @property
    def is_allowed(self):
        return current_user.is_allowed(self.endpoint, "stores")

    @expose("/stores/subscription-details", perm="stores", methods=("GET",))
    def subscription_details(self):
        account = self.account_or_404()

        s_id = request.values.get("s_id")

        if not s_id:
            abort(404)

        subscription = self.subscription_or_404(account.id)

        provider_id = account.provider["id"]
        offers = self._prebilling_service.offers.list(provider_id)
        offers_map = {o.id: o for o in offers}

        return self.render(
            self.subscription_template,
            account=account,
            offers_map=offers_map,
            subscription=subscription,
            pb2dict=pb2dict,
        )

    @expose("/stores/update-subscription-state", perm="stores", methods=("GET",))
    def update_subscription_state(self):
        account = self.account_or_404()

        s_id = request.values.get("s_id")

        if not s_id:
            abort(404)

        subscription = self.subscription_or_404(account.id)

        try:
            self._stores_service.update_subscription_state(
                s_id, payment_system_type=subscription.payment_system_type, initiator=current_user.login
            )
        except Exception as e:
            logger.error(f"Stores service error: {str(e)}")
            flash(_("Failed to update subscription status"), "error")
        else:
            self.set_user_action_event(
                BaseAccountActionEvent(account.id, description=f"Subscription {subscription} state updated")
            )
            flash(_("Subscription status updated"), "success")

        return redirect(self.get_url(".stores_subscription_details", id=account.id, s_id=s_id))

    @expose("/stores/event_details", perm="stores", methods=("GET",))
    def event_details(self):
        account = self.account_or_404()

        event_id = request.values.get("e_id")

        if not event_id:
            abort(404)

        event = self._stores_service.get_event(event_id)

        provider_id = account.provider["id"]
        offers = self._prebilling_service.offers.list(provider_id)
        offers_map = {o.id: o for o in offers}

        return self.render(
            self.event_template,
            account=account,
            offers_map=offers_map,
            event=event,
            pb2dict=pb2dict,
        )
