from .base import Component


class BlockingHistory(Component):
    """Component for representing blocking history of an account."""

    account_template = "accounts/account_blocking_history.html"

    def __init__(self, prebilling_service=None, *args, **kwargs):
        self._prebilling_service = prebilling_service
        super().__init__(*args, **kwargs)

    @property
    def is_allowed(self):
        account = self.account_or_404()
        return account.is_blocked
