<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">{{ _("Purchases") }}</h3>
    </div>
    <div class="box-body">
        {% if subscriptions|length > 0 %}
            <table class="table table-bordered small-font">
                <tbody>
                    <tr>
                        <th>{{ _("Name") }}</th>
                        <th>{{ _("Status") }}</th>
                        <th>{{ _("Offer type") }}</th>
                        <th>{{ _("Created at") }}</th>
                        <th>{{ _("Expiration time") }}</th>
                        {% if current_user.is_allowed(admin_view.endpoint, 'disable_subscription') and not view_only %}
                            <th></th>
                        {% endif %}
                    </tr>
                    {% for s in subscriptions %}
                        <tr data-purchase-id={{s.id}}>
                            <td>
                                {% if cms %}
                                    {% set committed_at = s.committed_at|localize_prebilling_datetime or s.created|localize_prebilling_datetime %}
                                    <span data-toggle="tooltip" data-placement="bottom" style="color: {{'grey' if s.is_uncommitted else 'green'}}"
                                          data-original-title="{{ pgettext("purchase", "Not confirmed") if s.is_uncommitted else pgettext("purchase", "Confirmed") + ' ' + committed_at }}">
                                        <i class="fa fa-check-circle{{ '' if s.is_uncommitted else '-o' }}"></i>
                                    </span>
                                {% endif %}
                                {% set show_details = current_user.is_allowed(admin_view.endpoint, 'subscription_details') and not hide_subscription_details and not view_only %}
                                {% if show_details %} <a href="{{ url_for(endpoint + '.subscriptions_subscription_details', id=account.id, s_id=s.id) }}"> {% endif %}
                                    {% if s.offer_id in offers_map %}
                                        {% set offer = offers_map[s.offer_id] %}
                                        {{ admin_view.format_offer(offer) }}
                                    {% else %}
                                        {{ s.offer_id }}
                                    {% endif %}
                                    {% if cms %}
                                        <a title="{{ _("Offer") }}" href="{{ url_for('offer.edit_view', id=s.offer_id) }}">
                                            <i class="fa fa-link"></i>
                                        </a>
                                    {% endif %}
                                {% if show_details %} </a> {% endif %}
                            </td>
                            <td>
                                {% if s.state == "ACTIVE" %}
                                    <span class="badge bg-green">{{ _("Active") }}</span>
                                {% elif s.state == "SUSPENDED" %}
                                    <span class="badge bg-yellow">{{ _("Suspended") }}</span>
                                {% elif s.state == "DISABLED" %}
                                    <span class="badge bg-red">{{ _("Disabled") }}</span>
                                {% endif %}

                                {% if s.change_info.state == 4 %}
                                    <span class="badge bg-red" title="{{ _("Will be cancelled") }} {{ s.change_info.scheduled|localize_prebilling_datetime if s.change_info.scheduled else ""}}">
                                        <i class="fa fa-calendar-times-o"></i>
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if s.offer_id in offers_map %}
                                    {{ offers_map[s.offer_id].typ }}
                                {% endif %}
                            </td>
                            <td>
                                {{ s.created|localize_prebilling_datetime }}
                            </td>
                            <td>
                                {{ s.expire_time|localize_prebilling_datetime }}
                            </td>
                            {% if admin_view.cancelling_allowed(offers_map[s.offer_id]) %}
                                <td>
                                    <a href="{{ url_for(endpoint + '.subscriptions_disable_subscription', id=account.id, offer_id=s.offer_id, s_id=s.id) }}"
                                       class="btn btn-danger btn-xs">{{ _("Disable") }}</a>
                                </td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p class="text-muted text-center">{{ _("There are no purchases right now") }}</p>
        {% endif %}
    </div>
    <div class="box-footer">
        {% if current_user.is_allowed(admin_view.endpoint, 'manage_subscription') and not view_only %}
           <a href="{{ url_for(endpoint+'.subscriptions_manage_subscription', id=account.id) }}" class="btn btn-success">{{ _("Add purchase") }}</a>
        {% endif %}
        {% if current_user.is_allowed(admin_view.endpoint, 'subscription_history') %}
            <a href="{{ url_for(endpoint+'.subscriptions_subscriptions_history', id=account.id) }}" class="btn btn-default pull-right">{{ _("Purchase history") }}</a>
        {% endif %}
    </div>
</div>
