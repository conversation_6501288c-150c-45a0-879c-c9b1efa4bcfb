{% macro resources_table(_resources, caption, state, page=0, exhausted=False, rollback=Fale) %}
    {% set frm = page * page_size %}

    {% set resources = _resources[frm:frm + page_size] %}
    {% set more = _resources[frm + page_size + 1:] %}

    {% set show_rollback = rollback and current_user.is_allowed(admin_view.endpoint, 'promo_resources_edit') and not view_only %}

    {% if resources|length > 0 %}
        <table class="table table-bordered small-font resources-table">
            <tbody>
                <caption>
                    <span class="align-left"><strong>{{ caption }}</strong></span>
                </caption>
                <tr>
                    <th>ID</th>
                    <th>{{ _("Type") }}</th>
                    <th>{{ _("Granted at") }}</th>
                    <th>{{ _("Initiator") }}</th>
                    {% if exhausted %}
                        <th>{{ _("Exhausted") }}</th>
                        <th>{{ _("Offer") }}</th>
                        {% if show_rollback %}
                            <th></th>
                        {% endif %}
                    {% else %}
                        <th></th>
                    {% endif %}
                </tr>
                {% for r in resources %}
                    {% set original = all_resources.get(r.resource_id) %}
                    <tr data-resource-id={{r.resource_id}}>
                        <td class="col-md-3">{{ r.resource_id }}</td>
                        <td class="col-md-3">{{ original.resource_type if original.resource_type else "n/a" }}</td>
                        <td class="col-md-3">{{ r.granted_at|localize_datetime if r.granted_at else "n/a" }}</td>
                        {% if exhausted %}
                            <td class={{ "col-md-2" if show_rollback else "col-md-4" }}>{{ r.details.exhaust_initiator }}</td>
                            <td class="col-md-3">{{ r.exhaust_time|localize_datetime }}</td>
                            <td class="col-md-3">
                                {% set offer = offers.get(r.details.exhausted_for_offer_id) %}
                                {% if offer %}
                                    <a href="{{ url_for("offer.edit_view", id=offer.id) }}">{{ admin_view.format_offer(offer) }}</a>
                                {% else %}
                                    n/a
                                {% endif %}
                            </td>
                            {% if show_rollback %}
                                <td class="col-md-1" style="width: 100px">
                                    <a href="{{ url_for(endpoint + ".promo_rollback_resource", id=account.id, resource_id=r.resource_id) }}"
                                       onclick="return confirm('{{ _("Are you sure you want to roll back the exhausted resource?") }}')"
                                       title="Rollback Exhaust" class="btn btn-danger btn-xs">{{ _("Roll back") }}</a>
                                </td>
                            {% endif %}
                        {% else %}
                            <td class={{ "col-md-2" if show_rollback else "col-md-4" }}>{{ r.details.grant_initiator }}</td>
                            <td class="col-md-1" style="width: 100px">
                                <a href="{{ url_for(endpoint + ".promo_exhaust_for_account", id=account.id, resource_id=r.resource_id) }}"
                                   onclick="return confirm('{{ _("Are you sure you want to exhaust the resource?") }}')"
                                   title="Exhaust" class="btn btn-danger btn-xs">{{ _("Exhaust") }}</a>
                            </td>
                        {% endif %}
                    </tr>
                {% endfor %}
                {% if frm > 0 or more %}
                    <tr>
                        <td colspan="100">
                            <ul class="pagination no-margin pull-right">
                                {% if frm > 0 %}
                                    <li class="paginate_button previous">
                                        {% set params = {state + "_page": page - 1, "promo_resources_search": promo_resources_search} %}
                                        <a href="{{ url_for(endpoint + '.info', id=account.id, **params)}}">
                                            <i class="fa fa-long-arrow-left" aria-hidden="true"></i>
                                        </a>
                                    </li>
                                {% endif %}

                                {% if more %}
                                    <li class="paginate_button next">
                                        {% set params = {state + "_page": page + 1, "promo_resources_search": promo_resources_search} %}
                                        <a href="{{ url_for(endpoint + '.info', id=account.id, **params) }}">
                                            <i class="fa fa-long-arrow-right" aria-hidden="true"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    {% endif %}
{% endmacro %}


<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">{{ _("Promo resources") }}</h3>
        <div style="position: absolute; top: 5px; right: 5px;">
            {% if not service_error %}
                <div class="pull-right">
                    {% set available_ids = available|map(attribute="resource_id")|list %}
                    {% set exhausted_ids = exhausted|map(attribute="resource_id")|list %}
                    <form action="{{ url_for(endpoint + '.promo_grant_for_account') }}" method="GET" class="btn-group form-inline" style="display: inline-block;">
                        <input type="hidden" name="id" value="{{ account.id }}" />
                        <select onchange="confirm('{{ _("Are you sure you want to grant the resource?") }}') && this.form.submit()" class="form-control input-sm"
                                name="resource_id" data-role="select2" title="{{ _("Grant") }}" placeholder="{{ _("Grant") }}">
                            <option value="" disabled selected>{{ _("Grant") }}</option>
                            {% for id, resource in all_resources.items() %}
                                {% if resource.resource_type == "manual" and id not in available_ids %}
                                    <option value="{{ resource.resource_id }}">{{ id }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </form>
                    <form action="{{ url_for(endpoint + '.promo_exhaust_for_account') }}" method="GET" class="btn-group form-inline" style="display: inline-block;">
                        <input type="hidden" name="id" value="{{ account.id }}" />
                        <select onchange="confirm('{{ _("Are you sure you want to exhaust the resource?") }}') && this.form.submit()" class="form-control input-sm"
                                name="resource_id" data-role="select2" title="{{ _("Exhaust") }}" placeholder="{{ _("Exhaust") }}">
                            <option value="" disabled selected>{{ _("Exhaust") }}</option>
                            {% for id, resource in all_resources.items() %}
                                {% if resource.resource_type == "trial" and id not in exhausted_ids %}
                                    <option value="{{ resource.resource_id }}">{{ id }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </form>
                    <form action="{{ url_for(endpoint + '.info') }}" method="GET" class="btn-group form-inline" style="display: inline-block;">
                        <input type="hidden" name="id" value="{{ account.id }}" />
                        <div class="input-group" style="width: 150px;">
                            <input class="form-control input-sm" name="promo_resources_search" title="{{ _("Search") }}" placeholder="{{ _("Search") }}" style="line-height: 1.5; padding-top: 4px;"
                                   id="search_form" value="{{ promo_resources_search }}" onkeydown="if (event.keyCode == 13) { document.forms.search_form.submit() }" />
                            {% if promo_resources_search %}
                                <span class="input-group-btn">
                                    <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-sm btn-default"><i class="fa fa-times"></i></a>
                                </span>
                            {% endif %}
                        </div>
                    </form>
                </div>
            {% endif %}
        </div>
    </div>
    <div class="box-body">
        {% if service_error %}
            <p class="text-muted text-center" style="color: red">{{ _("Unable to retrieve information about promo resources") }}</p>
        {% else %}

            {{ resources_table(available, _("Available resources"), "available", page=available_page) }}
            {{ resources_table(exhausted, _("Exhausted resources"), "exhausted", page=exhausted_page, exhausted=True, rollback=True) }}

            {% if not (available|length > 0 or exhausted|length > 0) %}
                <p class="text-muted text-center">{{ _("There are no resources at the moment") }}</p>
            {% endif %}
        {% endif %}
    </div>
</div>
