{% from 'cmf/utils.html' import modal, simple_block %}

{% macro render_details_row(label, value) %}
    <tr>
        <td>{{ label }}</td>
        <td>{{ value }}</td>
    </tr>
{% endmacro %}

{% macro render_details_block(title, info) %}
    {# info is expected to be `dict` #}
    {% call simple_block(title, is_collapsed=True) %}
        <table class="table">
            {% for key, value in info.items()|sort(attribute=0) %}
                <tr>
                    <td>{{ key }}</td>
                    <td style="word-break: break-all">{{ value }}</td>
                </tr>
            {% endfor %}
        </table>
    {% endcall %}
{% endmacro %}

{% macro render_details_modal(session) %}
    {% set modal_id = "session_details_" + session.id %}
    <span class="btn btn-xs btn-primary" data-toggle="modal" data-target="#{{ modal_id }}" title="{{ _("Details") }}">
        <i class="glyphicon glyphicon-eye-open"></i>
    </span>
    {% call modal(modal_id, "Session details", large=True) %}
        <table class="table table-bordered table-striped">
            {{ render_details_row("ID", session.id) }}
            {{ render_details_row("Account", session.account) }}
            {{ render_details_row("TTL", session.ttl) }}
            {{ render_details_row("Last touch", session.last_touch) }}
            {{ render_details_row("Created at", session.created_at) }}
            {{ render_details_row("Evicted Session ID", session.evicted_session_id) }}
            {{ render_details_row("Previous sessions count", session.previous_sessions_count) }}
            {{ render_details_row("Current sessions count", session.current_sessions_count) }}
            {{ render_details_row("Max sessions num", session.max_sessions_num) }}
        </table>
        {{ render_details_block("Last use with client", session.last_touched_client_info) }}
        {{ render_details_block("Created with client", session.client_info) }}
    {% endcall %}
{% endmacro %}

<div class="box box-primary sessions">
    <div class="box-header with-border">
        <h3 class="box-title">{{ _("Active sessions") }}</h3>
    </div>
    <div class="box-body">
        {% if sessions is none %}
            <p class="text-muted text-center" style="color: red">{{ _("Cannot retrieve information about sessions") }}</p>
        {% elif sessions|length > 0 %}
        <table class="table table-bordered small-font">
            {% set can_delete = current_user.is_allowed(admin_view.endpoint, 'delete_sessions') and not view_only %}
            <tbody>
                <tr>
                    <th>{{ _("Creation date") }}</th>
                    <th>{{ _("Last used date") }}</th>
                    <th>{{ _("Device") }}</th>
                    <th>{{ _("Application") }}</th>
                    <th>{{ _("IP address") }}</th>
                    <th>{# details button #}</th>
                    {% if can_delete %}
                        <td>
                            <a href="{{ url_for(endpoint + '.sessions_delete_session', id=account.id, all=1) }}"
                               class="btn btn-danger btn-xs"
                               title="{{ _("Delete all") }}"
                               onclick="return confirm('{{ _("Are you sure you want to delete ALL sessions?") }}')"
                            >
                                <i class="glyphicon glyphicon-trash"></i>
                            </a>
                        </td>
                    {% endif %}
                </tr>
                {% for session in sessions %}
                <tr data-session-id={{session.id}}>
                    <td>
                        {{ session.created_at|localize_datetime }}
                    </td>
                    <td>
                        {{ session.last_touch|localize_datetime }}
                    </td>
                    <td>
                        {{ render_client_info_labels(session.last_touched_client_info, 'device') }}
                    </td>
                    <td>
                        {{ render_client_info_labels(session.last_touched_client_info, 'app') }}
                    </td>
                    <td>
                        {{ session.ip }}
                    </td>
                    <td>
                        {{ render_details_modal(session) }}
                    </td>
                    {% if can_delete %}
                        <td>
                            <a href="{{ url_for(endpoint + '.sessions_delete_session', id=account.id, session_id=session.id) }}"
                               class="btn btn-danger btn-xs"
                               title="{{ _("Delete") }}"
                               onclick="return confirm('{{ _("Are you sure you want to delete the session?") }}')"
                            >
                                <i class="glyphicon glyphicon-trash"></i>
                            </a>
                        </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
            <p class="text-muted text-center">{{ _("There are no active sessions at the moment.") }}</p>
        {% endif %}
    </div>
    {% if current_user.is_allowed(admin_view.endpoint, 'authhistory') %}
    <div class="box-footer">
        <a href="{{ url_for(endpoint + '.sessions_authhistory', id=account.id) }}" class="btn btn-default pull-right">{{ _("Session history") }}</a>
    </div>
    {% endif %}
</div>
