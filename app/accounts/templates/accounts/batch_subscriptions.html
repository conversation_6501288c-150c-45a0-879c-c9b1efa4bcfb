{% extends admin_base_template %}


{% block head_css %}
    {{ super() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.3/daterangepicker.min.css" rel="stylesheet">
{% endblock %}


{% block body %}
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <form method="post" class="form form-horizontal" role="form">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                {% if action == "create" %}
                                    {{ pgettext("box title", "Create purchase") }}
                                {% else %}
                                    {{ pgettext("box title", "Cancel purchase") }}
                                {% endif %}
                            </h3>
                        </div>
                        <div class="box-body">
                            {% set action_text = (_("Create") if action == "create" else pgettext("action button", "Cancel")) %}
                            {% set count = account_ids|length %}
                            <p>
                                {{ ngettext(
                                    "{action_text} subscription {offer} for {count} account?",
                                    "{action_text} subscription {offer} for {count} accounts?",
                                    count
                                ).format(
                                    action_text=action_text,
                                    offer=admin_view.format_offer(offer),
                                    count=count
                                ) }}
                            </p>
                            {% for field in form %}
                                {{ field() }}
                            {% endfor %}
                        </div>
                        <div class="box-footer">
                            <button type="submit" class="btn btn-success">{{ action }}</button>
                            <a href="{{ url_for(endpoint+'.index') }}" class="btn btn-default pull-right">{{ _("Back") }}</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
{% endblock %}


{% block tail_js %}
    {{ super() }}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.3/daterangepicker.min.js"></script>
    <script>
        $(function() {
            $("#expire_time").daterangepicker({
                timePicker12Hour: false,
                timePicker24Hour: true,
                singleDatePicker: true,
                autoUpdateInput: false,
                timePickerIncrement: 1,
                timePicker: true,
                minDate: moment(),
                firstDay: 1,
                locale: {
                    customRangeLabel: '{{ _("Select manually") }}',
                    cancelLabel: '{{ _("Clear") }}',
                    applyLabel: '{{ _("Choose") }}',
                }
            }).on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('DD.MM.YY HH:mm'));
            }).on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        });
    </script>
{% endblock %}
