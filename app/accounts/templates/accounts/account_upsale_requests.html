{% set STATUS_TEXT = {
    0: _("Unknown"),
    1: _("Processing"),
    2: _("Purchase successful"),
    3: _("Purchase error"),
    4: _("Synchronization error"),
    5: _("Synchronization successful"),
}%}
{% set STATUS_COLOR = {
    0: "bg-default",
    1: "bg-yellow",
    3: "bg-red",
    4: "bg-red",
    5: "bg-green"
}%}


<div class="box box-primary subscriptions">
    <div class="box-header with-border">
        <h3 class="box-title">{{ _("Upsale requests") }}</h3>
    </div>
    <div class="box-body">
        {% if service_error %}
            <p class="text-muted text-center" style="color: red">{{ _("Unable to retrieve information about Upsale service requests") }}</p>
        {% elif requests|length > 0 %}
        <table class="table table-bordered small-font">
            <tbody>
                <tr>
                    <th>{{ pgettext("upsale-requests", "Request") }}</th>
                    <th>{{ _("Provider") }}</th>
                    <th>{{ _("Status") }}</th>
                    <th>{{ _("Attempt") }}</th>
                </tr>
                {% for r in requests %}
                    <tr data-request-id="{{r.qs_transaction_id}}">
                        {% set e = extra_info(r) %}
                        <td>
                            <a href="{{ url_for(endpoint + '.upsale_request_details', id=account.id, r_id=r.qs_transaction_id) }}">
                                {% if e.offer and e.offer.name %}
                                    {{ admin_view.format_offer(e.offer) }}
                                {% else %}
                                    {{ r.qs_transaction_id }}
                                {% endif %}
                            </a>

                            {% if e.offer %}
                                <a title="{{ _("Offer") }}" href="{{ url_for('offer.edit_view', id=e.offer.id) }}">
                                    <i class="fa fa-link"></i>
                                </a>
                            {% endif %} 
                        </td>
                        <td>
                            {% if e.provider %}
                                <a title="{{ _("Provider") }}" href="{{ url_for('provider.edit_view', id=e.provider.id) }}">{{e.provider.name}}</a>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge {{STATUS_COLOR.get(r.status)}}">{{STATUS_TEXT.get(r.status)}}</span>
                        </td>
                        <td>
                            {{ r.retry_counter }}
                            <span class="pull-right">
                                <a title="{{ _("Archive") }}" href="{{ url_for(endpoint + '.upsale_archive_request', id=account.id, r_id=r.qs_transaction_id) }}"
                                    onclick="return confirm('{{ _("Are you sure you want to archive the request?") }}')">
                                    <i class="fa fa-archive"></i>
                                </a>
                                {% if r.status == 2 or r.status == 3 or r.status == 4 %}
                                    <a title="{{ _("Retry") }}" href="{{ url_for(endpoint + '.upsale_retry_request', id=account.id, r_id=r.qs_transaction_id) }}"
                                        onclick="return confirm('{{ _("Are you sure you want to repeat your request?") }}')">
                                        <i class="fa fa-refresh"></i>
                                    </a>
                                {% endif %}
                            </span>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
            <p class="text-muted text-center">{{ _("There are currently no active requests for the Upsale service") }}</p>
        {% endif %}
    </div>
    <div class="box-footer">
        <ul class="pagination no-margin pull-right">
            {% if upsale_page %}
                <li class="paginate_button previous">
                    <a href="{{ url_for(endpoint + '.info', id=account.id, upsale_page=upsale_page - 1) }}">
                        <i class="fa fa-long-arrow-left" aria-hidden="true"></i>
                    </a>
                </li>
            {% endif %}

            {% if upsale_more %}
                <li class="paginate_button next">
                    <a href="{{ url_for(endpoint + '.info', id=account.id, upsale_page=upsale_page + 1) }}">
                        <i class="fa fa-long-arrow-right" aria-hidden="true"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </div>
</div>
