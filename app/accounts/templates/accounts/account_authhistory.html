{% extends 'accounts/info.html' %}


{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">{{ _("Session history") }}</h3>
            </div>
            <div class="box-body">
                {% if history is none %}
                    <p class="text-muted text-center" style="color: red">{{ _("Unable to retrieve saved session information") }}</p>
                {% elif history|length > 0 %}
                <table class="table table-bordered small-font">
                    <tbody>
                        <tr>
                            <th>{{ _("Active") }}</th>
                            <th>{{ _("Creation date") }}</th>
                            <th>{{ _("Device") }}</th>
                            <th>{{ _("Application") }}</th>
                            <th>UserAgent</th>
                            <th>{{ _("Authentication method") }}</th>
                            <th>{{ _("IP address") }}</th>
                            <th>{{ _("Credentials") }}</th>
                            {% if can_delete %}
                                <th></th>
                            {% endif %}
                        </tr>
                        {% for history_item in history %}
                            <tr data-history-id={{ history_item.session_id }}>
                                <td>
                                    {% if history_item.session_id in (sessions or []) %}
                                        {{ _('yes') }}
                                    {% else %}
                                        {{ _('no') }}
                                    {% endif %}
                                </td>
                                <td>
                                    {{ history_item.session_created_at|localize_prebilling_datetime }}
                                </td>
                                <td>
                                    {{ render_client_info_labels(history_item.client_info, 'device') }}
                                </td>
                                <td>
                                    {{ render_client_info_labels(history_item.client_info, 'app') }}
                                </td>
                                <td>
                                    {{ history_item.user_agent }}
                                </td>
                                <td>
                                    {{ history_item.auth_method }}
                                </td>
                                <td>
                                    {{ history_item.ipAddress }}
                                </td>
                                <td>
                                    {{ history_item.authed_with_credential }}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                    <p class="text-muted text-center">{{ _("There are no saved user sessions") }}</p>
                {% endif %}
            </div>
            <div class="box-footer">
                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default">{{ _("Back") }}</a>

                <ul class="pagination no-margin pull-right">
                    {% if frm > 0 %}
                        <li class="paginate_button previous">
                            <a href="{{ url_for(endpoint + '.sessions_authhistory', id=account.id, page=page - 1) }}">
                                <i class="fa fa-long-arrow-left" aria-hidden="true"></i>
                            </a>
                        </li>
                    {% endif %}

                    {% if more %}
                        <li class="paginate_button next">
                            <a href="{{ url_for(endpoint + '.sessions_authhistory', id=account.id, page=page + 1) }}">
                                <i class="fa fa-long-arrow-right" aria-hidden="true"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>

            </div>
        </form>
    </div>
{% endblock %}
