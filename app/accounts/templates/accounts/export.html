{% extends admin_base_template %}

{% import 'common/utils.html' as utils %}
{% import 'admin/model/cms_layout.html' as cms_model_layout with context %}

{% macro status_label(export_item) %}
    {% with status=export_item.status %}
        {% include 'cms/tasks/task_status_label.html' %}
    {% endwith %}
{% endmacro %}

{% macro render_row(export_item) %}
    <tr>
        <td>
            <a class="btn btn-danger btn-xs" href="{{ url_for('.export_delete', export_id=export_item.id, page=page) }}">
                <span class="fa fa-trash glyphicon glyphicon-trash"></span>
            </a>
        </td>
        <td>
            <a href="{{ url_for("provider.edit_view", id=export_item.provider.id) }}">{{ export_item.provider }}</a>
        </td>
        <td>{{ export_item.created_at|localize_datetime }}</td>
        <td>
            {{ status_label(export_item) }}
        </td>
        <td>{{ export_item.finished_at|localize_datetime }}</td>
        <td>
            {% if export_item.status|lower == 'done' %}
                <nobr>
                    <a href="{{ url_for('.export_download', export_id=export_item.id) }}">
                        <i class="fa fa-download"></i> Download
                    </a>
                </nobr>
            {% endif %}
        </td>
    </tr>
{% endmacro %}

{% block body %}
    <section class="content-header">
        <h1>Accounts export</h1>
    </section>

    <section class="content">
        <div class="row">
            <div class="col-sm-12">
                <div class="box">
                    <form method="post" class="form form-horizontal" role="form">
                        <input type="hidden" name="page" value="{{page}}"/>
                        <div class="box-body">
                            {% for field in form %}
                                {{ field()|safe }}
                                {% if h.is_field_error(field.errors) %}
                                    <ul class="help-block input-errors">
                                        {% for e in field.errors if e is string %}
                                        <li>{{ e }}</li>
                                        {% endfor %}
                                    </ul>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <div class="box-footer">
                            <button type="submit" class="btn btn-primary">Export</button>
                        </div>
                    </form>
                </div>
               {% if exports %}
                    <div class="box box-primary">
                        <div class="box-header">
                            <h3 class="box-title">Exports</h3>
                        </div>
                        <div class="box-body no-padding">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>Provider</th>
                                        <th>Requested</th>
                                        <th>Status</th>
                                        <th>Finished</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for export_item in exports %}
                                        {{ render_row(export_item) }}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="box-footer clearfix">
                            {{ utils.render_pagination(exports_paginator) }}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </section>

    {% block select_providers_dlg %}
        {{ cms_model_layout.select_providers_dlg() }}
    {% endblock %}

{% endblock %}
