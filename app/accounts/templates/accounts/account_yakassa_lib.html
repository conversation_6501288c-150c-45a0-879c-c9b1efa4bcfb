{% macro payment_method(method) %}
    {% set card = method.card  %}
    {% set num = method.title.split("*")[-1] if method.title else '' %}

    {% if card.expiry_month and card.expiry_year %}
        {% set expiration = card.expiry_month + '/' + card.expiry_year %}
    {% endif %}

    {{ _("card") }} {{ card.card_type }} {{ num }} {{ expiration }}
{% endmacro %}


{% macro render_dict(d) %}
    {% for key, value in d.items() %}
        {% if value %}
            <span class="label label-default" title="{{ key }}">{{ value }}</span>
        {% endif %}
    {% endfor %}
{% endmacro %}
