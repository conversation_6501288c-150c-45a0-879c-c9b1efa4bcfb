{% extends 'accounts/info.html' %}


{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">{{ _("Account unlinking") if unlink else _("Account deletion") }}</h3>
            </div>
            <div class="box-body">
                <h4>
                    {% if unlink %}
                        {{ _("Are you sure you want to unlink %(login)s and all its subscriptions?") % {'login': account.login} }}
                    {% else %}
                        {{ _("Are you sure you want to delete %(login)s account and all its subscriptions?") % {'login': account.login} }}
                    {% endif %}
                </h4>
                <br/>
                {% if config['PURCHASES_INTEGRATION'] %}
                <div class="form-group">
                    <input id="skip-integrations" name="skip-integrations" type="checkbox" value="y" checked>
                    <label for="skip-integrations" class="control-label">{{ _("Ignore integrations") }}</label>
                </div>
                {% endif %}
                {% if config['PURCHASES_NOTIFICATION'] %}
                    <div class="form-group">
                        <input id="disable-notifications" name="skip-notifications" type="checkbox" value="y" checked>
                        <label for="disable-notifications" class="control-label">{{ _("Do not send notifications") }}</label>
                    </div>
                {% endif %}
            </div>
            <div class="box-footer">
                <button type="submit" class="btn btn-danger">{{ _('Unlink') if unlink else _('Delete') }}</button>

                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default pull-right">{{ _("Cancel") }}</a>
            </div>
        </form>
    </div>
{% endblock %}
