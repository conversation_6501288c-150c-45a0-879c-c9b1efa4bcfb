<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">{{ _("Extra info") }}</h3>
        {% if account.provider_info %}
        <div class="box-tools pull-right">
            {% if current_user.is_allowed(admin_view.endpoint, 'manage_provider_info') %}
                <a href="{{ url_for(endpoint + '.extra_provider_info', id=account.id) }}" class="btn btn-box-tool btn-sm"
                    data-toggle="tooltip" title="{{ _("Edit") }}">
                    <i class="fa fa-edit"></i>
                </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
    <div class="box-body">
        {% if account.provider_info %}
            {% for k, v in account.provider_info.items() %}
                {% set fields = provider_info_fields(account) %}
                <strong>
                    {% if k in fields %}
                        {{ fields[k] }}
                    {% else %}
                        {{ k }}
                    {% endif %}
                </strong>
                <p class="text-muted">{{ v }}</p>
                <hr />
            {% endfor %}
        {% else %}
            <p class="text-muted text-center">{{ _("No information") }}</p>
            {% if current_user.is_allowed(admin_view.endpoint, 'manage_provider_info') %}
                <p class="text-center">
                    <a href="{{ url_for(endpoint + '.extra_provider_info', id=account.id) }}" class="btn btn-success btn-xs">{{ _("Add") }}</a>
                </p>
            {% endif %}
        {% endif %}
    </div>
</div>
