{% import 'accounts/account_yakassa_lib.html' as acclib with context %}

{% set can_manage = current_user.is_allowed(admin_view.endpoint, 'yakassa_manage_subscriptions') and not view_only %}
{% set can_finish = current_user.is_allowed(admin_view.endpoint, 'yakassa_finish_subscriptions') and not view_only %}
{% set can_refund = current_user.is_allowed(admin_view.endpoint, 'yakassa_refund_subscriptions') and not view_only %}
{% set can_remove_payment_method = current_user.is_allowed(admin_view.endpoint, 'yakassa_payment_method_remove') and not view_only %}
{% set can_archive_subscription = current_user.is_allowed(admin_view.endpoint, 'yakassa_archive_subscription') and not view_only %}

{% macro yakassa_actions_btn(s) %}
    {% if can_manage %}
        <div class="dropdown">
            <a class="btn btn-default btn-sm btn-block dropdown-toggle"
               id="yakassa_actions_btn"
               data-toggle="dropdown"
               aria-haspopup="true"
               aria-expanded="true"
            >
                {{ _("Actions") }} <span class="caret"></span>
            </a>
            <ul class="dropdown-menu dropdown-menu-right" aria-labelledby="yakassa_actions_btn" style="margin-top: 35px; margin-left: -60px">
                {% if s.subscription_renew_status == 'active' or s.subscription_renew_status == 'in_retry' %}
                    <li>
                        <a class="text-yellow"
                           href="{{ url_for(endpoint + '.yakassa_cancel_subscription', id=account.id, s_id=s.id) }}"
                           onclick="return confirm('{{ _("Are you sure you want to pause auto-renewal for the subscription?") }}')">
                            <i class="glyphicon glyphicon-pause"></i> {{ _("Pause auto-renewal") }}
                        </a>
                    </li>
                {% else %}
                    <li>
                        <a class="text-green"
                           href="{{ url_for(endpoint + '.yakassa_activate_subscription', id=account.id, s_id=s.id) }}"
                           onclick="return confirm('{{ _("Are you sure you want to activate the subscription?") }}')">
                            <i class="glyphicon glyphicon-play"></i> {{ _("Activate") }}
                        </a>
                    </li>
                    {% if can_finish %}
                        <li>
                            <a class="text-red"
                               href="{{ url_for(endpoint + '.yakassa_finish_subscription', id=account.id, s_id=s.id) }}"
                               onclick="return confirm('{{ _("Are you sure you want to terminate the subscription early?") }}')"
                            >
                                <i class="glyphicon glyphicon-remove"></i> {{ _("Terminate early") }}
                            </a>
                        </li>
                    {% endif %}
                {% endif %}
                {% if can_refund and s.last_payment and s.last_payment.status == 'succeeded' and s.subscription_renew_status != 'in_retry' %}
                    <li>
                        <a class="text-red"
                           href="{{ url_for(endpoint + '.yakassa_refund_subscription', id=account.id, s_id=s.id) }}"
                           onclick="return confirm('{{ _("Are you sure you want to refund?") }}')"
                        >
                            <i class="fa fa-money"></i> {{ _("Refund") }}
                        </a>
                    </li>
                {% endif %}
                {% if can_archive_subscription %}
                    <li>
                        <a class="text-red"
                           href="{{ url_for(endpoint + '.yakassa_archive_subscription', id=account.id, s_id=s.id) }}"
                           onclick="return confirm('{{ _("Are you sure you want to archive this subscription? Any operations with it will be unavailable.") }}')">
                            <i class="fa fa-archive"></i> {{ _("Archive") }}
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    {% else %}
        <p>{{ _("Actions are not available") }}</p>
    {% endif %}
{% endmacro %}

<div class="box box-primary yakassa">
    <div class="box-header with-border">
        <h3 class="box-title">{{ _('Archived subscriptions') if archived == True else _('Subscriptions') }} {{ _("YooKassa") }}</h3>
    </div>
    <div class="box-body">
        {% if subscriptions is none %}
            <p class="text-muted text-center" style="color: red">{{ _("Unable to retrieve information about subscriptions") }}</p>
        {% elif subscriptions|length > 0 %}
        <table class="table table-bordered small-font">
            <tbody>
                <tr>
                    <th>{{ _("Name") }}</th>
                    <th>{{ _("Status") }}</th>
                    <th>{{ _("Creation date") }}</th>
                    <th>{{ _("Expiration date") }}</th>
                    <th>{{ pgettext("yakassa subs", "Updated at") }}</th>
                    <th>{{ _("Next charge attempt at") }}</th>
                    {% if not archived %}
                        <th></th>
                    {% endif %}
                </tr>
                {% for s in subscriptions %}
                <tr data-yakassa-sub-id={{s.id}}>
                    <td>
                        <a title="{{ _("Details") }}" href="{{ url_for(endpoint + '.yakassa_subscription_details', id=account.id, s_id=s.id) }}">
                            {% if s.offer.id and s.offer.id in offers_map %}
                                {% set offer = offers_map[s.offer.id] %}
                                {{ admin_view.format_offer(offer) }}
                            {% else %}
                                {{ s.id }}
                            {% endif %}
                        </a>
                        {% if cms %}
                            <a title="{{ _("Offer") }}" href="{{ url_for('offer.edit_view', id=s.offer.id) }}">
                                <i class="fa fa-link"></i>
                            </a>
                        {% endif %}
                    </td>
                    <td>
                        <span class="badge {{ 'bg-green' if s.subscription_renew_status == 'active' else 'bg-red'}}">
                            {{ config.SUBSCRIPTION_RENEW_STATUS[s.subscription_renew_status] }}
                        </span>
                        {% if s.error%}
                            <a href="#" data-toggle="modal" data-target="#dlg-error-{{loop.index}}" title="{{ _("Details") }}" style="margin-left: 3px">
                                <i class="fa fa-eye"></i>
                            </a>
                            <div class="modal fade" id="dlg-error-{{loop.index}}" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                                <div class="modal-dialog modal-md" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                            <h4 class="modal-title">{{ _("Error details") }}</h4>
                                        </div>
                                        <div class="modal-body">
                                            <pre>{{ s.error }}</pre>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">OK</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </td>
                    <td>
                        {{ s.created_at|localize_datetime }}
                    </td>
                    <td>
                        {{ s.expires_at|localize_datetime }}
                    </td>
                    <td>
                        {{ s.updated_at|localize_datetime }}
                    </td>
                    <td>
                        {{ s.next_charge_attempt_at|localize_datetime }}
                    </td>
                    {% if not archived %}
                        <td>
                            {{ yakassa_actions_btn(s) }}
                        </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
            {% if archived %}
                <p class="text-muted text-center">{{ _("There are no archived subscriptions right now") }}</p>
            {% else %}
                <p class="text-muted text-center">{{ _("There are no active subscriptions right now") }}</p>
            {% endif %}
        {% endif %}
    </div>
    <div class="box-footer">
        {% if archived %}
            <ul class="pagination no-margin pull-right" style="margin-left: 5px !important">
                {% set pagination_endpoint = endpoint + '.yakassa_archived_subscriptions' if archived else '.info' %}

                {% if yakassa_frm > 0 %}
                    <li class="paginate_button previous">
                        <a href="{{ url_for(pagination_endpoint, id=account.id, yakassa_page=yakassa_page - 1) }}">
                            <i class="fa fa-long-arrow-left" aria-hidden="true"></i>
                        </a>
                    </li>
                {% endif %}

                {% if yakassa_more %}
                    <li class="paginate_button next">
                        <a href="{{ url_for(pagination_endpoint, id=account.id, yakassa_page=yakassa_page + 1) }}">
                            <i class="fa fa-long-arrow-right" aria-hidden="true"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>

            <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default">{{ _("Back") }}</a>
        {% else %}
            {% if payment_method %}
                <span class="payment-method">
                    <label>{{ _("Payment method") }}:</label>
                    <a href="{{ url_for(endpoint + '.yakassa_payment_method_view', id=account.id)}}">{{ acclib.payment_method(payment_method) }}</a>
                    {% if can_remove_payment_method %}
                        <a href="{{ url_for(endpoint + '.yakassa_payment_method_remove', id=account.id) }}" onclick="return confirm('{{ _("Are you sure you want to unlink the bank card?") }}')">
                            <i class="fa fa-remove" style="color: red"></i>
                        </a>
                    {% endif %}
                </span>
            {% endif %}
            <div class="pull-right">
                <a class="btn btn-default" href="{{ url_for(endpoint + '.yakassa_refund_history', id=account.id) }}">{{ _("Refund history") }}</a>
                <a class="btn btn-default" href="{{ url_for(endpoint + '.yakassa_archived_subscriptions', id=account.id, archived=True) }}">{{ _("Subscriptions archive") }}</a>
            </div>
        {% endif %}
    </div>
</div>
