{% extends 'accounts/info.html' %}


{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">{{ _("Edit phone number") }}</h3>
                <div class="box-tools pull-right">
                    {% set ver = account.is_phone_verified %}
                    {% if current_user.is_allowed(admin_view.endpoint, 'phone_verification_toggle') and account.phone %}
                        <a class="btn btn-box-tool btn-sm" href="{{ url_for(endpoint + '.account_phone_verification_toggle', id=account.id) }}" data-toggle="tooltip" data-placement="bottom"
                           title="Изменить статус на&nbsp;{{ _('verified') if ver else _('not verified') }}" style="{{ 'color: green' if ver else '' }}">
                            <i class="fa fa-check-circle{{ '' if ver else '-o' }}"></i>
                        </a>
                    {% endif %}
                    {% if current_user.is_allowed(admin_view.endpoint, 'phone_remove') and account.phone %}
                        <a class="btn btn-box-tool btn-sm" href="{{ url_for(endpoint + '.account_phone_remove', id=account.id) }}" data-toggle="tooltip" data-placement="bottom"
                           title="{{ _("Unlink and delete") }}" style="color: remove">
                            <i class="fa fa-remove"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="box-body">
                {% for field in form %}
                    {{ field() }}
                {% endfor %}
            </div>
            <div class="box-footer">
                <button type="submit" class="btn btn-success">{{ _("Save") }}</button>
                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default pull-right">{{ _("Cancel") }}</a>
            </div>
        </form>
    </div>
{% endblock %}
