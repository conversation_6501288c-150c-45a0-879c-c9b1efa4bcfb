{% extends 'accounts/info.html' %}


{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">{{ _("Cancel purchase") }}</h3>
            </div>
            <div class="box-body">
                {% for field in form %}
                    {{ field() }}
                {% endfor %}
            </div>
            <div class="box-footer">
                <button type="submit" class="btn btn-danger">{{ pgettext("action button", "Cancel") }}</button>
                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default pull-right">{{ _("Back") }}</a>
            </div>
        </form>
    </div>
{% endblock %}
