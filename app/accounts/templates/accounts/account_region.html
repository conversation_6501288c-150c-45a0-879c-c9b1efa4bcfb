<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">{{ _("Region") }}</h3>
        {% if current_user.is_allowed(admin_view.endpoint, 'manage_region_edit') and not view_only %}
            <div class="box-tools pull-right">
                <a href="{{ url_for(endpoint + '.region_manage_region', id=account.id) }}" class="btn btn-box-tool btn-sm" data-toggle="tooltip" title="{{ _("Edit") }}">
                    <i class="fa fa-edit"></i>
                </a>
            </div>
        {% endif %}
    </div>
    <div class="box-body">
        {% if account.region %}
            <p>{{ account.region }}</p>
        {% else %}
            <p class="text-muted text-center">{{ _("Region is not set") }}</p>
        {% endif %}
    </div>
</div>
