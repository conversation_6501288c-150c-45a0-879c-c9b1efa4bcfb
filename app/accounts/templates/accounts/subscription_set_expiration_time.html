{% extends 'accounts/info.html' %}


{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">{{ _("Change expiration time for purchase") }}</h3>
            </div>
            <div class="box-body">
                {% for field in form %}
                    {{ field() }}
                {% endfor %}
            </div>
            <div class="box-footer">
                <button type="submit" class="btn btn-success">{{ _("Save") }}</button>
                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default pull-right">{{ _("Cancel") }}</a>
            </div>
        </form>
    </div>
{% endblock %}


{% block tail_js %}
    {{ super() }}
    <script>
        $(function() {
            $("#expire_time").daterangepicker({
                timePicker: true,
                timePicker24Hour: true,
                timePicker12Hour: false,
                timePickerIncrement: 1,
                singleDatePicker: true,
                autoUpdateInput: true,
                format: 'DD.MM.YY HH:mm',
                firstDay: 1,
                minDate: moment(),
                locale: {
                    customRangeLabel: '{{ _("Select manually") }}',
                    cancelLabel: '{{ _("Clear") }}',
                    applyLabel: '{{ _("Choose") }}',
                }
            });
        });
    </script>
{% endblock %}
