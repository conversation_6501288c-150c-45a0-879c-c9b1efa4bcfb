{% extends 'accounts/info.html' %}

{% import 'accounts/account_yakassa_lib.html' as acclib with context %}

{% set s = subscription %}
{% set i = s.create_request_info %}

{% set app = i.req_info['app'] %}
{% set device = i.req_info['device'] %}


{% block content_info %}
    <div class="box box-primary subscription-details">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">
                    {% if s.offer.id and s.offer.id in offers_map %}
                        {% set offer = offers_map[s.offer.id] %}
                        {{ admin_view.format_offer(offer) }}
                    {% else %}
                        {{ s.id }}
                    {% endif %}
                    <span class="badge {{ 'bg-green' if s.subscription_renew_status else 'bg-red'}}">
                        {{ config.SUBSCRIPTION_RENEW_STATUS.get(s.subscription_renew_status, '') }}
                    </span>
                </h3>
            </div>
            <div class="box-body">
                <div><label>{{ _("Creation date") }}</label>: {{ s.created_at|localize_datetime }}</div>
                <div><label>{{ _("Expiration date") }}</label>: {{ s.expires_at|localize_datetime }}</div>
                <div><label>{{ pgettext("yakassa subs", "Updated at") }}</label>: {{ s.updated_at|localize_datetime }}</div>
                <div><label>{{ _("Next charge attempt at") }}</label>: {{ s.next_charge_attempt_at|localize_datetime }}</div>
                <hr/>
                <div><label>IP</label>: {{ i.ip }}</div>
                <div><label>UserAgent</label>: {{ i.user_agent }}</div>
                {% if i.req_info %}
                    {% if app %}
                        <div><label>{{ _("Application") }}</label>: {{ acclib.render_dict(app) }}</div>
                    {% endif %}
                    {% if device %}
                        <div><label>{{ _("Device") }}</label>: {{ acclib.render_dict(device) }}</div>
                    {% endif %}
                {% endif %}
            </div>
        </form>
    </div>
    <div class="box box-primary last-payment" style="box-shadow: none;">
        <div class="box-header with-border">
            <a title="{{ _("Details") }}" href="{{ url_for(endpoint + '.yakassa_payment', id=account.id, subscription_id=s.id, payment_id=payment.id) }}">
                <h3 class="box-title">{{ _("Last payment") }}</h3>
            </a>
            <a href="{{ url_for(endpoint + '.yakassa_sync_payment', id=account.id, s_id=s.id, p_id=payment.id) }}" class="btn btn-primary btn-xs pull-right">{{ _("Synchronize") }}</a>
        </div>
        <div class="box-body">
            {% if payment is none %}
                <p class="text-muted text-center">{{ _("Missing data about last payment") }}</p>
            {% elif payments|length > 0 %}
                <div><label>{{ _("Status") }} </label>: {{ config.SUBSCRIPTION_PAYMENT_STATUS.get(payment.status, '') }}</div>
                {% if payment.status == 'canceled' %}
                    <div><label>{{ _("Cancellation details") }} </label>: {{ payment.cancellation_details['reason'] }}</div>
                {% endif %}
                <div><label>{{ _("Amount") }}</label>: {{ payment.amount['value'] }} {{ payment.amount['currency'] }}</div>
                <div><label>{{ _("Creation date") }}</label>: {{ payment.created_at|localize_datetime }}</div>
                <div><label>{{ _("Expiration date") }}</label>: {{ payment.expires_at|localize_datetime }}</div>
                <div><label>{{ _("Captured at") }}</label>: {{ payment.captured_at|localize_datetime }}</div>
                {% if payment.payment_method %}
                    <div>
                        <label>{{ _("Payment method") }}</label>:
                        <a href="{{ url_for(endpoint + '.yakassa_payment_method_view', id=account.id, url=request.url)}}">
                            {{ acclib.payment_method(payment.payment_method) }}
                        </a>
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
    <div class="box box-primary payments-history" style="box-shadow: none;">
        <div class="box-header ">
            <h3 class="box-title">{{ _("Payment history") }}</h3>
        </div>
        <div class="box-body">
            {% if payments is none %}
                <p class="text-muted text-center" style="color: red">{{ _("Unable to retrieve payment information") }}</p>
            {% elif payments|length > 0 %}
                <table class="table table-bordered small-font">
                    {% set can_delete = current_user.is_allowed(admin_view.endpoint, 'yakassa_cancel_subscriptions') and not view_only %}
                    <tbody>
                        <tr>
                            <th>ID</th>
                            <th>{{ _("Status") }}</th>
                            <th>{{ _("Amount") }}</th>
                            <th>{{ _("Creation date") }}</th>
                            <th>{{ _("Expiration date") }}</th>
                            <th>{{ _("Captured at") }}</th>
                            <th>{{ _("Payment method") }}</th>
                            <th></th>
                        </tr>
                        {% for p in payments %}
                            <tr data-payment-id={{p.id}}>
                                <td>
                                    <a title="{{ _("Details") }}" href="{{ url_for(endpoint + '.yakassa_payment', id=account.id, subscription_id=s.id, payment_id=p.id) }}">{{ p.id }}</a>
                                </td>
                                <td>
                                    <span class="badge {{ 'bg-green' if p.status == 'succeeded' else 'bg-red' if p.status == 'canceled' else ''}}">
                                        {{ config.SUBSCRIPTION_PAYMENT_STATUS.get(p.status, '') }}
                                    </span>
                                </td>
                                <td>
                                    {{ p.amount['value'] }} {{ p.amount['currency'] }}
                                </td>
                                <td>
                                    {{ p.created_at|localize_datetime }}
                                </td>
                                <td>
                                    {{ p.expires_at|localize_datetime }}
                                </td>
                                <td>
                                    {{ p.captured_at|localize_datetime }}
                                </td>
                                <td>
                                    {% if payment.payment_method %}
                                    <a href="{{ url_for(endpoint + '.yakassa_payment_method_view', id=account.id, url=request.url)}}">
                                        {{ acclib.payment_method(payment.payment_method) }}
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="text-muted text-center">{{ _("No payment records") }}</p>
            {% endif %}
        </div>
        <div class="box-footer">
            <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default">{{ _("Back") }}</a>

            <ul class="pagination no-margin pull-right">
                {% if frm > 0 %}
                    <li class="paginate_button previous">
                        <a href="{{ url_for(endpoint + '.yakassa_subscription_details', id=account.id, s_id=subscription.id, page=page - 1) }}">
                            <i class="fa fa-long-arrow-left" aria-hidden="true"></i>
                        </a>
                    </li>
                {% endif %}

                {% if more %}
                    <li class="paginate_button next">
                        <a href="{{ url_for(endpoint + '.yakassa_subscription_details', id=account.id, s_id=subscription.id, page=page + 1) }}">
                            <i class="fa fa-long-arrow-right" aria-hidden="true"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
 {% endblock %}
