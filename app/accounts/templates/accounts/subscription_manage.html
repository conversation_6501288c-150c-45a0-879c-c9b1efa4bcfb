{% extends 'accounts/info.html' %}


{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">
                    {% if new %}
                        {{ _("Create purchase") }}
                    {% else %}
                        {{ _("Edit purchase") }}
                    {% endif %}
                </h3>
            </div>
            <div class="box-body">
                {% for field in form %}
                    {{ field() }}
                {% endfor %}
            </div>
            <div class="box-footer">
                <button type="submit" class="btn btn-success">{{ _("Save") }}</button>
                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default pull-right">{{ _("Cancel") }}</a>
            </div>
        </form>
    </div>
{% endblock %}


{% block tail_js %}
    {{ super() }}
    <script>
        $(function() {
            $("#expire_time").daterangepicker({
                timePicker12Hour: false,
                timePicker24Hour: true,
                singleDatePicker: true,
                autoUpdateInput: false,
                timePickerIncrement: 1,
                timePicker: true,
                minDate: moment(),
                firstDay: 1,
                locale: {
                    customRangeLabel: '{{ _("Select manually") }}',
                    cancelLabel: '{{ _("Clear") }}',
                    applyLabel: '{{ _("Choose") }}',
                }
            }).on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('DD.MM.YY HH:mm'));
            }).on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });

            $('#offer_id').select2();
        });
    </script>
{% endblock %}
