{% extends 'accounts/info.html' %}

{% import 'accounts/account_yakassa_lib.html' as acclib with context %}

{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">{{ _("Payment") }} {{payment.id}}</h3>
            </div>
            <div class="box-body">
                <div><label>{{ _("Status") }}</label>: {{ config.SUBSCRIPTION_PAYMENT_STATUS.get(payment.status, '') }}</div>
                {% if payment.status == 'canceled' %}
                    <div><label>{{ _("Cancellation details") }}:</label> {{ payment.cancellation_details['reason'] }}</div>
                {% endif %}
                <div><label>{{ _("Amount") }}</label>: {{ payment.amount['value'] }} {{ payment.amount['currency'] }}</div>
                <div><label>{{ _("Creation date") }}</label>: {{ payment.created_at|localize_datetime }}</div>
                <div><label>{{ _("Expiration date") }}</label>: {{ payment.expires_at|localize_datetime }}</div>
                <div><label>{{ _("Captured at") }}</label>: {{ payment.captured_at|localize_datetime }}</div>
                <div><label>{{ _("Payment method") }}</label>: {{ acclib.payment_method(payment.payment_method) or '' }}</div>
            </div>
            <div class="box-footer">
                <a href="{{ url_for(endpoint + '.yakassa_subscription_details', id=account.id, s_id=subscription_id) }}" class="btn btn-default">{{ _("Back") }}</a>
            </div>
        </form>
    </div>
    <div class="box box-primary" style="box-shadow: none;">
        <div class="box-header with-border">
            <h3 class="box-title">{{ _("Data from service") }}</h3>
        </div>
        <div class="box-body">
            <pre style="background: white; border: 0">{{payment|to_pretty_json}}</pre>
        </div>
    </div>
    <div class="box box-primary" style="box-shadow: none;">
        <div class="box-header with-border">
            <h3 class="box-title">{{ _("Saved data") }}</h3>
        </div>
        <div class="box-body">
            <pre style="background: white; border: 0">{{payment_info|to_pretty_json}}</pre>
        </div>
    </div>
{% endblock %}
