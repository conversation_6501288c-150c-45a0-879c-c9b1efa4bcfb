{% extends 'accounts/base.html' %}


{% block head_css %}
    {{ super() }}

    <style type="text/css">
        .popover {
            min-width: 450px;
        }

        .popover pre {
            background: white;
            border: 0;
        }
    </style>
{% endblock %}


{% block account_info %}
    {% if admin_view.account_block %}
        {% for item in admin_view.account_block %}
            {{ item.render_widget(account) }}
        {% endfor %}
    {% elif admin_view.account_template %}
        {% include admin_view.account_template %}
    {% endif %}
{% endblock %}


{% block content_info %}
    {% if admin_view.content_block %}
        {% for item in admin_view.content_block %}
            {% if item.template %}
                {{ item.render_widget(account) }}
            {% endif %}
        {% endfor %}
    {% elif admin_view.template %}
        {% include admin_view.template %}
    {% endif %}
{% endblock %}


{% block tail_js %}
    {{super()}}

    <script>{% include 'accounts/components/boxes/paginated_box.js' %}</script>

    <script type="text/javascript">
        $(document).ready(function() {
          $('[data-toggle="popover"]').popover({
            trigger: 'hover',
            placement: 'bottom',
            html: true
          })

          if ($('#actions_list>li').length === 0) {
            $('#actions_button').hide();
          }
        })
    </script>
{% endblock %}
