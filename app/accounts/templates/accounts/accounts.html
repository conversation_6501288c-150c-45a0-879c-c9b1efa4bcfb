{% extends admin_base_template %}
{% import 'admin/lib.html' as lib with context %}


{% macro searchmacro(exact=False) %}
<div class="form-inline dt-bootstrap">
    {% if search_fields %}
        {% for k in search_fields %}
            <div class="input-group input-group-sm">
                <input class="form-control" data-key="{{ k['name'] }}" id="search-{{ k['name'] }}" name="search" type="text"
                        value="{% if k['name'] == search_field %}{{ search }}{% endif %}" placeholder="{{k.title}}" />
                <span class="input-group-btn">
                    <button type="button" class="btn btn-default btn-flat search" value="{{ k['name'] }}" {% if not exact %} data-exact="false" {% endif %}><i class="fa fa-search" aria-hidden="true"></i></button>
                </span>
            </div>
        {% endfor %}
    {% endif %}
    </div>
{% endmacro %}

{% block head_css %}
    {{ super() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.3/daterangepicker.min.css" rel="stylesheet">
{% endblock %}


{% block body %}
    <section class="content-header">
        <h1>{{admin_view.name}}</h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-xs-12">
                <div class="nav-tabs-custom">
                    <ul class="nav nav-tabs">
                        {% if config.ENABLE_LEGACY_ACCOUNTS_SEARCH %}
                            <li {% if exact %} class="active" {% endif %}><a href="#searchexact-tab" data-toggle="tab">{{ _("Precise search") }}</a></li>
                            <li {% if not exact %} class="active" {% endif %}><a href="#search-tab" data-toggle="tab">{{ _("Search") }}</a></li>
                        {% else %}
                            <li {% if exact %} class="active" {% endif %}><a href="#searchexact-tab" data-toggle="tab">{{ _("Search") }}</a></li>
                        {% endif %}
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane {% if exact %} active {% endif %}" id="searchexact-tab">
                            {{ searchmacro(exact=True) }}
                        </div>
                        <div class="tab-pane {% if not exact %} active {% endif %}" id="search-tab">
                            {{ searchmacro() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-xs-12">
                <div class="box box-primary">
                    <div class="box-header">
                        <div class="row">
                            <div class="dataTables_wrapper form-inline dt-bootstrap">
                                <div class="col-sm-3">
                                    <div class="dataTables_length">
                                        <label>
                                            <select id="page-size" size="1" class="form-control input-sm">
                                                {% for i in range(10, 110, 10) %}
                                                    <option {% if page_size == i %} selected="selected" {% endif %} value="{{ i }}">{{ i }}</option>
                                                {% endfor %}
                                            </select> {{ _("records per page") }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-sm-9">
                                    <div class="dataTables_filter pull-right">
                                        <div class="form-group form-group-sm">
                                            <div class="input-group">
                                                <button type="button" class="btn btn-default pull-right btn-sm" id="daterange-btn">
                                                    <span>
                                                        {% if registred_from %}
                                                            {{ registred_from|localize_prebilling_date }}
                                                        {% endif %}

                                                        {% if registred_to %}
                                                            - {{ registred_to|localize_prebilling_date }}
                                                        {% endif %}

                                                        {% if not registred_to and not registred_from %}
                                                            {{ _("Registration date") }}
                                                        {% endif %}
                                                    </span>
                                                    <i class="fa fa-caret-down"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <select size="1" id="offer-select" class="form-control input-sm" style="max-width: 200px">
                                            <option value="">-</option>
                                            {% for offer in offers %}
                                                <option {% if offer_id == offer.id %} selected="selected" {% endif %}
                                                        value="{{ offer.id }}">{{ admin_view.format_offer(offer) }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if accounts and accounts|length > 0 %}
                        <div class="box-body" style="overflow-x: auto">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                    <tr>
                                        {% if group_operations and current_user.is_allowed(admin_view.endpoint, 'batch_subscription') %}
                                            <th><input id="global-check" type="checkbox"/></th>
                                        {% endif %}
                                        {% for field, title in list_columns if field %}
                                            <th class="column-header">
                                                {% set sorted = sort.endswith(field) %}
                                                {% set sort_desc = sort.startswith('-') and sorted %}
                                                <a title="Sort by {{ title }}" href="{{ url_for(endpoint + '.index', sort=field if sort_desc else '-' + field,
                                                                                                page=page, page_size=page_size, search=search, search_field=search_field,
                                                                                                registred_from=registred_from, registred_to=registred_from, offer=offer) }}">
                                                    {{ title }}
                                                    {% if sorted %}
                                                        {% if sort_desc %}
                                                            <span class="fa fa-chevron-up glyphicon glyphicon-chevron-up"></span>
                                                        {% else %}
                                                            <span class="fa fa-chevron-down glyphicon glyphicon-chevron-down"></span>
                                                        {% endif %}
                                                    {% endif %}
                                                </a>
                                            </th>
                                        {% endfor %}
                                        {% if provider_columns and not hide_provider_columns %}
                                            {% for field in provider_columns %}
                                                <th>
                                                    {{ field['title'] }}
                                                </th>
                                            {% endfor %}
                                        {% endif %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for account in accounts %}
                                    {% set p = providers[account.id] %}
                                    <tr>
                                        {% if group_operations and current_user.is_allowed(admin_view.endpoint, 'batch_subscription') %}
                                            <td><input name="account_ids[]" class="check" value="{{ account.id }}" type="checkbox"></td>
                                        {% endif %}
                                        {% for field, title in list_columns if field %}
                                            <td>
                                                {% if field == 'login'%}
                                                    {% if current_user.is_allowed(admin_view.endpoint, 'info') %}
                                                        <a href={{ url_for(endpoint + '.info', id=account['id']) }}>{{account[field] or ''}}</a>
                                                    {% else %}
                                                        {{ account[field] or '' }}
                                                    {% endif %}
                                                {% elif field == 'purchases' %}
                                                    {% for p in sort_by_creation(account.purchases) %}
                                                        {% if cms or not p.is_uncommitted %}
                                                            <p><span class="label label-{% if p.state == "ACTIVE" %}success{% else %}warning{% endif %}">
                                                                {% set offer = list_offers_map[p.offer_id] %}
                                                                {% if p.offer_id in list_offers_map %}
                                                                    {{ admin_view.format_offer(offer) }}
                                                                {% else %}
                                                                    {{ p.offer_id }}
                                                                {% endif %}
                                                            </span></p>
                                                        {% endif %}
                                                    {% endfor %}
                                                {% elif field == 'provider' %}
                                                    <a href="http://{{ (p['provider_subdomain'], '.', config['OCP_DOMAIN'], '/info?id=', account.id|string)|join('') }}">
                                                        {{ p['provider_name'] or p['provider_subdomain'] }}
                                                    </a>
                                                {% elif field == 'region' %}
                                                    {{ account[field] if account[field] else p['provider_region'] or '' }}
                                                {% elif field == 'name' %}
                                                    {{ (account.provider_info or {}).get('fio') or '-' }}
                                                {% elif field == 'operator' %}
                                                    {{ (account.provider_info or {}).get('operator') or '-' }}
                                                {% elif account[field]|is_date %}
                                                    {{ account[field]|localize_prebilling_datetime }}
                                                {% else %}
                                                    {{ account[field] or '' }}
                                                {% endif %}
                                            </td>
                                        {% endfor %}
                                        {% if provider_columns and not hide_provider_columns %}
                                            {% for field in provider_columns %}
                                                {% if account.provider_info %}
                                                    <td> {{ account.provider_info.get(field['name'], '-') }}</td>
                                                {% else %}
                                                    <td> - </td>
                                                {% endif %}
                                            {% endfor %}
                                        {% endif %}
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}
                    <div class="box-footer clearfix">
                        {% if num_pages is not none %}
                            {{ lib.pager(page, num_pages, url) }}
                        {% else %}
                            {{ lib.simple_pager(page, data|length == page_size, url) }}
                        {% endif %}
                        <div class="col-md-5" >
                            <div class="dataTables_info">
                                {{ _("Found") }} {{ count or 0 }}
                            </div>
                            {% if group_operations %}
                                <div class="batch" style="display: none">
                                    {{ _("With selected") }}:
                                    <select id="batch-select" size="1" class="form-control input-sm">
                                        <option>-</option>
                                        {% for offer in offers %}
                                            <option data-action="create" value="{{ offer.id }}">{{ _("Enable") }} {{ admin_view.format_offer(offer) }}</option>
                                        {% endfor %}
                                        {% for offer in offers %}
                                            {% if admin_view.cancelling_allowed(offer) %}
                                                <option data-action="disable" value="{{ offer.id }}">{{ _("Disable") }} {{ admin_view.format_offer(offer) }}</option>
                                            {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block tail_js %}
    {{super()}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.3/daterangepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/URI.js/1.19.1/URI.min.js"></script>
    <script>
        var uri = new URI("{{ uri|safe }}");
        $(function(){
            $('#daterange-btn').daterangepicker(
                {
                    ranges: {
                        "{{ _("Today") }}": [moment(), moment()],
                        "{{ _("Yesterday") }}": [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        "{{ _("Last 7 days") }}": [moment().subtract(6, 'days'), moment()],
                        "{{ _("Last 30 days") }}": [moment().subtract(29, 'days'), moment()],
                        "{{ _("This month") }}": [moment().startOf('month'), moment().endOf('month')],
                        "{{ _("Last month") }}": [
                            moment().subtract(1, 'month').startOf('month'),
                            moment().subtract(1, 'month').endOf('month')
                        ]
                    },
                    autoUpdateInput: false,
                    firstDay: 1,
                    locale: {
                        customRangeLabel: '{{ _("Select manually") }}',
                        cancelLabel: '{{ _("Clear") }}',
                        applyLabel: '{{ _("Choose") }}',
                    }
                    {% if registred_from %}
                        ,startDate: moment("{{ registred_from }}")
                    {% endif %}
                    {% if registred_to %}
                        ,endDate: moment("{{ registred_to }}")
                    {% endif %}
                },

                function (start, end) {

                }
            ).on('cancel.daterangepicker', function(ev, picker) {
                window.location.href = uri.removeQuery('registred_from').removeQuery('registred_to');
            }).on('apply.daterangepicker', function(ev, picker){
                var start = picker.startDate,
                    end = picker.endDate;
                $('#daterange-btn span').html(start.format('D.MM.YY') + ' - ' + end.format('D.MM.YY'));
                window.location.href = uri.setQuery('registred_from', start.format('YYYY-MM-DD')).setQuery('registred_to', end.format('YYYY-MM-DD'))
            });

            $('.search').on('click', function() {
                key = $(this).val()

                value = $(this).parent().parent().find('input[data-key="' + key + '"]').val()
                uri = uri.setQuery('search', value).setQuery('search_field', key).setQuery('page', 0);

                if ($(this).data("exact") == false) {
                    uri = uri.setQuery("exact", "false")
                } else {
                    uri = uri.setQuery("exact", "true")
                }

                window.location.href = uri
            })

            $('input[name="search"]').on('keyup', function(e) {
                if (e.keyCode == 13) {
                    $(e.target).next().find('button.search').trigger('click')
                }
            });

            $("#page-size").on("change", function() {
                window.location.href = uri.setQuery("page_size", $(this).val()).toString();
            });

            $("#offer-select").on("change", function() {
                window.location.href = uri.setQuery("offer", $(this).val()).toString();
            });

            {% if group_operations and current_user.is_allowed(admin_view.endpoint, 'batch_subscription') %}
                $("#global-check").on("click", function() {
                    $(".check").prop("checked", $(this).prop('checked'));
                    $(".check").trigger("change");
                });

                $(".check").on("change", function() {
                    if ($(".check:checked").length == 0) {
                        $(".batch").hide();
                    } else {
                        $(".batch").show();
                    }
                })

                $("#batch-select").on("change", function() {
                    var accountIds = [],
                        selected = $(this).find("option:selected"),
                        action = selected.data('action'),
                        offerId = $(this).val();

                     if (!offerId) {
                        return
                    }

                    $(".check:checked").each(function() {
                        accountIds.push($(this).val());
                    })

                    var params = {
                        "account_ids": accountIds,
                        "offer_id": offerId,
                        "action": action,
                    }

                    window.location.href = "{{ url_for(endpoint + '.subscriptions_batch_subscriptions') }}" + "?" + $.param(params)
                })
            {% endif %}
        });
    </script>
{% endblock %}
