{% extends 'accounts/blocks/base_block.html' %}

{% macro refund_status_label(item) %}
    {% if item.status == "succeeded" %}
        <span class="label label-success">
            {{ _("Accepted") }}
        </span>
    {% elif item.last_error %}
        <span class="label label-danger">
            {{ _("Fail") }}
        </span>
    {% endif %}
{% endmacro %}

{% macro details_button(button_label, details_text) %}
    <span class="btn btn-sm btn-default btn-block" onclick="alert($(this).attr('data-text'))"
          data-text="{{ details_text|escape }}">
        <i class="fa fa-eye"></i> {{ button_label }}
    </span>
{% endmacro %}

{% macro refund_table_row(item) %}
    <tr>
        <td>
            <a href="{{ url_for(endpoint + '.yakassa_subscription_details', id=account.id, s_id=item.subscription_id) }}">
                <i class="fa fa-link"></i>
            </a>
        </td>
        <td>{{ item.payment_id }}</td>
        <td>{{ item.amount.value }} {{ item.amount.currency }}</td>
        <td>{{ item.created_at|localize_prebilling_datetime }}</td>
        <td>{{ item.succeeded_at|localize_prebilling_datetime }}</td>
        <td>{{ item.initiator }}</td>
        <td>{{ refund_status_label(item) }}</td>
        <td>
            {% if item.last_error %}
                {{ details_button(_("See error details"), item.last_error) }}
            {% else %}
                {{ _("No errors") }}
            {% endif %}
        </td>
    </tr>
{% endmacro %}

{# === end macros === #}

{% block title %}
    {{ _("Refund history") }}
{% endblock %}

{% block body %}
    {% if refund_history_items is none %}
        <p class="text-muted text-center" style="color: red">{{ _("Refund history is not available.") }}</p>
    {% elif refund_history_items|length > 0 %}
        <table class="table">
            <thead>
                <tr>
                    <th>{{ _("Subscription") }}</th>
                    <th>{{ _("Payment ID") }}</th>
                    <th>{{ _("Amount") }}</th>
                    <th>{{ _("Creation date") }}</th>
                    <th>{{ _("Succeeded at") }}</th>
                    <th>{{ _("Initiator") }}</th>
                    <th>{{ _("Status") }}</th>
                    <th>{{ _("Error text") }}</th>
                </tr>
            </thead>
            <tbody>
                {% for refund_history_item in refund_history_items %}
                    {{ refund_table_row(refund_history_item) }}
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <p class="text-muted text-center">{{ _("No refund history.") }}</p>
    {% endif %}
{% endblock %}

{% block buttons %}
    <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default">{{ _("Back") }}</a>
{% endblock %}
