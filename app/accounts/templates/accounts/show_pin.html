{% extends 'accounts/info.html' %}


{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">{{ _("Show PIN") }}</h3>
            </div>
            <div class="box-body no-padding">
                <table class="table table-striped table-bordered table-hover">
                    <thead>
                        <tr>
                            <th class="text-center">{{ pgettext("account info", "Name") }}</th>
                            <th class="text-center">{{ _("PIN") }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if profiles %}
                            {% for p in profiles %}
                                <tr>
                                    <td>{{ p.name }}</td>
                                    <td class="text-center">
                                        <div style="position: relative; margin: 5px; min-height: 24px" class="clipboard-container">
                                            {% if p.pin %}
                                                <div>
                                                    {%- for s in p.pin -%}
                                                        <label class="badge badge-secondary" style="display: inline; margin: 10px; padding: 10px">{{ s }}</label>
                                                    {%- endfor -%}
                                                </div>
                                                <sup style="position: absolute; margin-top: 10px; margin-left: -6px" title="Copy">
                                                    <i class="fa fa-clipboard fa-fw clipboard" style="cursor: pointer; display: none;"></i>
                                                </sup>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">{{ _("PIN is undefined") }}</p>
                        {% endif %}
                    </tbody>
                </table>
            </div>
            <div class="box-footer">
                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default pull-right">{{ _("Back") }}</a>
            </div>
        </form>
    </div>
{% endblock %}
