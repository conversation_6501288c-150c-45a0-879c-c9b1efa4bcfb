<div class="box box-primary">
    <div class="box-body box-profile">
        <h3 class="profile-username text-center">{{ account.login }}</h3>
        <p class="text-muted text-center">
            {% if current_user.is_allowed("user_activity", "all_users_activity") %}
                <i class="fa fa-history"></i>
                <a href="{{ url_for("user_activity.timeline", views="clients", any_user=True, request_args_id=account.id) }}">changes_history</a>
                <br /><br />
            {% endif %}
            {% if cms and account_type %}
                {{ _("Type") }}: {{ account_type }}<br />
            {% endif %}

            {% if account.email %}
                Email: <a href="mailto:{{ account.email }}">{{ account.email }}</a><br />
            {% endif %}
            {% if account.phone or pm %}
                <span>
                    {{ _("Phone") }}: {{ account.phone or _("not set") }}
                    {% if account.is_phone_verified %}
                        <span style="color: green; display: inline" data-toggle="tooltip" title="{{ _("Verified") }}">
                            <i class="fa fa-check"></i>
                        </span>
                    {% endif %}
                </span>
                <br />
            {% endif %}
            {% if account.city %}
                {{ _("City") }}: {{ account.city }}<br />
            {% endif %}
            {% if provider and not hide_provider %}
                {{ _("Provider") }}: <a target="_blank" href="http://{{ (provider.subdomain, '.', config.OCP_DOMAIN, '/info?id=', account.id|string)|join('') }}">{{ provider }}</a><br />
            {% endif %}

            {% if cms and provider.tags %}
                <div class="info-tags" style="overflow: hidden;">
                    {% for tag in provider.tags %}
                        <div class="label label-xs tag-el" style="background-color:{{tag.color}} !important">
                            <a href="{{ url_for('tagged.index_view', flt_tags_in_list=tag.name) }}">{{ tag.name }}</a>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        </p>
        <ul class="list-group list-group-unbordered">
            {% if current_user.is_allowed(admin_view.endpoint, 'user_activity') or not admin_view.provider %}
                <li class="list-group-item">
                    <b>{{ _("First login time") }}</b><br>
                    <div>{{ account.first_login_time|localize_prebilling_datetime or 'n/a' }}</div>
                </li>
                <li class="list-group-item">
                    <b>{{ _("Last play time") }}</b><br>
                    <div>{{ account.last_play_time|localize_prebilling_datetime or 'n/a' }}</div>
                </li>
            {% endif %}
            <li class="list-group-item">
                <b>{{ _("Registration") }}</b><br />
                <div>{{ account.created|localize_prebilling_datetime }}</div>
            </li>
            {% if account.registered_from %}
                <li class="list-group-item">
                    <b>{{ _("Registration") }} через</b><br />
                    <div>{{ account.registered_from }}</div>
                </li>
            {% endif %}
            {% if current_user.is_allowed(admin_view.endpoint, 'view_updated') %}
            <li class="list-group-item">
                <b>{{ _("Updated") }}</b><br />
                <div>{{ account.updated|localize_prebilling_datetime }}</div>
            </li>
            {% endif %}
            {% if account.remote_id %}
                <li class="list-group-item">
                    <b>{{ _("Remote ID") }}</b>
                    <div class="pull-right">{{ account.remote_id }}</div>
                </li>
            {% endif %}
            {% if account.is_blocked %}
                <li class="list-group-item">
                    <b>
                        <span style="color: red;">{{ _("Blocked") }}</span>
                    </b>
                    <a style="color: grey; display: inline" href="#block_history" data-target="#block_history" data-toggle="modal" title="{{ _("History") }}">
                        <i class="fa fa-history"></i>
                    </a>
                    <div>{{ account.blocked|localize_prebilling_datetime }}</div>
                    {% include 'accounts/account_blocking_history.html' %}
                </li>
            {% endif %}
        </ul>
        {% if current_user and not view_only %}
        <button id="actions_button" type="button" class="btn btn-primary btn-block dropdown-toggle"
            data-toggle="dropdown">
            {{ _("Actions") }}
            <span class="fa fa-caret-down"></span>
        </button>
        <ul id="actions_list" class="dropdown-menu">
            {% if current_user.is_allowed(admin_view.endpoint, 'pin') %}
                <li><a href="{{ url_for(endpoint + '.account_show_pin', id=account.id) }}">{{ _("PIN") }}</a></li>
            {% endif %}
            {% if current_user.is_allowed(admin_view.endpoint, 'reset_password') %}
                <li><a href="{{ url_for(endpoint + '.account_reset_password', id=account.id) }}">{{ _("New password") }}</a></li>
                <li class="divider"></li>
            {% endif %}
            {% if current_user.is_allowed(admin_view.endpoint, 'manage_contacts') %}
                <li><a href="{{ url_for(endpoint + '.account_manage_contacts', id=account.id) }}">{{ _("Email") }}</a>
            {% endif %}
            {% if current_user.is_allowed(admin_view.endpoint, 'phone_manage') %}
                <li><a href="{{ url_for(endpoint + '.account_phone_manage', id=account.id) }}">{{ _("Phone number") }}</a>
            {% endif %}
            {% if current_user.is_allowed(admin_view.endpoint, 'set_verified_phone') %}
                <li><a href="{{ url_for(endpoint + '.account_set_verified_phone', id=account.id) }}">{{ _("Change number and send password") }}</a>
            {% endif %}
            {% if current_user.is_allowed(admin_view.endpoint, 'unlink') or current_user.is_allowed(admin_view.endpoint, 'delete') %}
                <li class="divider"></li>
            {% endif %}
            {% if current_user.is_allowed(admin_view.endpoint, 'delete') and not disable_user_deletion %}
                <li><a class="text-red" href="{{ url_for(endpoint + '.account_delete', id=account.id) }}">{{ _("Delete account") }}</a></li>
            {% endif %}
            {% if current_user.is_allowed(admin_view.endpoint, 'unlink') %}
                <li><a class="text-red" href="{{ url_for(endpoint + '.account_unlink', id=account.id) }}">{{ _("Unlink account") }}</a></li>
            {% endif %}
        </ul>
        {% endif %}
    </div>
</div>
