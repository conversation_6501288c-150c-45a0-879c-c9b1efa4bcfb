{% extends 'accounts/info.html' %}


{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">{{ _("Purchase history") }}</h3>
            </div>
            <div class="box-body">
                {% if subscriptions|length > 0 %}
                    <table class="table table-bordered small-font">
                        <tbody>
                            <tr>
                                <th>{{ _("Name") }}</th>
                                <th>{{ _("Status") }}</th>
                                <th>{{ _("Offer type") }}</th>
                                <th>{{ _("Created at") }}</th>
                                <th>{{ _("Expiration time") }}</th>
                            </tr>
                            {% for s in subscriptions %}
                                <tr id=subscription-{{ s.id }} >
                                    <td>
                                        {% if cms %}
                                            {% set committed_at = s.committed_at|localize_prebilling_datetime or s.created|localize_prebilling_datetime %}
                                            <span data-toggle="tooltip" data-placement="bottom" style="color: {{'grey' if s.is_uncommitted else 'green'}}"
                                                  data-original-title="{{ pgettext("purchase", "Not confirmed") if s.is_uncommitted else pgettext("purchase", "Confirmed") + ' ' + committed_at }}">
                                                <i class="fa fa-check-circle{{ '' if s.is_uncommitted else '-o' }}"></i>
                                            </span>
                                        {% endif %}
                                        {% set show_details = current_user.is_allowed(admin_view.endpoint, 'subscription_details') and not hide_subscription_details and not view_only %}
                                        {% if show_details %} <a href="{{ url_for(endpoint + '.subscriptions_subscription_details', id=account.id, s_id=s.id) }}"> {% endif %}
                                            {% if s.offer_id in offers_map %}
                                                {% set offer = offers_map[s.offer_id] %}
                                                {{ admin_view.format_offer(offer) }}
                                            {% else %}
                                                {{ s.offer_id }}
                                            {% endif %}
                                            {% if cms %}
                                                <a title="{{ _("Offer") }}" href="{{ url_for('offer.edit_view', id=s.offer_id) }}">
                                                    <i class="fa fa-link"></i>
                                                </a>
                                            {% endif %}
                                        {% if show_details %} </a> {% endif %}
                                    </td>
                                    <td>
                                        {% if s.state == "ACTIVE" %}
                                            <span class="badge bg-green">{{ _("Active") }}</span>
                                        {% elif s.state == "SUSPENDED" %}
                                            <span class="badge bg-yellow">{{ _("Suspended") }}</span>
                                        {% elif s.state == "DISABLED" %}
                                            <span class="badge bg-red">{{ _("Disabled") }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if s.offer_id in offers_map %}
                                            {{ offers_map[s.offer_id].typ }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ s.created|localize_prebilling_datetime }}
                                    </td>
                                    <td>
                                        {{ s.expire_time|localize_prebilling_datetime }}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <p class="text-muted text-center">{{ _("There are no purchases right now") }}</p>
                {% endif %}
            </div>
            <div class="box-footer">
                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default">{{ _("Back") }}</a>

                <ul class="pagination no-margin pull-right">
                    {% if frm > 0 %}
                        <li class="paginate_button previous">
                            <a href="{{ url_for(endpoint + '.subscriptions_subscriptions_history', id=account.id, page=page - 1) }}">
                                <i class="fa fa-long-arrow-left" aria-hidden="true"></i>
                            </a>
                        </li>
                    {% endif %}

                    {% if to < count - 1 %}
                        <li class="paginate_button next">
                            <a href="{{ url_for(endpoint + '.subscriptions_subscriptions_history', id=account.id, page=page + 1) }}">
                                <i class="fa fa-long-arrow-right" aria-hidden="true"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </form>
    </div>
{% endblock %}
