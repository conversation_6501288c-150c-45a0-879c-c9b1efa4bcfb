{% extends 'accounts/info.html' %}

{% set STATUS_TEXT = {0: _("Unknown"), 1: _("Processing"), 2: _("Purchase successful"), 3: _("Purchase error"), 4: _("Synchronization error"), 5: _("Synchronization successful")} %}
{% set STATUS_COLOR = {0: "bg-default", 1: "bg-yellow", 3: "bg-red", 4: "bg-red", 5: "bg-green"} %}


{% block content_info %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">
                    {{request_details.qs_transaction_id}}
                    <span class="badge {{STATUS_COLOR.get(request_details.status)}}">{{STATUS_TEXT.get(request_details.status)}}</span>
                </h3>
                <span class="pull-right">
                    <a title="{{ _("Archive") }}" href="{{ url_for(endpoint + '.upsale_archive_request', id=account.id, r_id=request_details.qs_transaction_id) }}"
                        onclick="return confirm('{{ _("Are you sure you want to archive the request?") }}')">
                        <i class="fa fa-archive"></i>
                    </a>
                    {% if request_details.status not in (2, 5) %}
                        <a title="{{ _("Retry") }}" href="{{ url_for(endpoint + '.upsale_retry_request', id=account.id, r_id=request_details.qs_transaction_id) }}" style="margin-left: 5px"
                            onclick="return confirm('{{ _("Are you sure you want to repeat your request?") }}')">
                            <i class="fa fa-refresh"></i>
                        </a>
                    {% endif %}
                </span><br/>
                {% if request_details.purchase_id %}
                    <a title="{{ _("Purchase") }}" href="{{ url_for(endpoint + '.subscriptions_subscription_details', id=account.id, s_id=request_details.purchase_id) }}">{{ _("Purchase") }}</a>
                {% endif %}
                {% if provider %}{% if request_details.purchase_id %} | {% endif %}
                    <a title="{{ _("Provider") }}" href="{{ url_for('provider.edit_view', id=provider.id) }}">{{provider.name}}</a>
                {% endif %}
                {% if offer %} |
                    <a title="{{ _("Offer") }}" href="{{ url_for('offer.edit_view', id=offer.id) }}">{{offer.name}}</a>

                    {% if trial %}
                        (<a title="{{ _("Trial") }}" href="{{ url_for('offer.edit_view', id=trial.id) }}">{{trial.name}}</a>)
                    {% endif %}
                {% endif %}
            </div>
            <div class="box-body" style="padding-left: 20px; padding-right: 20px">
                <p>
                    <ul class="list-unstyled">
                        {% set user = request_details.user %}
                        <li><b>{{ _("Login") }}</b>: {{ user.login }}</li>
                        <li><b>IP</b>: {{ user.client_i_p }}</li>
                        <li><b>{{ _("Phone") }}</b>: {{ user.phone }}</li>
                        <li><b>{{ _("Device ID") }}</b>: {{ user.device_i_d }}</li>
                        <li><b>{{ _("User ID") }}</b>: {{ user.user_i_d }}</li>
                        <li><b>{{ _("Profile ID") }}</b>: {{ user.profile_i_d }}</li>
                    </ul>
                    <hr/>
                    <h4>{{ _("Device") }}:</h4>
                    <ul class="list-unstyled">
                        {% set device_info = request_details.device_info %}
                        <li><b>ID</b>: {{ device_info.device_i_d }}</li>
                        <li><b>{{ _("Type") }}</b>: {{ device_info.device_type }}</li>
                        <li><b>{{ _("User Agent") }}</b>: {{ device_info.device_user_agent }}</li>
                        <li><b>{{ _("Manufacturer") }}</b>: {{ device_info.brand_name }}</li>
                        <li><b>{{ _("Model") }}</b>: {{ device_info.model_name }}</li>
                    </ul>
                    <hr/>
                    <h4>{{ _("Application") }}:</h4>
                    <ul class="list-unstyled">
                        {% set app_info = request_details.application_info %}
                        <li><b>{{ _("Version") }}</b>: {{ app_info.version }}</li>
                        <li><b>{{ _("Check sum") }}</b>: {{ app_info.hash }}</li>
                        <li><b>{{ _("Assembly type") }}</b>: {{ app_info.build_type }}</li>
                        <li><b>{{ _("Origin") }}</b>: {{ app_info.store_i_d }}</li>
                    </ul>
                </p>

                {% if request_details.retry_counter %}
                    <hr/>
                    <h4 class="box-title">{{ _("Number of attempts") }}: {{request_details.retry_counter}}</h4>
                {% endif %}

                {% if request_details.communication_records %}
                    <hr/>
                    <h4 class="box-title">{{ _("Communication") }}:</h4>
                    <table class="table table-bordered small-font">
                        <tbody>
                            <tr>
                                <th></th>
                                <th>{{ _("Time") }}</th>
                                <th>{{ _("Status") }}</th>
                                <th>{{ _("Method") }}</th>
                                <th>URL</th>
                           </tr>
                            {% for cr in request_details.communication_records|reverse %}
                                <tr data-cr-id={{loop.index}}>
                                    <td style="width: 50px">
                                        <a href="#" data-toggle="modal" data-target="#val-cr-{{loop.index}}" title="{{ _("Details") }}" style="margin: 3px 10px 0">
                                            <i class="fa fa-eye"></i>
                                        </a>
                                    </td>
                                    <td>{{cr.request_time|localize_datetime}}</td>
                                    <td style="text-align: center">
                                        <span class="badge {{"bg-green" if cr.status == 200 else "bg-red"}}">{{cr.status or "n\\a"}}</span>
                                    </td>
                                    <td>{{cr.method}}</td>
                                    <td><div style="max-width: 500px">{{cr.url|ellipsis_tip|clipboard}}</div></td>
                                </tr>
                                <div class="modal fade" id="val-cr-{{loop.index}}" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                                <h4 class="modal-title">
                                                    {{cr.request_time|localize_date(tz=none)}}
                                                    {% if cr.status == 0 %}
                                                    {% else %}
                                                        <span style="margin-bottom: 3px" class="badge {{"bg-green" if cr.status == 200 else "bg-red"}}">
                                                            {{cr.status or "n\\a"}}
                                                        </span>
                                                    {% endif %}
                                                    {{cr.method}}
                                                </h4>
                                            </div>
                                            <div class="modal-body">
                                                <pre>{{cr.url}}</pre>
                                                {% if cr.client_full_url %}
                                                    <pre>{{cr.client_full_url}}</pre>
                                                {% endif %}
                                                <hr/>
                                                {% if cr.raw_request_body %}
                                                    <h4 class="box-title">{{ _("Request") }}{% if cr.request_time %} {{cr.request_time|localize_time}}{% endif %}</h4>
                                                    {% if cr.request_headers %}
                                                        <pre>{{cr.request_headers.to_dict()|tojson|format_json}}</pre>
                                                    {% endif %}
                                                    <pre>{{cr.raw_request_body|format_json}}</pre>
                                                {% endif %}
                                                {% if cr.raw_response_body %}
                                                    <h4 class="box-title">{{ _("Response") }}{% if cr.request_finish_time %} {{cr.request_finish_time|localize_time}}{% endif %}</h4>
                                                    {% if cr.response_headers %}
                                                        <pre>{{cr.response_headers.to_dict()|tojson|format_json}}</pre>
                                                    {% endif %}
                                                    <pre>{{cr.raw_response_body|format_json}}</pre>
                                                {% endif %}
                                                {% if cr.errors %}
                                                    <h4 class="box-title">{{ _("Errors") }}:</h4>
                                                    {% for e in cr.errors|format_json %}
                                                        <pre>{{e}}</pre>
                                                    {% endfor %}
                                                {% endif %}
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-default" data-dismiss="modal">ОК</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </tbody>
                    </table>
                {% endif %}
            </div>
            <div class="box-footer">
                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default">{{ _("Back") }}</a>
            </div>
        </form>
    </div>
{% endblock %}
