{% extends 'accounts/info.html' %}

{% import 'common/utils.html' as utils %}
{% import 'accounts/components/stores/account_stores_lib.html' as stores_lib with context %}
{% from 'accounts/components/boxes/base.html' import paginated_box %}

{% block content_info %}
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">
                {% if subscription.offer_id in offers_map %}
                    {% set offer = offers_map[subscription.offer_id] %}
                    {{ admin_view.format_offer(offer) }}
                {% else %}
                    {{ subscription.offer_id }}
                {% endif %}
                {{ stores_lib.subscription_status(subscription) }}
            </h3>
            <div class="pull-right">
                <a style="margin-right: 10px" href="{{ url_for(endpoint + ".stores_update_subscription_state", id=account.id, s_id=subscription.id) }}" title="{{ _("Update state") }}">
                    <i class="fa fa-refresh"></i>
                </a>
            </div>
        </div>
        <div class="box-body">
            {{ stores_lib.subscription_details(subscription, hidden=["auto_renew_status"]) }}
        </div>
        <div class="box-footer">
            <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default">{{ _("Back") }}</a>
        </div>
    </div>

    {{ paginated_box(_("Notifications"), url_for(endpoint + '.stores_get_notifications_table', id=account.id, s_id=subscription.id)) }}
    {{ paginated_box(_("History"), url_for(endpoint + '.stores_get_history_table', s_id=subscription.id)) }}
    {{ paginated_box(_("Event log"), url_for(endpoint + '.stores_get_events_table', id=account.id, s_id=subscription.id)) }}
{% endblock %}

