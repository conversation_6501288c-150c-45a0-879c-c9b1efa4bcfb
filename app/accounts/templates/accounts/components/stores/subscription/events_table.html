{% import 'accounts/components/stores/account_stores_lib.html' as stores_lib with context %}

{% macro render_item(item, loop) %}
    <tr data-event-id={{ loop.index }}>
        <td>
            <a href="{{ url_for(endpoint + '.stores_event_details', id=account.id, e_id=item.event_id) }}" title="Детали события">
                {{ item.added_at|localize_datetime}}
            </a>
        </td>
        <td>
            {% if item.event_type %}
                <span class="badge bg-default">
                    {{ item.event_type}}
                </span>
            {% endif %}
        </td>
        <td>
            {% if item.initiator_data %}
                <a href="#" data-toggle="modal" data-target="#val-event-initiator-{{loop.index}}" title="{{ _("State details") }}">
                    <i class="fa fa-eye"></i>
                </a>
                {{ stores_lib.details_modal("val-event-initiator-{}".format(loop.index), _("Initiator"), body=stores_lib.initiator_details(item.initiator_data)) }}
            {% else %}
                n/a
            {% endif %}
        </td>
        <td>
            {% if item.subscription %}
                <a href="#" data-toggle="modal" data-target="#val-subscription-{{loop.index}}" title="{{ _("Subscription details") }}">
                    <i class="fa fa-eye"></i>
                </a>
                {{ stores_lib.details_modal("val-subscription-{}".format(loop.index), _("Subscription"), body=stores_lib.subscription_details(item.subscription, hidden=("origin_data", "origin_state_data"))) }}
            {% endif %}
        <td>
            {% if item.notification != "null" %}
                <a href="#" data-toggle="modal" data-target="#val-event-notification-{{loop.index}}" title="{{ _("Notification details") }}">
                    <i class="fa fa-eye"></i>
                </a>
                {{ stores_lib.details_modal("val-event-notification-{}".format(loop.index), _("Notification"), data=item.notification|json2dict|to_pretty_json) }}
            {% else %}
                n/a
            {% endif %}
        </td>
        <td>
            {% if item.origin_state != "null" %}
                <a href="#" data-toggle="modal" data-target="#val-event-origin-state-{{loop.index}}" title="{{ _("State details") }}">
                    <i class="fa fa-eye"></i>
                </a>
                {{ stores_lib.details_modal("val-event-origin-state-{}".format(loop.index), _("State"), data=item.origin_state|json2dict|to_pretty_json) }}
            {% else %}
                n/a
            {% endif %}
        </td>
    </tr>
{% endmacro %}

<table class="table small-font js-table">
    <thead>
        <tr>
            <td>{{ _("Added") }}</td>
            <td>{{ _("Initiator") }}</td>
            <td>{{ _("Initiator data") }}</td>
            <td>{{ _("Subscription") }}</td>
            <td>{{ _("Notification") }}</td>
            <td>{{ _("State") }}</td>
        </tr>
    </thead>
    <tbody>
        {% for notification in items %}
            {{ render_item(notification, loop) }}
        {% endfor %}
    </tbody>
</table>
