{% import 'accounts/components/stores/account_stores_lib.html' as stores_lib with context %}

{% macro render_item(loop, item) %}
    <tr data-history-id={{ loop.index }}>
        {% set time = item.added_at|localize_datetime %}
        <td style="width: 30px">
            <a href="#" data-toggle="modal" data-target="#val-k-history-{{ loop.index }}" title={{ _("Details") }}>
                <i class="fa fa-eye"></i>
            </a>
            {{ stores_lib.details_modal(
                    "val-k-history-{}".format(loop.index),
                    _("Subscription state at") + " {} {}".format(time, stores_lib.subscription_status(item.subscription)),
                    body=stores_lib.subscription_details(item.subscription, key="history".format(loop.index), hidden=["auto_renew_status"])) }}
        </td>
        <td>{{ time }}</td>
        <td>
            {{ stores_lib.subscription_status(item.subscription) }}
        </td>
    </tr>
{% endmacro %}

<table class="table small-font js-table">
    <thead>
        <tr>
            <th></th>
            <th>{{ _("Changed at") }}</th>
            <th>{{ _("Status") }}</th>
        </tr>
    </thead>
    <tbody>
        {% for item in items %}
            {{ render_item(loop, item) }}
        {% endfor %}
    </tbody>
</table>
