{% import 'accounts/components/stores/account_stores_lib.html' as stores_lib with context %}

{% macro render_item(item, loop) %}
    <tr data-notification-id={{ loop.index }}>
        {% set notification_time = item.date|localize_datetime %}
        <td style="width: 30px">
            <a href="#" data-toggle="modal" data-target="#val-notification-details-{{ loop.index }}" title="{{ _("Details") }}">
                <i class="fa fa-eye"></i>
            </a>
        </td>
        <td>{{ notification_time }}</td>
        {{ stores_lib.details_modal("val-notification-details-{}".format(loop.index), _("Notified at") + " " + notification_time, data=item.data|prettify_json_string) }}
    </tr>
{% endmacro %}

<table class="table small-font js-table">
    <thead>
        <tr>
            <th></th>
            <th>{{ _("Notification time") }}</th>
        </tr>
    </thead>
    <tbody>
        {% for notification in items %}
            {{ render_item(notification, loop) }}
        {% endfor %}
    </tbody>
</table>
