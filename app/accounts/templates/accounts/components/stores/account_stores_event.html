{% extends 'accounts/info.html' %}
{% import 'accounts/components/stores/account_stores_lib.html' as stores_lib with context %}


{% block content_info %}
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">{{ _("Event") }} {{ event.event_id }}</h3>
        </div>
        <div class="box-body">
            <ul class="list-unstyled">
                <li><b>{{ pgettext("event", "Added") }}</b>: {{event.added_at|localize_datetime}}</li>
                {% if event.event_type %}
                    <li><b>{{ _("Type") }}</b>: <span class="badge bg-default">{{ event.event_type }}</span></li>
                {% endif %}
            </ul>
        </div>
        <div class="box-footer">
            <a href="{{ url_for(endpoint + '.stores_subscription_details', id=account.id, s_id=event.subscription.id) }}" class="btn btn-default">{{ _("Back") }}</a>
        </div>
    </div>
    {% if event.subscription %}
        <div class="box box-primary">
            <div class="box-header">
                <h3 class="box-title">{{ _("Subscription") }}</h3>

                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse">
                        <i class="fa fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                {{ stores_lib.subscription_details(event.subscription) }}
            </div>
        </div>
    {% endif %}
    {% if not event.notification == "null" %}
        <div class="box box-primary">
            <div class="box-header">
                <h3 class="box-title">{{ _("Notification") }}</h3>

                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse">
                        <i class="fa fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                <pre>{{ event.notification|json2dict|to_pretty_json }}</pre>
            </div>
        </div>
    {% endif %}
    {% if not event.origin_state == "null" %}
        <div class="box box-primary">
            <div class="box-header">
                <h3 class="box-title">{{ _("Origin") }}</h3>

                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse">
                        <i class="fa fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                <pre>{{ event.origin_state|json2dict|to_pretty_json }}</pre>
            </div>
        </div>
    {% endif %}
    {% if event.initiator_data %}
        <div class="box box-primary">
            <div class="box-header">
                <h3 class="box-title">{{ _("Initiator") }}</h3>

                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse">
                        <i class="fa fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                {{ stores_lib.initiator_details(event.initiator_data) }}
            </div>
        </div>
    {% endif %}
{% endblock %}
