{#
`subscription` see  type definition at `app.stores.proto.stores.Subscription`
`SubscriptionActions` - see type definition at `app.accounts.components.stores_boxes.SubscriptionActions`
#}
{% from 'accounts/components/stores/account_stores_lib.html' import subscription_status %}

{% macro _get_confirm_message(subscription) -%}
    {%- if not admin_view.can_be_re_enabled(subscription) -%}
        {{ _("This action is irreversible!") }}&nbsp;
    {%- endif -%}
    {{ _("Are you sure you want to pause auto-renewal for the subscription?") }}
{%- endmacro %}

{% macro _render_actions_btn(subscription) %}
    {# "Actions" button, based on Yakassa implementation. #}
    {% set btn_id = "subscribtion_actions_btn_" + subscription.id %}

    <div class="dropdown">
        <a class="btn btn-default btn-sm btn-block dropdown-toggle"
           id="{{ btn_id }}"
           data-toggle="dropdown"
           aria-haspopup="true"
           aria-expanded="true"
        >
            {{ _("Actions") }} <span class="caret"></span>
        </a>
        <ul class="dropdown-menu dropdown-menu-right" aria-labelledby="{{ btn_id }}" style="margin-top: 35px; margin-left: -60px">
            {% if admin_view.is_action_allowed(subscription, SubscriptionActions.disable_renew) %}
                {# "Stop autorenewal" button #}
                <li>
                    <a class="text-yellow js-post-button"
                       data-url="{{ url_for(endpoint + ".stores_disable_autorenew", id=account.id) }}"
                       data-post-s_id="{{ subscription.id }}"
                       data-confirm="{{ _get_confirm_message(subscription) }}"
                       data-success-event="cms-refresh-box"
                    >
                        <i class="glyphicon glyphicon-pause"></i> {{ _("Pause auto-renewal") }}
                    </a>
                </li>
            {% elif admin_view.is_action_allowed(subscription, SubscriptionActions.enable_renew) %}
                {# "Resume autorenewal" button #}
                <li>
                    <a class="text-green js-post-button"
                       data-url="{{ url_for(endpoint + ".stores_enable_autorenew", id=account.id) }}"
                       data-post-s_id="{{ subscription.id }}"
                       data-confirm="{{ _("Are you sure you want to enable auto-renewal for the subscription?") }}"
                       data-success-event="cms-refresh-box"
                    >
                        <i class="glyphicon glyphicon-play"></i> {{ _("Enable auto-renewal") }}
                    </a>
                </li>
            {% endif %}
        </ul>
    </div>
{% endmacro %}

{% macro render_actions_btn(subscription) %}
    {% if admin_view.has_allowed_actions(subscription) %}
        {{ _render_actions_btn(subscription) }}
    {% else %}
        <p>{{ _("Actions are not available") }}</p>
    {% endif %}
{% endmacro %}

{% macro render_item(subscription) %}
    <tr data-subscription-id={{ subscription.id}}>
        <td>
            <a title="{{ _("Details") }}" href="{{ url_for(endpoint + '.stores_subscription_details', id=account.id, s_id=subscription.id) }}">
                {% if subscription.offer_id and subscription.offer_id in offers_map %}
                    {% set offer = offers_map[subscription.offer_id] %}
                    {{ admin_view.format_offer(offer) }}
                {% else %}
                    {{ subscription.id }}
                {% endif %}
            </a>
            <a title="{{ _("Offer") }}" href="{{ url_for('offer.edit_view', id=subscription.offer_id) }}">
                <i class="fa fa-link"></i>
            </a>
        </td>
        <td>
            {{ subscription_status(subscription.auto_renew_status) }}
        </td>
        <td>
            {{ subscription.payment_system_type }}
        </td>
        <td>
            {{ subscription.created_at|localize_datetime }}
        </td>
        <td>
            {{ subscription.expires_at|localize_datetime }}
        </td>
        <td>
            {{ render_actions_btn(subscription) }}
        </td>
    </tr>
{% endmacro %}

<table class="table table-bordered small-font">
    <tbody>
        <tr>
            <th>{{ _("Name") }}</th>
            <th>{{ _("Status") }}</th>
            <th>{{ _("Payment system") }}</th>
            <th>{{ _("Created at") }}</th>
            <th>{{ _("Expiration time") }}</th>
            <th>{# Actions column #}</th>
        </tr>
        {% for sub in subscriptions %}
            {{ render_item(sub) }}
        {% endfor %}
    </tbody>
</table>
