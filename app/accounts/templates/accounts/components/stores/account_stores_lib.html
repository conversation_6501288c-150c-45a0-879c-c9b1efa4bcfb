{% set AUTO_RENEW_STATUS = {
    0: pgettext("auto renewal", "unknown"),
    1: pgettext("auto renewal", "active"),
    2: pgettext("auto renewal", "not active"),
}%}

{% set SUBSCRIPTION_STATUS = {
    0: pgettext("subscription status", "Unknown"),
    1: pgettext("subscription status", "Active"),
    2: pgettext("subscription status", "Not active"),
}%}

{% macro render_data(data) %}
    <textarea style="width: 100%; margin: 0; background: white; border: 0; overflow-y: scroll; height: 500px">{{ data }}</textarea>
{% endmacro %}

{% macro render_cleaning_status(cleaning_info) %}
    {% set status = cleaning_info.status.name %}
    {% if status == 'CLEANING_STATUS_FAILED' %}
        <span class="label label-danger">{{ status }}</span>
    {% elif status == 'CLEANING_STATUS_RETRY' %}
        <span class="label label-warning">{{ status }}</span>
    {% else  %}
        <span class="label label-success">{{ status }}</span>
    {% endif %}
{% endmacro %}

{% macro render_cleaning_info_body(cleaning_info) %}
    <ul>
        <li><b>{{ _("Status") }}:</b> {{ render_cleaning_status(cleaning_info) }}</li>
        <li><b>{{ _("Counter") }}:</b> {{ cleaning_info.counter }}</li>
        <li><b>{{ _("Last failure reason") }}:</b> {{ cleaning_info.last_fail_reason }}</li>
        <li><b>{{ _("Next attempt") }}:</b> {{ cleaning_info.next_try_at|localize_datetime }}</li>
        <li><b>{{ _("Updated at") }}:</b> {{ cleaning_info.updated_at|localize_datetime }}</li>
    </ul>
{% endmacro %}

{% macro eye_modal(id, body, z_index=11000) %}
    <a href="#" data-toggle="modal" data-target="#{{ id }}" title={{ _("Details") }}>
        <i class="fa fa-eye"></i>
    </a>
    {{ details_modal(id, _("Data from source"), body=body, z_index=z_index) }}
{% endmacro %}

{% macro details_modal(id, title, data="", body="", z_index=10000) %}
    <div class="modal fade" id="{{ id }}" tabindex="-1" role="dialog" style="z-index: {{ z_index }}">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">{{ title|safe }}</h4>
                </div>
                <div class="modal-body {{ "no-padding" if data else ""}}" style="position: relative">
                    {% if data %}
                        {{ render_data(data) }}
                    {% elif body %}
                        {{ body }}
                    {% else %}
                        <p class="text-muted text-center">{{ _("No data") }}</p>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ _("Close") }}</button>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}


{% macro subscription_details(subscription, key="", hidden=[]) %}
    <ul class="list-unstyled">
        <li><b>{{ _("Transaction ID") }}</b>: {{ subscription.transaction_id }}</li>
        <li><b>{{ _("Product ID") }}</b>: {{ subscription.sku }}</li>
        <li><b>{{ _("Purchase ID") }}</b>: {{ subscription.prebilling_purchase_id }}</li>
        <li><b>{{ _("Offer ID") }}</b>: {{ subscription.offer_id }}</li>
        <li><b>{{ _("Payment system") }}</b>: {{ subscription.payment_system_type }}</li>
        <li><b>{{ _("Expiration date") }}</b>: {{ subscription.expires_at|localize_datetime }}</li>
        <li><b>{{ _("Retry period") }}</b>: {{ _('yes') if subscription.is_in_retry_period else _('no') }}</li>
        <li><b>{{ _("Grace period") }}</b>: {{ _('yes') if subscription.is_in_grace_period else _('no') }}</li>
        {% if "origin_data" not in hidden %}
            <li>
                <b>{{ _("Data from source") }}</b>
                <a href="#" data-toggle="modal" data-target="#val-k-origin-data" title="Детали">
                    <i class="fa fa-eye"></i>
                </a>
                {{ details_modal("val-k-origin-data", _("Data from source"), data=subscription.origin_data.to_dict()|to_pretty_json, z_index=11000) }}
            </li>
        {% endif %}
        {% if "origin_state_data" not in hidden %}
            <li>
                <b>{{ _("Origin state data") }}</b>
                <a href="#" data-toggle="modal" data-target="#val-k-origin-state-data" title="Детали">
                    <i class="fa fa-eye"></i>
                </a>
                {{ details_modal("val-k-origin-state-data", _("Data from source"), data=subscription.origin_state_data.to_dict()|to_pretty_json, z_index=11000) }}
            </li>
        {% endif %}
        {% if "auto_renew_status" not in hidden %}
            <li><b>{{ _("Auto renew status") }}</b>: {{ AUTO_RENEW_STATUS.get(subscription.auto_renew_status, 'n/a') }}</li>
        {% endif %}
        {% if subscription.cleaning_info %}
            <li>
                <b>{{ _('Cleaning status') }}</b>:
                {{ render_cleaning_status(subscription.cleaning_info) }}
            </li>
            <li>
                <b>{{ _('Cleaning info') }}:</b>
                {{ eye_modal("cleaning-info-modal", body=render_cleaning_info_body(subscription.cleaning_info)) }}
            </li>
        {% else %}
            <li><b>{{ _("Cleaning info") }}:</b> {{ _("no") }}</li>
        {% endif %}
    </ul>
{% endmacro %}


{% macro initiator_details(data) %}
    <ul class="list-unstyled">
        {% for k, v in data.items()|sort(attribute=0) %}
            <li><b>{{ k }}</b>: {{ v or "n/a" }}</li>
        {% endfor %}
    </ul>
{% endmacro %}


{% macro subscription_status(subscription) %}
    {% set color = {0: "bg-default", 1: "bg-green", 2: "bg-red"}.get(subscription.auto_renew_status, '') %}
    <span class="badge {{color}}">
        {{ SUBSCRIPTION_STATUS.get(subscription.auto_renew_status, '')  }}
    </span>
{% endmacro %}
