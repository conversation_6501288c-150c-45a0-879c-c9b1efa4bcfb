{% macro paginated_box(box_title, url) %}
    <div class="box box-primary js-paginated-box" data-url="{{ url }}">
        <div class="box-header">
            <h3 class="box-title" style="cursor: pointer" data-widget="collapse">{{ box_title }}</h3>
            <div class="box-tools pull-right">
                <button type="button" class="js-refresh-box btn btn-box-tool">
                    <i class="js-icon fa fa-refresh"></i>
                </button>
            </div>
        </div>
        <div class="box-body js-body">
            {# Here be table. #}
            <i class="fa fa-spinner fa-pulse"></i>
        </div>
        <div class="box-footer js-pagination">
            {# Here be pagination. #}
        </div>
    </div>
{% endmacro %}