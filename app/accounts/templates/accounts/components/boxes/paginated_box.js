$(function () {

    /**
     * Update paginated box from given url.
     *
     * @param $paginated_box
     * @param url
     * @returns {JQuery.jqXHR<any>}
     */
    function updateBox($paginated_box, url) {
        let $body = $paginated_box.find(".js-body");
        let $pagination = $paginated_box.find(".js-pagination");

        return $.get(url).done(function (response) {
            let cms_response = new CmsJsonResponse(response);
            $body.html(cms_response.data.html);
            $pagination.html(cms_response.data.pagination);
            $paginated_box.attr("data-url", url);
        });
    }

    function refreshBox($paginated_box) {
        let url = $paginated_box.attr("data-url");
        return updateBox($paginated_box, url);
    }

    // Init all boxes.
    $(".js-paginated-box").each(function (index, el) {
        refreshBox($(el));
    });

    ajaxButton(".js-paginate-button", function ($btn) {
        let url = $btn.attr("data-url");
        let $paginated_box = $btn.closest(".js-paginated-box");
        return updateBox($paginated_box, url);
    });

    $(document).on("cms-refresh-box", ".js-paginated-box", function (event) {
        event.stopPropagation();
        let $paginated_box = $(this);
        refreshBox($paginated_box);
    });

    // Explicitly use 'ajaxButton' instead of simple `trigger('cms-refresh-box')`,
    // because `ajaxButton` will block button (which is good) until current request is done or failed.
    ajaxButton(".js-refresh-box", function ($btn) {
        let $paginated_box = $btn.closest(".js-paginated-box");
        return refreshBox($paginated_box);
    });

});