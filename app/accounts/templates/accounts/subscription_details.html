{% extends 'accounts/info.html' %}


{% macro offer_state(info) %}
    {% if info.state == 0 %}
        <span class="badge bg-primary">{{ pgettext("offer state", "created") }}</span>
    {% elif info.state == 1 %}
        <span class="badge bg-green">{{ pgettext("offer state", "enabled") }}</span>
    {% elif info.state == 2 %}
        <span class="badge bg-warning">{{ pgettext("offer state", "changed") }}</span>
    {% elif info.state == 3 %}
        <span class="badge bg-red">{{ pgettext("offer state", "error") }}</span>
    {% elif info.state == 4 %}
        <span class="badge bg-red">{{ pgettext("offer state", "cancelled") }}</span>
    {% endif %}
{% endmacro %}


{% macro render_field(field, value, state_key=None) %}
    <b>{{ field }}</b>:
    {% if field in ['createdAt', 'expiresAt', 'disabledAt', 'startAt', 'endsAt'] %}
        {{ value|localize_prebilling_datetime }}
    {% elif field == "state" and state_key %}
        {{ states[state_key].values_by_number[value].name }}
    {% else %}
        {{ value }}
    {% endif %}
{% endmacro %}


{% macro vod_details(title, purchase, state_key=None) %}
    {% if purchase %}
        <div class="box box-primary" style="box-shadow: 0 0 !important">
            <div class="box-header with-border">
                <h3 class="box-title">{{ title }}</h3>
            </div>
            <div class="box-body">
                <ul class="list-unstyled">
                    {% for f, v in purchase.items() %}
                        <li>{{ render_field(f, v, state_key) }}</li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    {% endif %}
{% endmacro %}


{% block content_info %}
    {% set p = purchase %}
    <div class="box box-primary">
        <form method="post" class="form form-horizontal" role="form">
            <div class="box-header with-border">
                <h3 class="box-title">
                    {{ admin_view.format_offer(offer) }}
                    {% if p.state == "ACTIVE" %}
                        <span class="badge bg-green">{{ _("Active") }}</span>
                    {% elif p.state == "SUSPENDED" %}
                        <span class="badge bg-yellow">{{ _("Suspended") }}</span>
                    {% elif p.state == "DISABLED" %}
                        <span class="badge bg-red">{{ _("Disabled") }}</span>
                    {% endif %}
                    {% if cms %}
                        {% set committed_at = p.committed_at|localize_prebilling_datetime or p.created|localize_prebilling_datetime %}
                        <span data-toggle="tooltip" data-placement="bottom" style="color: {{'grey' if p.is_uncommitted else 'green'}}"
                              data-original-title="{{ pgettext("purchase", "Not confirmed") if p.is_uncommitted else pgettext("purchase", "Confirmed") + ' ' + committed_at }}">
                            <i class="fa fa-check-circle{{ '' if p.is_uncommitted else '-o' }}"></i>
                        </span>
                    {% endif %}
                </h3>
                {% if p.state != "DISABLED" %}
                    <div class="pull-right">
                        {% if current_user.is_allowed(admin_view.endpoint, 'retry_vod_purchases') and p.state != 'DISABLED' %}
                            <a style="margin-right: 10px" href="{{ url_for(endpoint + ".subscriptions_retry_vod_purchases", id=account.id, s_id=p.id, offer_id=offer.id) }}" title="{{ _("Re-creating VOD subscriptions") }}">
                                <i class="fa fa-refresh"></i>
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
            <div class="box-body">
                <p>
                    <ul class="list-unstyled">
                        <li><b>{{ _("Offer type") }}</b>: {{ offer.typ }}</li>
                        {% if p.created %}
                            <li><b>{{ _("Created at") }}</b>: {{ p.created|localize_prebilling_datetime }}</li>
                        {% endif %}
                        <li>
                            <b>{{ _("Expiration time") }}</b>: {{ p.expire_time|localize_prebilling_datetime or "n/a" }}
                            {% if current_user.is_allowed(admin_view.endpoint, 'set_purchase_expiration') and p.state != 'DISABLED' %}
                                <a style="margin-left: 5px" href="{{ url_for(endpoint + ".subscriptions_set_purchase_expiration", id=account.id, s_id=p.id, offer_id=offer.id) }}" title="{{ _("Change expiration date") }}">
                                    <i class="fa fa-clock-o"></i>
                                </a>
                            {% endif %}
                        </li>
                        {% if p.unsubscribed %}
                            <li><b>{{ _("Unsubscribed") }}</b></li>
                        {% endif %}
                    </ul>
                </p>

                {{ vod_details('Amedia', p.amedia_purchase["megogo"], 'megogo') }}
                {{ vod_details('Amedia2', p.amedia_2_purchase, 'amedia2') }}
                {{ vod_details('IVI', p.ivi_purchase, 'ivi') }}
                {{ vod_details('Kaz VOD', p.kazvod_purchase, 'kazvod') }}
                {{ vod_details('Megogo', p.megogo_purchase, 'megogo') }}
                {{ vod_details('Wink', p.moretv_purchase["megogo"], 'megogo') }}
                {{ vod_details('Start', p.start_purchase) }}
                {{ vod_details('VIP Play', p.vipplay_purchase) }}

                {% if p.change_info %}
                    <div class="box box-primary" style="box-shadow: 0 0 !important">
                        <div class="box-header with-border"><h3 class="box-title">{{ _("In the process of change") }}</h3></div>
                        <div class="box-body">
                            <ul class="list-unstyled">
                                <li>
                                    <b>{{ _("State") }}</b>: {{ offer_state(p.change_info) }}
                                </li>
                                {% if next_offer %}
                                    <li><b>{{ _("Offer") }}</b>: <a href="{{ url_for('offers.index') }}#{{ next_offer.id }}">{{ admin_view.format_offer(next_offer) }}</a></li>
                                {% endif %}
                                <li><b>{{ _("Date") }}</b>: {{ p.change_info.scheduled|localize_prebilling_datetime if p.change_info.scheduled else '' }}</li>
                            </ul>
                        </div>
                    </div>
                {% endif %}

                 {% if channels %}
                    <div class="box box-primary collapsed-box" style="box-shadow: 0 0 !important;">
                        <div class="box-header">
                            <h3 class="box-title">{{ _("Channel list") }}</h3>
                            <div class="box-tools pull-right">
                                <button type="button" class="btn btn-box-tool" data-widget="collapse">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="box-body">
                            <table class="table table-bordered table-hover dataTable small-font" role="grid">
                                <tr>
                                    <th>{{ _("Number") }}</th>
                                    <th>{{ _("Name") }}</th>
                                </tr>
                                {% for channel in channels %}
                                    <tr role="row">
                                        <td>{{ channel.channel_num or 0 }}</td>
                                        <td>{{ channel.title }}</td>
                                    </tr>
                                {% endfor %}
                            </table>
                        </div>
                    </div>
                {% endif %}
            </div>
            <div class="box-footer">
                <a href="{{ url_for(endpoint + '.info', id=account.id) }}" class="btn btn-default">{{ _("Back") }}</a>
            </div>
        </form>
    </div>
{% endblock %}
