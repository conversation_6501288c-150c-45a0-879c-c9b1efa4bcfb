<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">{{ _("Devices") }}</h3>
    </div>
    <div class="box-body">
        {% if devices|length > 0 %}
        <table class="table table-bordered small-font">
            <tbody>
                <tr>
                    <th>ID</th>
                    <th>Type ID</th>
                    <th>{{ _("Name") }}</th>
                    <th>{{ _("Created at") }}</th>
                    <th>{{ pgettext("devices table", "Updated at") }}</th>
                    {% if current_user.is_allowed(admin_view.endpoint, 'delete_device') and not view_only %}
                        <th></th>
                    {% endif %}
                </tr>
                {% for d in devices %}
                <tr data-device-id="{{d.id}}">
                    <td>
                        <a href="{{ url_for(endpoint + '.devices_manage_device', id=account.id, device_id=d.id) }}">{{ d.id }}</a>
                    </td>
                    <td>
                        {{ d.id_type }}
                    </td>
                    <td>
                        {{ d.name }}
                    </td>
                    <td>
                        {{ d.created_at|localize_prebilling_datetime }}
                    </td>
                    <td>
                        {{ d.updated_at|localize_prebilling_datetime }}
                    </td>
                    {% if current_user.is_allowed(admin_view.endpoint, 'delete_device') and not view_only %}
                        <td>
                            {% if d.deleted %}
                                <span style="color: red;">{{ _("deleted") }}</span>
                            {% else %}
                                <a href="{{ url_for(endpoint + '.devices_delete_device', id=account.id, device_id=d.id) }}"
                                   class="btn btn-danger btn-xs">{{ _("Delete") }}</a>
                            {% endif %}
                        </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p class="text-muted text-center">{{ _("There are no devices at the moment") }}</p>
        {% endif %}
    </div>
    <div class="box-footer">
        {% if current_user.is_allowed(admin_view.endpoint, 'manage_device_edit') and not view_only %}
            <a href="{{ url_for(endpoint + '.devices_manage_device', id=account.id) }}" class="btn btn-success">{{ _("Add device") }}</a>
        {% endif %}
    </div>
</div>
