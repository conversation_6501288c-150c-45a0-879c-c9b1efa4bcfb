from app.activity.service import BaseUserActionEvent

from .models import BaseAccountUserAction


class BaseAccountActionEvent(BaseUserActionEvent):
    """Represents a base action that was made with account."""

    model = BaseAccountUserAction

    def __init__(self, account_id: str, description: str = None):
        super().__init__(description)

        self.account_id = account_id

    def create_document(self) -> BaseAccountUserAction:
        document = super().create_document()
        document.account_id = self.account_id

        return document
