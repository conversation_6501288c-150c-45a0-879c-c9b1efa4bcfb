import json
import logging
import os
import uuid
from io import BytesIO
from typing import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)

import fs
import requests
from flask import (
    Response,
    abort,
    current_app,
    flash,
    redirect,
    request,
)
from flask_babelex import lazy_gettext as _
from jinja2 import Template
from markupsafe import Markup
from prebilling.misc import (
    encrypt_password,
    generate_password,
)
from prebilling.providers import (
    EMPTY_PROVIDER,
    Provider,
)
from prebilling.rest.accounts import AccountRequest
from prebilling.rest.providers import PROVIDER_STATE_APPROVED
from werkzeug.wsgi import FileWrapper

from app.accounts.components.common import password_url
from app.auth import expose
from app.cms.models import TaskTrigger
from app.cms.search import (
    SearchResult,
    register_view_searcher,
)
from app.comments.mixin import CommentMixin
from app.common.views import ProviderMixin
from app.purchases.report.celery_tasks import generate_provider_accounts_export_batch
from app.purchases.report.models import ProviderAccountsExport
from cmf.core.view import BasicView
from cmf.core.views.base_form_view import BaseFormView
from notifications import notify

from .components import (
    Account,
    AccountInfo,
    AccountsList,
    BatchSubscriptions,
    BlockingHistory,
    Comments,
    Component,
    Devices,
    Extra,
    Promo,
    Region,
    Sessions,
    Stores,
    Subscriptions,
    Upsale,
    UTMInfo,
    Yakassa,
)
from .components.base import ComponentHostMixin
from .components.forms import (
    ExportAccountsForm,
    create_new_account_form,
    providers_loader,
)

logger = logging.getLogger(__name__)


class AccountView(ComponentHostMixin, CommentMixin, ProviderMixin, BasicView):
    """Account view for managing accounts.

    It is an interface for rendering and managing account data,
    subscriptions and integrations with relevant services.

    Required permission to make the view visible is `view`.
    """

    extra_permissions = {"user_activity", "provider_info", "disable_trials"}

    # FIXME: DANGER! Mutable (!) shared lists of components. These lists are shared between components and the view.
    account_block: list
    content_block: list

    def __init__(self, *args, expose_to_global_search=False, **kwargs):
        self._default_view = "index"
        super().__init__(*args, **kwargs)
        if expose_to_global_search:
            register_view_searcher(self, self._global_search_account_by_id)

        # Mutable (!) shared lists of components. Shared between components and this view.
        self.account_block = []
        self.content_block = []

    def _global_search_account_by_id(self, id):
        prebilling_service = current_app.extensions["prebilling"]

        try:
            account: Account = prebilling_service.accounts.get(id)
            edit_url = self.get_url(f"{self.endpoint}.info", id=account.id)
            return SearchResult(found=True, redirect_url=edit_url)
        except requests.exceptions.HTTPError:
            return SearchResult(message=f"Account with id '{id}' not found.")

    def build_components(self) -> Generator[Component, None, None]:
        prebilling_service = current_app.extensions["prebilling"]

        self.list_component = AccountsList(
            method_name_prefix="",
            prebilling_service=prebilling_service,
            disable_cancel_subscription=False,
            disable_group_operations=False,
            hide_provider_columns=False,
            view_only=False,
            list_query={"include_purchases": True},
            cms=current_app.config["ADMIN_TYPE"] == "CMS",
        )
        yield self.list_component

        self.info_component = AccountInfo(method_name_prefix="", prebilling_service=prebilling_service)
        yield self.info_component

        # Block of components to render in the left column
        self.account_block += [
            Account(
                prebilling_service=prebilling_service,
                disable_user_deletion=False,
                hide_provider=False,
                view_only=False,
                web_url=current_app.config["WEB_URL"],
                notification_email=current_app.config["MAIL_USERNAME"],
                phone_regexp=current_app.config["ACCOUNT_PHONE_REGEXP"],
                password_length=current_app.config["AUTO_GENERATE_PASSWORD_LENGTH"],
                password_alphabet=current_app.config["AUTO_GENERATE_PASSWORD_ALPHABET"],
                cms=current_app.config["ADMIN_TYPE"] == "CMS",
            ),
            Extra(prebilling_service=prebilling_service),
            BlockingHistory(prebilling_service=prebilling_service),
            UTMInfo(prebilling_service=prebilling_service),
        ]
        yield from self.account_block

        # Block of components to render in the right column
        self.content_block += [
            Subscriptions(
                prebilling_service=prebilling_service,
                purchases_service=current_app.extensions["purchases"],
                disable_cancel_subscription=False,
                hide_subscription_details=False,
                view_only=False,
                offer_types=current_app.config["OFFER_TYPE_CHOICES"],
                show_integration=current_app.config["PURCHASES_INTEGRATION"],
                show_notification=current_app.config["PURCHASES_NOTIFICATION"],
                timezone=current_app.config["DEFAULT_TIMEZONE"],
                cms=current_app.config["ADMIN_TYPE"] == "CMS",
            ),
            BatchSubscriptions(
                method_name_prefix="subscriptions",
                prebilling_service=prebilling_service,
                disable_cancel_subscription=False,
                view_only=False,
                show_integration=current_app.config["PURCHASES_INTEGRATION"],
                show_notification=current_app.config["PURCHASES_NOTIFICATION"],
                timezone=current_app.config["DEFAULT_TIMEZONE"],
            ),
            Yakassa(
                prebilling_service=prebilling_service,
                yakassa_service=current_app.extensions["yakassa"],
                view_only=False,
            ),
            Stores(
                prebilling_service=prebilling_service,
                view_only=False,
            ),
            Devices(prebilling_service=prebilling_service, view_only=False),
            Region(prebilling_service=prebilling_service, view_only=False),
            Sessions(
                prebilling_service=prebilling_service,
                view_only=False,
            ),
            Promo(
                prebilling_service=prebilling_service,
                view_only=False,
            ),
            Upsale(prebilling_service=prebilling_service),
            Comments(comments=self.comments, view_only=False),
        ]
        yield from self.content_block

    def register_component(self, component: Component):
        component.account_block = self.account_block
        component.content_block = self.content_block
        super().register_component(component)


class AddAccountView(ProviderMixin, BasicView):
    account_endpoint = "clients"
    template = "accounts/new.html"

    show_provider = True
    show_phone = True
    show_password = True
    show_phone_confirmation = True

    def get_new_account_form(self):
        return create_new_account_form(
            show_provider=self.show_provider,
            show_password=self.show_password,
            show_phone=self.show_phone,
            show_phone_confirmation=self.show_phone_confirmation,
            show_integration=current_app.config["PURCHASES_INTEGRATION"],
            show_notification=current_app.config["PURCHASES_NOTIFICATION"],
            phone_regexp=current_app.config["ACCOUNT_PHONE_REGEXP"],
        )

    @expose("/", methods=("GET", "POST"))
    def index(self):
        form = self.get_new_account_form()(request.form)

        if request.method == "POST":
            if form.validate():
                try:
                    req = AccountRequest()
                    form.populate_obj(req)

                    provider = getattr(req, "provider", None)

                    if provider:
                        delattr(req, "provider")

                    acc, error = self.create_account_notify(req, provider or self.provider)

                    acc and flash(_("Account created successfully"), "success")  # Account is created

                    error and flash("{}: {}".format(_("Account creation error"), error), "error")

                    return redirect(self.get_url(self.account_endpoint + ".info", id=acc.id))
                except Exception as e:
                    flash("{}: {}".format(_("Account creation error"), e), "error")  # Error creating account: {}

        return self.render(self.template, form=form)

    @expose("/ajax/lookup/")
    def ajax_lookup(self):
        if not self.show_provider:
            abort(403)

        loader = providers_loader

        query = request.args.get("query")
        offset = request.args.get("offset", type=int)
        limit = request.args.get("limit", 10, type=int)

        data = [loader.format(m) for m in loader.get_list(query, offset, limit)]
        return Response(json.dumps(data), mimetype="application/json")

    def create_account_notify(self, request: AccountRequest, provider: Provider) -> Tuple[Account, str]:
        """Helper for account creation and notification."""
        prebilling_service = current_app.extensions["prebilling"]

        if request.provider is None:
            request.provider = {
                "id": str(provider.id) if provider else EMPTY_PROVIDER,
                "state": PROVIDER_STATE_APPROVED,
            }

        if request.login is not None:
            request.login = request.login.lower()

        if request.provider_login is None:
            request.provider_login = request.login.lower()

        password_in_msg = False
        if provider.generate_password is True:
            if not request.password:
                password = generate_password(
                    provider.password_length or current_app.config["AUTO_GENERATE_PASSWORD_LENGTH"],
                    provider.password_alphabet or current_app.config["AUTO_GENERATE_PASSWORD_ALPHABET"],
                )

                hsh, salt = encrypt_password(password)
                password_in_msg = True

                request.password = hsh
                request.salt = salt
            else:
                password = request.password
                hsh, salt = encrypt_password(password)
                password_in_msg = True

                request.password = hsh
                request.salt = salt
        else:
            request.restore_password_code = uuid.uuid4().hex
            request.password and delattr(request, "password")

        if not request.phone and request.is_phone_verified:
            delattr(request, "is_phone_verified")

        request.registered_from = current_app.config["ADMIN_TYPE"]
        request.is_valid = True

        acc, err = prebilling_service.accounts.create_account(request)

        if err:
            raise Exception(err)

        if provider:
            tmpl = Template(provider.new_account_text)

            sms_tmpl = None
            sms_message = None
            if provider.new_account_sms:
                sms_tmpl = Template(provider.new_account_sms)

            if not request.skip_notification:
                try:
                    if password_in_msg:
                        message = tmpl.render(username=acc.provider_login, password=password)
                        if sms_tmpl:
                            sms_message = sms_tmpl.render(username=acc.provider_login, password=password)
                        if sms_tmpl:
                            sms_message = sms_tmpl.render(username=acc.provider_login, password=password)
                    else:
                        url = password_url(
                            current_app.config["WEB_URL"],
                            acc,
                            acc.users[0].restore_password_code,
                        )
                        message = tmpl.render(url=url, username=acc.provider_login)
                        if sms_tmpl:
                            sms_message = sms_tmpl.render(url=url, username=acc.provider_login)

                    kwargs = {}

                    if sms_message:
                        kwargs["text_sms"] = sms_message

                    notify(acc, provider.new_account_subject, message, notifier=provider.notifier, **kwargs)
                except (Exception,) as err:
                    acc and prebilling_service.accounts.delete_account(acc.id)
                    raise err

        return acc, err


class ExportAccountsView(ProviderMixin, BaseFormView):
    form_class = ExportAccountsForm
    form: ExportAccountsForm

    template = "accounts/export.html"

    page_size = 20

    def form_valid(self):
        req = AccountRequest()
        self.form.populate_obj(req)

        generate_provider_accounts_export_batch.delay(
            task_trigger=TaskTrigger.MANUAL,
            provider_ids=[str(p.id) for p in req.providers],
        )
        return super().form_valid()

    def get_success_message(self):
        url = self.get_url(".index")
        return Markup(f"Accounts are being exported. Please <a href='{url}'>refresh</a> page to check its status.")

    def get(self):
        page = int(request.values.get("page", 1))

        exports_queryset = ProviderAccountsExport.objects.order_by("-started_at")
        exports_queryset = exports_queryset.only("provider", "status", "created_at", "finished_at")
        exports_paginator = exports_queryset.paginate(page, self.page_size)
        exports = exports_paginator.items

        return self.render(
            self.template, form=self.form, exports=exports, exports_paginator=exports_paginator, page=page
        )

    @expose("/ajax/lookup/")
    def ajax_lookup(self):
        query, offset, limit = (
            request.args.get("query"),
            request.args.get("offset", type=int),
            request.args.get("limit", 10, type=int),
        )
        data = [providers_loader.format(m) for m in providers_loader.get_list(query, offset, limit)]
        return Response(json.dumps(data), mimetype="application/json")

    @expose("/export/download", perm="export_download")
    def export_download(self):
        export = ProviderAccountsExport.objects.get_or_404(id=request.args.get("export_id"))
        export_filename = export.path.split("/")[-1].encode("utf-8").decode("latin-1")

        content = fs.open_fs(current_app.config["REPORTS_STORAGE"]).readbytes(export.path)

        resp = Response(FileWrapper(BytesIO(content)), mimetype="application/excel", direct_passthrough=True)
        resp.headers["Content-Disposition"] = f"attachment; filename={export_filename}"
        resp.headers["Content-type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        return resp

    @expose("/export/delete", perm="export_delete")
    def export_delete(self):
        id, page, export_id = request.args.get("id"), int(request.values.get("page", 1)), request.args.get("export_id")
        export = ProviderAccountsExport.objects.get_or_404(id=export_id)

        try:
            if export.path is not None:
                os.remove(export.path)
        except FileNotFoundError:
            flash(f"File {export.path} not found on file system. Document was removed from database.", "warning")

        export.delete()
        return redirect(self.get_url(".index", id=id, page=page))
