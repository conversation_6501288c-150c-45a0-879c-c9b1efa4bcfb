{% import 'common/utils.html' as utils %}
{% extends 'admin/model/cms_edit.html' %}

{% block form %}
    <div class="col-md-9">
        <div class="box box-primary">
            <div id="updater" class="box-body no-padding"></div>
        </div>
    </div>
{% endblock %}

{% block tail_js %}
    {{ super() }}
    <script src="{{ url_for('static', filename='js/compiled/purchase_updater.js') }}" type="text/javascript"></script>

    <script type="text/javascript">
        {% set offer_id = model.id|string() %}
        var Updater = App['purchase_updater'].default;
        var data = {{ dict(id=offer_id, endpoint=url_for(".updater"))|tojson }}

        Updater($('#updater').get(0), data);
    </script>
{% endblock %}
