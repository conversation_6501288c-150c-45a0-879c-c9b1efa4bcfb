{% extends 'purchases/offer/list.html' %}


{% block header %}
    <h1>{{ admin_view.name|capitalize }}</h1>
{% endblock %}


{% block head_css %}
    {{ super() }}

    <style type="text/css">

        input.action-checkbox, input.action-rowtoggle {
            cursor: pointer;
        }

        button#with-selected, button#with-selected+button, button#with-selected+ul.dropdown-menu {
            display: none;
        }

    </style>
{% endblock %}


{% block breadcrumb %}
    <ol class="breadcrumb" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
        <li><a href="{{ admin_view.admin.index_view.url }}"><i class="fa fa-home"></i>{{ _gettext('Home') }}</a></li>
        {% if provider_id %}
            <li><a href="{{ url_for('.general_settings', provider_id=provider_id) }}"> General </a></li>
        {% endif %}
        <li style="max-width: 200px; padding-top: 10px; text-overflow: ellipsis; white-space: nowrap" class="active">
            Select Offers
        </li>
    </ol>
{% endblock %}


{% block body %}

    {{ super() }}

    {% if not offers_only %}
        <section class="content-footer">
            <div class="row">
                <div class="col-md-12" style="padding: 0 30px; margin-bottom: 10px">
                    {% if provider_id %}
                        <form method="POST" id="add_offers">
                            <input type="hidden" name="offers" class="offers" />
                            {% set action = url_for(".index_view", provider_id=provider_id) %}
                            <a href="{{ url_for('.general_settings', provider_id=provider_id) }}" class="btn btn-default">Back</a>
                            <button type="submit" formaction="{{action}}" name="remove" class="btn btn-primary pull-right">Remove</button>
                            <button type="submit" formaction="{{action}}" name="add" class="btn btn-primary pull-right" style="margin-right: 5px">Add</button>
                        </form>
                    {% else %}
                        <a href="{{ url_for(".manage_providers", select=1) }}" class="btn btn-primary pull-right">Select Providers</a>
                    {% endif %}
                </div>
            </div>
        </section>
    {% endif %}

{% endblock %}


{% block tail_js %}
    {{ super() }}

    <script src="https://cdn.rawgit.com/mgalante/jquery.redirect/master/jquery.redirect.js"></script>

    <script>
        (function($) {
            $('.content div.col-xs-6').parent().append('<div id="offers_preview">');
            
            let get_offers = function() {
                return viewStorage.getItem('offers') ? viewStorage.getItem('offers').split(',') : [];
            }

            let set_offers = function(offers) {
                viewStorage.setItem('offers', [...new Set([...offers, ...get_offers()])]);

            }

            addEventListener('storage', function (e) {
                if (e.key === '{{admin_view.endpoint}}-offers') {
                    if (!viewStorage.getItem('offers')) {
                        $('input.action-checkbox, input.action-rowtoggle').prop('checked', false);
                    }
                }
            });

            $("input.action-checkbox[value='" + get_offers().join("'],[value='") + "']").prop( "checked", true);

            if ($("input.action-checkbox:checked").length == $("input.action-checkbox").length) {
                $('input.action-rowtoggle').prop("checked", true);
            }

            $('#filter_form>div a').replaceTag('<button>', true);
            $('#filter_form').attr('action', '{{ url_for(".index_view") }}');
            $('#filter_form').append('<input type="hidden" name="search" value="{{search or ''}}" />' +
                                     '<input type="hidden" name="provider_id" value="{{provider_id}}" />' +
                                     '<input type="hidden" class="offers" name="offers" value="{{offers|join(',')}}" />');

            $('form[role="search"] a').replaceTag('<button>', true);
            $('form[role="search"]').attr('action', '{{ url_for(".index_view") }}');
            $('form[role="search"]').append('<input type="hidden" name="provider_id" value="{{provider_id}}" />' +
                                            '<input type="hidden" class="offers" name="offers" value="{{offers|join(',')}}" />');

            $('#filter_form').submit(function(event) {
                if ($(this).data('clicked').text() === 'Reset Filters') {
                    $(this).find('table.filters').html('');
                    return true;
                }
            });

            $('form[role="search"]').submit(function(event) {
                $(this).append($('table.filters').clone().hide());

                if ($(this).data('clicked').prop('tagName') === 'I') {
                    $(this).find('input[name="search"]').val('');
                    return true;
                }
            });

            $('input.action-rowtoggle').change(function(event) {
                let selected_offers = $('input.action-checkbox:not(.dummy)', $('table.model-list')).map(function() {
                    return $(this).val();
                }).get();

                if (event.target.checked) {
                    set_offers(selected_offers);
                } else {
                    viewStorage.setItem('offers', get_offers().filter(function(value) {
                        return !selected_offers.includes(value)
                    }));
                }
            });

            $('input.action-checkbox').change(function(event) {
                let selected_offers = $('input.action-checkbox:not(.dummy):checked', $('table.model-list')).map(function() {
                    return $(this).val();
                }).get();

                if (!event.target.checked) {
                    viewStorage.setItem('offers', get_offers().filter(function(value) {
                        return value !== event.target.value
                    }));
                }

                set_offers(selected_offers);
            });

            $(document).on('keyup', '.remote-id', function(event) {
                if (event.target.value !== '') {
                    $.get('{{ url_for(".check_remote_id") }}?id=' + event.target.value, function(data) {
                        let $el = $(event.target);

                        if (data === 'True') {
                            $el.css('color', 'red');
                            $el.popover({
                                html: true,
                                trigger: 'hover',
                                placement: 'bottom',
                                container: $('body'),
                                content: function() {
                                    return '<span style="color:red">Id already exists!</span>'
                                }
                            }).popover('show');
                        } else {
                            $el.css('color', 'black');
                            $el.popover('destroy');
                        }
                    })
                }
            });
        })(jQuery);
    </script>
    
    {% with name = 'offers', step = 'offers' %}
        {% include 'common/preview_js.html' %}
    {% endwith %}

{% endblock %}
