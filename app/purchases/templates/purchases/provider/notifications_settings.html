{% extends admin_base_template %}


{% block head_css %}
    {{ super() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap3-wysiwyg/0.3.3/bootstrap3-wysihtml5.min.css">
{% endblock %}


{% block body %}
    <section class="content">
        <div class="row">
            <div class="col-sm-12">
                <div class="box box-primary">
                    <form method="post" class="form form-horizontal" role="form">
                        <div class="box-header with-border">
                            <h3 class="box-title">Настройка нотификаций нового пользователя</h3>
                        </div>
                        <div class="box-body">
                            <label class="col-sm-2 control-label">Доступные переменные</label>

                            <div class="col-sm-10" id="vars">

                            </div>

                            {% for field in form %}
                                {{ field() }}
                            {% endfor %}
                        </div>
                        <div class="box-footer">
                            <button type="submit" class="btn btn-success">Сохранить</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
{% endblock %}


{% block tail_js %}
    {{ super() }}
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap3-wysiwyg/0.3.3/bootstrap3-wysihtml5.all.min.js"></script>
    <script>
        $(function() {
            $("#message_new").wysihtml5();
            $("#message_reset").wysihtml5();
        })

        $(function() {
            var $auto = $("#auto"),
                $vars = $("#vars"),
                changeAuto = function(){
                    if (!$auto.is(':checked')) {
                        $vars.html("\
                            <p><strong>\{\{username\}\}</strong> &mdash; Логин<br/></p>\
                            <p><strong>\{\{url\}\}</strong> &mdash; Ссылка на задание пароля</p>\
                        ")
                    } else {
                        $vars.html("\
                            <p><strong>\{\{username\}\}</strong> &mdash; Логин<br/></p>\
                            <p><strong>\{\{password\}\}</strong> &mdash; Пароль</p>\
                        ")
                    }
                };
            $auto.on('change', function() {
                changeAuto();
            });
            changeAuto();
        })
    </script>
{% endblock %}
