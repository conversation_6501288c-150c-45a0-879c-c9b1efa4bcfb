{% extends "admin/model/cms_list.html" %}
{% import 'ads/carousel_banner/carousel_modals.html' as carousel_modals with context %}

{% block title_buttons %}
    <div class="btn-group">
        <a class="btn btn-primary" href="{{ get_url('.create_view', url=return_url) }}" title="{{ _gettext('Create new record') }}">
            <i class="fa fa-plus-circle"></i>
            {{ _gettext('Create') }}
        </a>
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            <span class="caret"></span>
            <span class="sr-only">Toggle Dropdown</span>
        </button>
        <ul class="dropdown-menu">
            <li>
                <a href="#" data-toggle="modal" data-target="#select-abcvod-title-modal" title="{{ _gettext('Create from ABCVOD Title') }}">
                    <i class="fa fa-plus-circle"></i>
                    {{ _gettext('Create from ABCVOD Title') }}
                </a>
            </li>
            <li>
                <a href="#" data-toggle="modal" data-target="#select-archive-title-modal" title="{{ _gettext('Create from Programmes Catalogue Title') }}">
                    <i class="fa fa-plus-circle"></i>
                    {{ _gettext('Create from Programmes Catalogue Title') }}
                </a>
            </li>
        </ul>
    </div>
{% endblock %}

{% block body %}
    {{ super() }}
    {{ carousel_modals.select_abcvod_title_modal(url_for(".create_from_title"), _gettext("Select")) }}
    {{ carousel_modals.select_archive_title_modal(url_for(".create_from_title"), _gettext("Select")) }}
{% endblock %}

{% block tail_js %}
    {{ super() }}
    <script>
        {% include "ads/carousel_banner/carousel_banner_list.js" %}
    </script>
{% endblock %}
