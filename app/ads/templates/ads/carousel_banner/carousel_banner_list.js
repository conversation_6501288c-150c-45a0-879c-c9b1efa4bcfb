$(function () {
    // Modal window selectors
    const SELECT_ABCVOD_TITLE_BTN = "#js-select-abcvod-title-btn";
    const ABCVOD_TITLE_SELECT2_SELECTOR = "#js-abcvod-title-select";

    // Archive modal window selectors
    const SELECT_ARCHIVE_TITLE_BTN = "#js-select-archive-title-btn";
    const ARCHIVE_TITLE_SELECT2_SELECTOR = "#js-archive-title-select";

     $(SELECT_ABCVOD_TITLE_BTN).on("click", function () {
        let $this_btn = $(this);
        if ($this_btn.attr("disabled")) return;
        $this_btn.attr("disabled", "disabled");
        let $select = $(ABCVOD_TITLE_SELECT2_SELECTOR);
        let title_id = $select.val();
        let get_url = $(this).attr("data-get-url");
        if (!title_id) {
            GlobalNotifications.error("Please select VOD.");
            return;
        }
        // https://select2.org/programmatic-control/retrieving-selections
        let db_alias = $select.find(':selected').attr("data-db-alias");
        $.get(get_url, {"title_id": title_id, db_alias: db_alias})
            .done(function (response) {
                $this_btn.removeAttr("disabled");
            });
    });

    $(SELECT_ARCHIVE_TITLE_BTN).on("click", function () {
        let $this_btn = $(this);
        if ($this_btn.attr("disabled")) return;
        $this_btn.attr("disabled", "disabled");
        let $select = $(ARCHIVE_TITLE_SELECT2_SELECTOR);
        let title_id = $select.val();
        let get_url = $(this).attr("data-get-url");
        if (!title_id) {
            GlobalNotifications.error("Please select VOD.");
            return;
        }
        $.get(get_url, {"title_id": title_id, "db_alias": "epg"})
            .done(function (response) {
                $this_btn.removeAttr("disabled");
            });
    });

});