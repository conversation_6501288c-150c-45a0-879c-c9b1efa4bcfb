{% macro select_abcvod_title_modal(btn_data_get_url, btn_text) %}
    <div class="modal fade" id="select-abcvod-title-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">{{ _gettext("Select VOD") }}</h4>
                </div>
                <div class="modal-body">
                    <select id="js-abcvod-title-select"
                            class="js-custom-select2"
                            data-ajax-url="{{ url_for('abcvod_search_title') }}"
                            data-dropdown-css-class="basevod-select2-items"
                    >
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ _gettext("Cancel") }}</button>
                    <button id="js-select-abcvod-title-btn" type="button" class="btn btn-primary" data-get-url="{{ btn_data_get_url }}">{{ btn_text }}</button>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}

{% macro select_archive_title_modal(btn_get_url, btn_text) %}
    <div class="modal fade" id="select-archive-title-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">{{ _gettext("Select VOD") }}</h4>
                </div>
                <div class="modal-body">
                    <select id="js-archive-title-select"
                            class="form-control"
                            data-role="select2-ajax"
                            data-url="{{ url_for(".ajax_lookup", name='title') }}"
                            data-placeholder="Please select"
                    >
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ _gettext("Cancel") }}</button>
                    <button id="js-select-archive-title-btn" type="button" class="btn btn-primary" data-get-url="{{ btn_get_url }}">{{ btn_text }}</button>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}