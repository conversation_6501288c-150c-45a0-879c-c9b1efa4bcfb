$(function () {
    // common selectors
    const ADD_ITEM_BTN_SELECTOR = ".js-add-item-btn";

    // Modal window selectors
    const FILL_FROM_VOD_BTN_SELECTOR = "#js-select-abcvod-title-btn";
    const ABCVOD_TITLE_SELECT2_SELECTOR = "#js-abcvod-title-select";

    // Archive modal window selectors
    const ARCHIVE_FILL_FROM_VOD_BTN_SELECTOR = "#js-select-archive-title-btn";
    const ARCHIVE_TITLE_SELECT2_SELECTOR = "#js-archive-title-select";

    // Rendering Data widget selectors.
    const RENDERING_DATA_ITEM_SELECTOR = ".js-rendering-data-item";
    const RENDERING_DATA_NAME_SELECTOR = ".js-rendering-data-name";

    // Multilang widget selectors.
    const MULTILANG_WIDGET_SELECTOR = ".js-multilang-widget";
    const MULTILANG_LINE_SELECTOR = ".js-multilang-widget-line";
    const MULTILANG_LOCALE_TD_SELECTOR = ".js-locale-td";
    const MULTILANG_TEXT_TD_SELECTOR = ".js-text-td";

    // "Prioritized Methods" widget selectors.
    const RENDERING_METHODS_FIELD_SELECTOR = "#rendering_methods";
    const ACTION_METHODS_FIELD_SELECTOR = "#action_methods";
    const PRIORITIZED_METHOD_ROW_SELECTOR = ".js-prioritized-methods-item";

    /**
     * Delete parameter from rendering data with certain "name".
     *
     * @param param - value of "name" field in rendering data widget.
     */
    function _deleteRenderingDataParameter(param) {
      let $input = $("#rendering_data").find(`${RENDERING_DATA_NAME_SELECTOR}>:input`).filter(function() {
          return $(this).val() === param;
      });
      $input.closest('.inline-field').remove();
    }

    /**
     * Return row with certain 'prioritized method' (if exists).
     *
     * @param $field - field with 'PrioritizedMethodsWidget'
     * @param method_name - name of method.
     * @returns {null|*}
     */
    function $getPrioritizedMethodRow($field, method_name) {
          let $result = $field
              .find('select option[value="' + method_name + '"]:selected')
              .closest(PRIORITIZED_METHOD_ROW_SELECTOR);
          if ($result.length > 0) {
              return $result.first();
          } else {
              return null;
          }
    }

    function setMethodPriority($method_row, priority) {
        $method_row.find(".js-priority input").val(priority);
    }

    function setMethodName($method_row, method_name) {
        $method_row.find(".js-method select").val(method_name).trigger("change");
    }

    function $addNewListItem($widget, item_selector) {
        let list_field_name = $widget.attr("id");
        let $btn = $widget.find(`>${ADD_ITEM_BTN_SELECTOR}`).first();
        faForm.addInlineField($btn[0], list_field_name, -1);
        return $widget.find(item_selector).last();
    }

    /*Same as $addNewListItem, but with different selector for $btn. */
    function $addNewImageItem($widget, item_selector) {
        let list_field_name = $widget.attr("id");
        let $btn = $widget.find(ADD_ITEM_BTN_SELECTOR).first();
        faForm.addInlineField($btn[0], list_field_name, -1);
        return $widget.find(item_selector).last();
    }

    function addOrSetPrioritizedMethod(field_selector, method_name, method_priority) {
        let $field = $(field_selector);
        let $method_row = $getPrioritizedMethodRow($field, method_name);
        if ($method_row !== null) {
            setMethodPriority($method_row, method_priority);
            return;
        }
        $method_row = $addNewListItem($field, PRIORITIZED_METHOD_ROW_SELECTOR);
        setMethodName($method_row, method_name);
        setMethodPriority($method_row, method_priority);
    }

    function fillOtherFields(response_data) {
        // Fill "Rendering Methods"
        for (const [method, priority] of Object.entries(response_data["rendering_methods"])) {
            addOrSetPrioritizedMethod(RENDERING_METHODS_FIELD_SELECTOR, method, priority);
        }
        // Fill "Action Methods"
        for (const [method, priority] of Object.entries(response_data["action_methods"])) {
            addOrSetPrioritizedMethod(ACTION_METHODS_FIELD_SELECTOR, method, priority);
        }
        // Fill images.
        if (response_data["image_background_16x9"]) {
            _fillMultilangImage("image_background_16x9", response_data["image_background_16x9"]);
        }
        if (response_data["image_source_icon_3x2"]) {
            _fillMultilangImage("image_source_icon_3x2", response_data["image_source_icon_3x2"]);
        }
        // Fill internal reference & vod content.
        $("#internal_reference-screen_id").val(response_data["internal_reference"]["screen_id"]).trigger("change");
        $("#internal_reference-vod_content-content_id").val(response_data["internal_reference"]["content_id"]);
        $("#internal_reference-vod_content-source").val(response_data["internal_reference"]["content_source"]);
    }


    function _fillMultilangData($multilang_widget, data) {
        if (typeof data === 'string' || typeof data === 'number') {
            $multilang_widget.find(`${MULTILANG_TEXT_TD_SELECTOR}>input`).first().val(data);
            return;
        }
        if (typeof data === 'object') {
            // Assume `data` is translation dict, like {"default":"Образовательное","en":"Education","kz":"Білім"}
            $multilang_widget.find(MULTILANG_LINE_SELECTOR).remove();  // Remove default empty line from widget.
            for (const [locale, text] of Object.entries(data)) {
                let $multilang_line = $addNewListItem($multilang_widget, MULTILANG_LINE_SELECTOR);
                let $locale_select = $multilang_line.find(MULTILANG_LOCALE_TD_SELECTOR).find("select");
                $locale_select.val(locale).trigger("change");
                let $text_input = $multilang_line.find(MULTILANG_TEXT_TD_SELECTOR).find("input");
                $text_input.val(text);
            }
            return;
        }
        GlobalNotifications.warning(`Unexpected data type - ${typeof data}`)
    }

    /**
     *
     * @param multilang_field_id
     * @param data {ImageData}
     * @private
     */
    function _fillMultilangImage(multilang_field_id, data) {
        const $widget = $(`#${multilang_field_id}`);
        const $added_item = $addNewImageItem($widget, ".inline-field-list .inline-field");
        if (!$added_item) {
            GlobalNotifications.error(`Error adding multilang image for ${multilang_field_id}`);
            return;
        }
        const $field = $added_item.find("input").first();
        triggerUpdateCmsImage($field, data);
    }

    function _addRenderingDataParameter(name, value) {
        let $rendering_data_widget = $("#rendering_data");
        let $latest_item = $addNewListItem($rendering_data_widget, RENDERING_DATA_ITEM_SELECTOR);
        let $name_input = $latest_item.find(RENDERING_DATA_NAME_SELECTOR).find("input").first();
        $name_input.val(name);
        let $multilang_widget = $latest_item.find(MULTILANG_WIDGET_SELECTOR).first();
        _fillMultilangData($multilang_widget, value);
    }

    function fillRenderingData(data) {
        for (const [name, value] of Object.entries(data)) {
            if (value === null || value === "") continue;
            if (typeof value === "object" && Object.keys(value).length === 0) continue;
            _deleteRenderingDataParameter(name);
            _addRenderingDataParameter(name, value);
        }
    }

    function _fillFormFromResponse($btn, response) {
        let cms_response = new CmsJsonResponse(response);
        try {
            fillRenderingData(cms_response.data["rendering_data"]);
            fillOtherFields(cms_response.data);
            GlobalNotifications.success("Rendering data filled from VOD info successfully!");
            $btn.closest(".modal").modal("hide");
        } catch (e) {
            console.error(e);
            GlobalNotifications.error("Something went wrong during 'fillRenderingData' operation.");
        }
    }

    $(FILL_FROM_VOD_BTN_SELECTOR).on("click", function () {
        let $this_btn = $(this);
        if ($this_btn.attr("disabled")) return;
        $this_btn.attr("disabled", "disabled");
        let $select = $(ABCVOD_TITLE_SELECT2_SELECTOR);
        let title_id = $select.val();
        let get_url = $(this).attr("data-get-url");
        if (!title_id) {
            GlobalNotifications.error("Please select VOD.");
            return;
        }
        // https://select2.org/programmatic-control/retrieving-selections
        let db_alias = $select.find(':selected').attr("data-db-alias");
        $.get(get_url, {"title_id": title_id, db_alias: db_alias})
            .done(function (response) {
                _fillFormFromResponse($this_btn, response);
            })
            .always(function () {
                $this_btn.removeAttr("disabled");
            });
    });

    $(ARCHIVE_FILL_FROM_VOD_BTN_SELECTOR).on("click", function () {
        let $this_btn = $(this);
        if ($this_btn.attr("disabled")) return;
        $this_btn.attr("disabled", "disabled");
        let $select = $(ARCHIVE_TITLE_SELECT2_SELECTOR);
        let title_id = $select.val();
        let get_url = $(this).attr("data-get-url");
        if (!title_id) {
            GlobalNotifications.error("Please select VOD.");
            return;
        }
        $.get(get_url, {"title_id": title_id, "db_alias": "epg"})
            .done(function (response) {
                _fillFormFromResponse($this_btn, response);
            })
            .always(function () {
                $this_btn.removeAttr("disabled");
            });
    });
});
