{% extends admin_base_template %}

{% block tail_js %}
{{ super() }}
<script type="text/javascript">
    $(function () {
        let conf = {autoclose: true, minView: 2, startView: 2}

        $('#start_date').datetimepicker(conf);
        $('#end_date').datetimepicker(conf);
    });
</script>
<script type="text/javascript">
    $(document).ready(function () {
        $('#refresh').on('click', function () {
            var search_params = new URLSearchParams(window.location.search);
            search_params.set('start_date', $('#start_date').val());
            search_params.set('end_date', $('#end_date').val());
            window.location.href = '?' + search_params.toString();
        })
    });
</script>
<script type="text/javascript"
    src="https://adminlte.io/themes/AdminLTE/bower_components/datatables.net-bs/js/dataTables.bootstrap.min.js"></script>

<script type="text/javascript">
    $(document).ready(function () {
        var data = JSON.parse("{{values|safe}}".replace(/'/g, '"'))
        var ChartCanvas = $('#chart').get(0).getContext('2d')
        var bar_labels = []
        var bar_data = []
        for (key in data) {
            bar_labels.push(key);
            bar_data.push(data[key]['number']);
        }
        // sort data
        indexed_bar_data = bar_data.map(function(e,i){return {ind: i, val: e}});
        indexed_bar_data.sort(function(x, y){return x.val > y.val ? 1 : x.val == y.val ? 0 : -1});
        indices_of_sorted = indexed_bar_data.map(function(e){return e.ind}).reverse().slice(0,10);
        top_10_labels = [];
        top_10_data = [];
        for (label_index in indices_of_sorted) {
            top_10_labels.push(bar_labels[indices_of_sorted[label_index]]);
            top_10_data.push(bar_data[indices_of_sorted[label_index]]);
        }
        var randomColor = function (num) {
            colors = [];
            for (col_num=0; col_num < num; col_num++) {
                colors.push('#' + (Math.random().toString(16) + '0000000').slice(2, 8));
            }
            return colors
        };
        var myChart = new Chart(ChartCanvas, {
            type: 'bar',
            data: {
                labels: top_10_labels,
                datasets: [{
                    label: '',
                    data: top_10_data,
                    backgroundColor : randomColor(5),
                    borderWidth: 1
                }]
            },
            options: {
                legend: {
                    display: false
                },
                scales: {
                    xAxes: [{
                        ticks: {
                            callback: function(value, index, values) {
                                return '';
                            }
                        }
                    }]
                }
            }
        });
    });
</script>
<script type="text/javascript">
    $(function () {
        var table = $('#table').DataTable({
            'paging': false,
            'lengthChange': false,
            'searching': false,
            'ordering': true,
            'desc': true,
            'info': true,
            'autoWidth': false
        });

        table.order([1, 'desc']).draw();

        var table_rows = $('.table_row');
        var total_value = parseInt("{{total_value}}");

        function get_other_value_row() {
            var other_row = '<tr id="other_val"><td>Other</td>';
            var other_value = 0;
            for (var i = 0; i < table_rows.length; i++) {
                if ($(table_rows[i])[0].classList.contains('collapse')) {
                    other_value += parseInt($(table_rows[i])[0].getElementsByTagName('td')[1].textContent.split('(')[0]);
                }
            }
            var other_value_fraction = parseFloat(other_value / total_value * 100).toFixed(2);
            other_row += '<td>' + other_value + ' (' + other_value_fraction + '%)';
            other_row += '</tr>';
            return other_row;
        }

        for (var i = 10; i < table_rows.length; i++) {
            $(table_rows[i]).addClass('collapse');
        }
        var expand_row = '<tr><td colspan="100%" style="text-align: center"><button id="expand_rows" class="btn btn-primary btn-xs"><i class="fa fa-plus"></i> Expand</button></td></tr>';
        if (table_rows.length > 10) {
            var other_row_initial = get_other_value_row();
            $('#table').find('tbody').append(other_row_initial);
            $('#table').find('tbody').append(expand_row);
        }
        $('#expand_rows').on('click', function () {
            var collapsed_rows = $('.collapse');
            for (i = 0; i < 10; i++) {
                $(collapsed_rows[i]).removeClass('collapse');
            }

            if ($('.collapse').length === 0) {
                $('#expand_rows').closest('tr').remove();
                $('#other_val').remove();
            } else {
                var other_row = get_other_value_row();
                $('#other_val').replaceWith(other_row);
            }

        });
    })
</script>

{% endblock %}

{% block body %}

    <section class="content-header">
        {% block header %}
        <h1>
            VOD unique accounts counted by pauses
        </h1>
        {% endblock %}

        {% block breadcrumb %}
        <ol class="breadcrumb">
            <li><a href="{{ admin_view.admin.index_view.url }}"><i class="fa fa-home"></i>{{ _gettext('Home') }}</a></li>
        </ol>
        {% endblock %}
    </section>

    <section class="content" style="overflow: auto;">
        {% block form %}
        <div class="row">
            <div class="col-xs-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">Dates range</h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-sm-2">
                                Start date
                            </div>
                            <div class="col-sm-10">
                                <input class="form-control" data-date-format="yyyy-mm-dd" id="start_date" type="text"
                                    value="{{start_date}}">
                            </div>
                        </div><br>
                        <div class="row">
                            <div class="col-sm-2">
                                End date
                            </div>
                            <div class="col-sm-10">
                                <input class="form-control" data-date-format="yyyy-mm-dd" id="end_date" type="text"
                                    value="{{end_date}}">
                            </div>
                        </div><br>
                    </div>
                    <div class="box-footer">
                        <button type="submit" class="btn btn-default" id="refresh"><i class="fa fa-refresh"></i> Regenerate</button>
                    </div>
                </div>
            </div>
        </div>
        {% endblock %}
        <div class="row">
            <div class="col-sm-7">
                <div class="box box-primary">
                    <div class="box-body">
                        <div class="row">
                            <div class="col-xs-12">
                                <table id="table" class="table table-bordered table-hover dataTable" role="grid">
                                    <thead>
                                        <tr>
                                            <th class="sorting">Source</th>
                                            <th class="sorting">Unique accounts</th>
                                            <th class="sorting">Unique accounts in %</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for key in values %}
                                        <tr class="table_row">
                                            <td>
                                                {{key}}
                                            </td>
                                            <td>
                                                {{ values[key]['number'] }}
                                            </td>
                                            <td>
                                                {{ values[key]['fraction'] }}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td><strong>Total</strong></td>
                                                <td><strong>{{ total_value }}</strong></td>
                                                <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-5">
                <div class="box box-primary">
                    <div class="box-header">
                        <h3 class="box-title">
                            VOD unique accounts
                        </h3>
                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i
                                    class="fa fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-box-tool" data-widget="remove"><i
                                    class="fa fa-times"></i></button>
                        </div>
                    </div>
                    <div class="box-body">
                        <canvas id="chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}
