{% extends admin_base_template %}
{% from 'clickhouse/drilldown_macros.html' import boxtools, dates, main_query, main_breadcrumb, chart_breadcrumb, chart %}

{% block tail_js %}
{{ super() }}

<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@0.7.0"></script>

<script type="text/javascript">
    $(document).ready(function () {
        let conf = {autoclose: true, minView: 2, startView: 2}

        $('#start_date').datetimepicker(conf);
        $('#end_date').datetimepicker(conf);

        changeDate('start_date', "{{ start_date }}");
        changeDate('end_date', "{{ end_date }}");

        $('.total_value').each(function () {
            var text = $(this)[0].textContent;
            $(this).html('<strong>' + formatNumber(text) + '</strong>');
        });
    });
</script>

<script type="text/javascript">
    $(document).ready(function () {
        $('.chart').each(function () {
            let data = $(this).data('data');
            data = JSON.parse(data.replace(/'/g, '"'));
            let groupbyDimension = "{{ groupby_dimension }}";
            let newGroupbyDimension = "{{ new_groupby_dimension }}";

            let type = $(this).data('type');
            if (type == 'pie') {
                type = 'doughnut';
            }

            let metric = $(this).data('metric');
            let total = $(this).data('total');
            let other = $(this).data('other');
            let canvas = $(this).find('canvas').get(0)

            config = getConfig(type, data, metric, other, total, null,
                                {{ config.CLICKKHOUSE_DRILLDOWN_MAX_ELS_ON_BARPLOT }},
                                {{ config.CLICKKHOUSE_DRILLDOWN_MAX_VISIBLE_LABELS_ON_PIEPLOT }})
            excelData = getExcelData(metric, groupbyDimension, newGroupbyDimension, data, other, total, type, labels=null)

            var downloadButton = $(this).find(".download-chart")
            downloadButton.on("click", function () {
                var plotType = (type == 'doughnut') ? 'pie' : type
                var filename = plotType + '_chart_' + moment().format("MM_DD_YY_hh_mm_ss");
                var ws = XLSX.utils.json_to_sheet(excelData, { skipHeader: true, dateNF: 'YYYYMMDD HH:mm:ss' });
                var wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, filename);
                XLSX.writeFile(wb, filename + ".xlsx");
            });

            new Chart(canvas, config)
        });

    });
</script>
<script type="text/javascript">
    var metrics_num = JSON.parse("{{ metrics|safe }}".replace(/'/g, '"')).length;
    var metrics = JSON.parse("{{ metrics|safe }}".replace(/'/g, '"'));
    var total_values = JSON.parse("{{ total_values|safe }}".replace(/'/g, '"'));
    var columnDefs = [];
    for (var i = 0, j = metrics_num; i < j; ++i) {
        var count_num = 2 * i + 1;
        columnDefs.push({
            'render': function (data, type, row, meta) {
                if (type === "display") {
                    return formatNumber(data) + ' (' + row[meta.col + 1] + '%)';
                }
                return data;
            },
            'targets': count_num,
        });
        columnDefs.push({
            "visible": false, "targets": [count_num + 1]
        });
    }
    $(function () {
        var table = $('#table').DataTable({
            'columnDefs': columnDefs,
            'paging': false,
            'lengthChange': false,
            'searching': false,
            'ordering': true,
            'desc': true,
            'info': true,
            'autoWidth': false
        });

        table.order([1, 'desc']).draw();

        var other_values = JSON.parse("{{ other_values|safe }}".replace(/'/g, '"'));
        var expand_row = '<tr><td colspan="100%" style="text-align: center"><button id="expand_rows" class="btn btn-primary btn-xs"><i class="fa fa-plus"></i> Expand</button></td></tr>';
        if (other_values.length != 0) {
            var other_row = '<tr><td><strong>Other</strong></td>';
            for (var other_ind = 0, other_len = metrics.length; other_ind < other_len; ++other_ind) {
                var o_val = other_values[metrics[other_ind]];
                var o_val_perc = parseFloat(o_val / total_values[metrics[other_ind]] * 100).toFixed(2);
                other_row += '<td><strong>' + formatNumber(o_val) + ' (' + o_val_perc + '%)' + '</strong></td>';
            }
            other_row += '</tr>';
            $('#table').find('tbody').append(other_row);
            $('#table').find('tbody').append(expand_row);
        }

        $('#expand_rows').on("click", function () {
            var search_params = new URLSearchParams(window.location.search);
            var curr_num_rows = parseInt(search_params.get('num_rows')) || 10;
            search_params.set('num_rows', curr_num_rows + 10);
            window.location.href = '?' + search_params.toString();

        });
    })
</script>
<script type="text/javascript">
    $(document).ready(function () {
        $(function () {
            $("[data-toggle='tooltip']").tooltip();
        });
    });
</script>
{% endblock %}

{% block body %}
{{ main_query(id='sql', query=query) }}
{{ main_query(id='sql-bydates', query=with_date_query) }}

{% set fixed_dims = [] %}
{% for dim, dim_value in fixed_dimensions %}
{% set fixed_dim = '&fixed_dimension=' ~ dim ~ ':' ~ dim_value %}
{% if fixed_dims.append(fixed_dim) %}{% endif %}
{% endfor %}
{% set prev_fixed_dimensions = fixed_dims|join('&') %}
{% if new_groupby_dimension %}
{% set groupby_name = groupby_dimension + '(' + new_groupby_dimension + ')' %}
{% else %}
{% set groupby_name = groupby_dimension %}
{% endif %}

<section class="content-header">
    {% block header %}
    <h1>
        {% if analytics_template %}{{ analytics_template.name }}{% else %}&nbsp;{% endif %}
        {% if admin_view.can_edit and admin_view.report_is_editable %}
            <small>
                <a href="{{ get_url(".edit_view", id=request.args.get("report_id")) }}">
                    <i class="fa fa-pencil"></i>
                </a>
            </small>
        {% endif %}
    </h1>
    {% endblock %}

    {% block breadcrumb %}
    {{ main_breadcrumb(template_name=analytics_template.name, admin_view=admin_view) }}
    {% endblock %}
</section>

<section class="content" style="overflow: auto;">
    {% if clh_error %}
    <div class="row">
        <div class="col-xs-12">
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="icon fa fa-ban"></i>Error occured!</h4>
                {% if clh_error.text %} {{ clh_error.text }} {% else %} {{ clh_error }} {% endif %}
            </div>
        </div>
    </div>
    {% else %}
    {% if description %}
    <div class="box box-primary">
        <div class="box-body">
            {{ description|safe }}
        </div>
    </div>
    {% endif %}
    <div class="row">
        <div class="col-xs-12">
            <div class="box box-primary collapsed-box">
                <div class="box-header">
                    <h3 class="box-title">
                        Report parameters
                    </h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i>
                        </button>
                    </div>
                </div>
                <div class="box-body">
                    <div>
                        <table>
                            <tbody>
                                <tr>
                                    <th><strong>metrics</strong></th>
                                </tr>
                                <tr>
                                    <td>&emsp;{{ analytics_template['metrics']|join(', ') }}</td>
                                </tr>
                                {% for parameter in analytics_template %}
                                {% if parameter not in ('created', 'updated_at', 'dimensions', 'metrics', 'description') %}
                                <tr>
                                    <th><strong>{{ parameter }}</strong></th>
                                </tr>
                                <tr>
                                    <td>&emsp;{{ analytics_template[parameter] }}</td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                                <tr>
                                    <th><strong>dimensions</strong></th>
                                </tr>
                                <tr>
                                    <td>&emsp;{{ analytics_template['dimensions']|join(', ') }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% block form %}
        {{ dates(start_date=start_date, end_date=end_date) }}
    {% endblock %}
    {% for metric in metrics_bydate_data %}

    <div>
    {{ chart(type='line', chart_data=metrics_bydate_data[metric], name='Chart by dates for ' + metric,
                 metric=metric, total_value=total_values[metric], prev_fixed_dimensions=prev_fixed_dimensions,
                 sql_target="#sql-bydates", other=other_values[metric], template_id=analytics_template.id) }}
    </div>
    {% endfor %}


    <div class="row">
        <div class="col-sm-7">
            <div class="box box-primary">
                {{ boxtools(name='Data', sql_target='#sql',
                        download_table_href=url_for('clickhouseplaybackinfostat.download_analytics_table',
                        report_id=analytics_template.id, start_date=start_date, end_date=end_date ) + prev_fixed_dimensions,
                        prev_fixed_dimensions=prev_fixed_dimensions, fn=report_filename) }}
                <div class="box-body">
                    <div class="row">
                        <div class="col-xs-12">
                            {{ chart_breadcrumb(fixed_dimensions=fixed_dimensions, analytics_template=analytics_template, mapped_fixed_val_dims=mapped_fixed_val_dims,
                                groupby_dimension=groupby_dimension, new_groupby_dimension=new_groupby_dimension, url=True) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <table id="table" class="table table-bordered table-hover dataTable" role="grid">
                                <thead>
                                    <tr>
                                        <th class="sorting" style="max-width: 100px"
                                            data-toggle="tooltip" data-placement="top" data-container="body"
                                            data-original-title="{{ dimensions_hints[groupby_dimension] or '' }}">
                                            {{ groupby_name|ellipsis_tip }}
                                        </th>
                                        {% for metr in metrics %}
                                            <th class="sorting" style="max-width: 50px"
                                                data-toggle="tooltip" data-placement="top"
                                                data-container="body" data-original-title="{{ metrics_hints[metr] or '' }}">
                                                {{ metr|ellipsis_tip }}
                                            </th>
                                            <th class="sorting" style="max-width: 50px">
                                                {{ (metr + " in %")|ellipsis_tip }}
                                            </th>
                                        {% endfor %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for dimension in values %}
                                    {% set value = values[dimension]['data'] %}
                                    {% set dim_col_value = values[dimension]['label'] %}
                                    <tr class="table_row">
                                        <td>
                                            {% if fixed_dimensions and futher_detail_possible %}
                                            <a href="?fixed_dimension={{ groupby_dimension }}:{{ dimension }}{{ prev_fixed_dimensions }}&report_id={{ analytics_template.id }}"
                                                class="clh_href">
                                                {% if dim_col_value %}{{ dim_col_value }}{% else %} NaN {% endif %}
                                            </a>
                                            {% elif futher_detail_possible %}
                                            <a href="?fixed_dimension={{ groupby_dimension }}:{{ dimension }}&report_id={{ analytics_template.id }}"
                                                class="clh_href">
                                                {% if dim_col_value %}{{ dim_col_value }}{% else %} NaN {% endif %}
                                            </a>
                                            {% else %}
                                            {% if dim_col_value %}{{ dim_col_value }}{% else %} NaN {% endif %}
                                            {% endif %}
                                        </td>
                                        {% for metr in metrics %}
                                            {% if metr in value %}
                                                {% set metr_obj = value[metr] %}

                                                <td>
                                                    {{ metr_obj.value }}
                                                </td>
                                                <td>
                                                    {{ metr_obj.fraction }}
                                                </td>
                                            {% else %}
                                                <td></td>
                                                <td></td>
                                            {% endif %}
                                        {% endfor %}
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td><strong>Total</strong></td>
                                        {% for metr in metrics %}
                                        <td class="total_value"><strong>{{ total_values[metr] }}</strong></td>
                                        <td></td>
                                        {% endfor %}
                                    </tr>
                                </tfoot>
                            </table>
                            <br>Data by dates for metrics &nbsp;
                            {% set dim_names = [] %}
                            {% for dimension in values %}
                            {% if loop.index <= config.CLICKHOUSE_MAX_DIMENSIONS_ON_DATES_GRAPH %}
                            {% set ap_value = dim_names.append(dimension) %}
                            {% endif %}
                            {% endfor %}
                            {% set dim_names = dim_names|join(',') %}
                            {% set prev_url = fixed_dims|join('&') %}
                            <div class="btn-group dropup">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                    Select metrics <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    {% for metric in metrics %}
                                    <li><a class="dropdown-item"
                                            href="{{ url_for('clickhouseplaybackinfostat.metrics_by_dates', report_id=analytics_template.id, metrics=loop.index - 1, dim_names=dim_names,  start_date=start_date, end_date=end_date ) }}{% if prev_fixed_dimensions %}{{ prev_fixed_dimensions }}{% endif %}"
                                            target="_blank">{{ metric }}</a></li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-5">
            {% for metric in metrics %}
            {{ chart(type=diagram_types[metric], chart_data=values, name='Chart for ' + metric, total_value=total_values[metric],
                    metric=metric, other=other_values[metric], prev_fixed_dimensions=prev_fixed_dimensions, groupby_name=groupby_name,
                    template_id=analytics_template.id) }}
            {% endfor %}
        </div>
    </div>
    {% endif %}
</section>
{% endblock %}
