{% extends admin_base_template %}
{% from 'clickhouse/drilldown_macros.html' import chart_breadcrumb %}

{% block tail_js %}
{{ super() }}
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.3/Chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@0.7.0"></script>
<script src="https://rawgit.com/chartjs/chartjs-plugin-annotation/master/chartjs-plugin-annotation.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        var data = JSON.parse("{{data|safe}}".replace(/'/g, '"'));
        var labels = JSON.parse("{{labels|safe}}".replace(/'/g, '"'));
        var counter = labels.length;
        var total = parseInt("{{total}}");
        var label = "{{label}}";
        var type = "{{type}}";
        ChartCanvas = $('#chart').get(0).getContext('2d');
        config = getConfigForLargeChart(type, data, total, labels,
                                        {{ config.CLICKKHOUSE_DRILLDOWN_MAX_ELS_ON_LARGE_BARPLOT }},
                                        {{ config.CLICKKHOUSE_DRILLDOWN_MAX_LABELS_ON_LARGE_PIEPLOT }} )
        new Chart(ChartCanvas, config);
    });
</script>
{% endblock %}

{% block body %}
<section class="content-header">
    {% block header %}
    <h4> {{ label }} </h4>
    {% endblock %}
    {% block breadcrumb %}
    <ol class="breadcrumb">
        <li><a href="{{ admin_view.admin.index_view.url }}"><i class="fa fa-home"></i>{{ _gettext('Home') }}</a></li>
        <li><a href="{{ admin_view.url }}"><i class="fa fa-list"></i>{{ admin_view.name }} </a></li>
        <li><a href="{{ url_for('clickhouseplaybackinfostat.analytics_view', report_id=analytics_template.id ) }}"> {{ analytics_template.name }} </a></li>
        <li><a id="report_href">Chart {% if type == 'line' %}by dates{% endif %} for {{ label }} </a></li>
    </ol>
    {% endblock %}
</section>

<section class="content" style="overflow: auto;">
    <div class=" box box-primary">
        <div class="box-header">
        <div class="text-center">
            <h4 class="text-center"> {{ analytics_template.name }}: {{ label }}</h4>
            <h4>
            {{ chart_breadcrumb(fixed_dimensions=fixed_dimensions, analytics_template=analytics_template,
                mapped_fixed_val_dims=mapped_fixed_val_dims, groupby_dimension=groupby)  }}
            </h4>
        </div>
        </div>
        <div class="box-body" style="position: relative; min-height:400px;">
            <canvas id="chart"></canvas>
        </div>
    </div>
</section>
{% endblock %}