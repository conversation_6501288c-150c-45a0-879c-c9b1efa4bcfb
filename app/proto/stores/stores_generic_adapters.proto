syntax = "proto3";

option go_package = "gitlab.lfstrm.tv/server-side/storescli";

package tightvideo.protobuf.stores;
import "google/api/annotations.proto";

import "google/protobuf/empty.proto";

message DisableAutorenewRequest {
    string subscription_id = 1;
    string account_id = 2;
    map<string, string> initiator_data = 3;
}

message EnableAutorenewRequest {
    string subscription_id = 1;
    string account_id = 2;
    map<string, string> initiator_data = 3;
}

message RenewV1Request {
    string subscription_id = 1;
    string account_id = 2;
    map<string, string> initiator_data = 3;
}

message RenewV1RResponse{
    bool success = 1;
    int32 seconds_until_retry = 2;
}

message CloneV1Request {
    string subscription_id = 1;
    string account_id = 2;
    map<string, string> initiator_data = 3;
}

service GenericSubscriptionsService {
    rpc DisableAutorenew(DisableAutorenewRequest)returns(google.protobuf.Empty) {
        option (google.api.http) = {
            post: "/generic-subscriptions/disable-auto-renew/v1/{subscription_id}"
            body: "*"
        };

    }

    rpc EnableAutorenew(EnableAutorenewRequest)returns(google.protobuf.Empty) {
        option (google.api.http) = {
            post: "/generic-subscriptions/enable-auto-renew/v1/{subscription_id}"
            body: "*"
        };

    }

    rpc RenewV1(RenewV1Request)returns(RenewV1RResponse) {
        option (google.api.http) = {
            post: "/generic-subscriptions/renew/v1/{subscription_id}"
            body: "*"
        };

    }

    rpc CloneV1(CloneV1Request)returns(google.protobuf.Empty) {
        option (google.api.http) = {
            post: "/generic-subscriptions/clone/v1/{subscription_id}"
            body: "*"
        };

    }
}
