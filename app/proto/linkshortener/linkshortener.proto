syntax = "proto3";

option go_package = "gitlab.lfstrm.tv/server-side/pblinkshortener";

import "google/protobuf/duration.proto";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

message LinkRequest {
  string namespace = 1;
  string url = 2;
  google.protobuf.Duration lifeTime = 3;
}

message LinkResponse {
  string shortUrl = 1;
  string qr = 2;
  int64 expiresAt = 3;
}

message ShortLink {
  string id = 1;
  string namespace = 2;
  string uid = 3;
  string original_url = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp remove_time = 6;
}

enum SortOrder {
  DESC = 0;
  ASC  = 1;
}

message GetActiveLinksRequest {
  int32 page_size = 1;
  string page_token = 2;
  string sort_by = 3;
  SortOrder sort_order = 4;
}

message GetActiveLinksResponse {
  repeated ShortLink active_links = 1;
  string next_page_token = 2;
  int32 total_size = 3;
}

service LinkShortener {
  rpc CreateLink(LinkRequest) returns(LinkResponse) {
    option (google.api.http).post = "/create";
  }
  rpc GetActiveLinks(GetActiveLinksRequest) returns (GetActiveLinksResponse) {
    option (google.api.http) = {
      get: "/links/active"
    };
  }
}

