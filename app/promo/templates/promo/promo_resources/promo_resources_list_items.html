{% extends "cmf/views/paginated_table_list/paginated_table_list_items.html" %}
{# For 'item' see reference at `app.promo.proto.promo.Resource` #}

{% block item_row scoped %}
    <tr>
        <td class="buttons-column">
            {# Delete button. #}
            <span class="btn btn-xs btn-danger js-delete-button"
                  data-item-id="{{ item.id|safe }}"
                  data-url="{{ url_for(".delete_resource") }}"
                  data-confirm="{{ _gettext('Are you sure you want to delete this record?') }}"
                  data-item-selector="tr"
                  title="Delete resource"
            >
                <i class="glyphicon glyphicon-trash"></i>
            </span>

            {# "Show statistics" button. #}
            <span class="btn btn-xs btn-primary js-get-button"
                  data-url="{{ url_for(".get_accounts_resource_stats", resource_id=item.resource_id) }}"
                  title="{{ _("Statistics") }}"
            >
                <i class="fa fa-eye"></i>
            </span>

            {# Go to offers button (link, opens in new tab.) #}
            <a class="btn btn-xs btn-default"
               href="{{ url_for('offer.index_view', flt0_required_promo_resources_equals=item.resource_id) }}"
               title="Used In Offers"
               target="_blank"
            >
                <i class="fa fa-link"></i>
            </a>
        </td>
        <td>{{ item.resource_id }}</td>
        <td>{{ item.resource_type }}</td>
    </tr>
{% endblock %}