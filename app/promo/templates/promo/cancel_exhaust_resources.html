{% extends 'admin/model/cms_edit.html' %}
{% import 'admin/lib.html' as lib with context %}


{% macro info_item(label, value) %}
    {% if value %}
        <div><label>{{ label }}:</label> {{ value }}</div>
    {% endif %}
{% endmacro %}


{% macro status_message(status, icon_class, text_class, message) %}
    <p class="{{ text_class }}">
        <i class="fa {{ icon_class }}"></i>
        {{ message }}
    </p>
{% endmacro %}


{% set i = process_info %}


{% block head_css %}

    {{ super() }}

    <style>

        input[type="submit"] {
            display: none;
        }

    </style>

{% endblock %}


{% block form %}

    {% if i %}
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">{{ _('Promo Resources Exhaustion Status') }}</h3>
                </div>
                <div class="box-body">
                    {{ info_item(_("Status"), {0: none, 1: _("in progress"), 2: _("finished"), 3: _("error")}[i.status]) }}
                    {{ info_item(_("Created"), i.created_at|localize_datetime) }}
                    {{ info_item(_("Finished"), i.finished_at|localize_datetime) }}
                    {{ info_item(_("Error"), i.error) }}
                </div>
            </div>
        </div>
    {% endif %}

    <div class="col-md-12">
        <div class="box box-warning">
            <div class="box-header with-border">
                <h3 class="box-title">{{ _('Cancel Promo Resources Exhaustion') }}</h3>
            </div>
            <div class="box-body">
                {% if i and i.status == 1 %}
                    {{ status_message(1, "fa-warning", "text-warning", _("This will cancel the currently running promo resources exhaustion process.")) }}
                    <p>{{ _('Are you sure you want to proceed?') }}</p>
                {% elif i and i.status == 2 %}
                    {{ status_message(2, "fa-info-circle", "text-info", _("The promo resources exhaustion process has already finished.")) }}
                {% elif i and i.status == 3 %}
                    {{ status_message(3, "fa-exclamation-circle", "text-danger", _("The promo resources exhaustion process finished with an error.")) }}
                {% else %}
                    {{ status_message(0, "fa-info-circle", "text-muted", _("No active promo resources exhaustion process found.")) }}
                {% endif %}
            </div>
        </div>
    </div>

    {{ super() }}
    
{% endblock %}


{% block tail_js %}

    {{ super() }}

    <script>

        {% if i and i.status == 1 %}
            (function($) {
                $('input[type="submit"][name!="_continue_editing"]').attr("value", "{{ _('Cancel Exhaustion') }}").show()
                    .addClass("btn-warning")
                    .click(function(e) {
                        if (!confirm("{{ _('Are you sure you want to cancel the promo resources exhaustion process?') }}")) {
                            e.preventDefault();
                            return false;
                        }

                        $('<input>').attr({
                            type: 'hidden',
                            name: 'cancel_exhaustion',
                            value: 'true'
                        }).appendTo($(this).closest('form'));
                        return true;
                    });
            })(jQuery)
        {% else %}
            (function($) {
                $('form .form-group *').attr('disabled', true);
                $('input[type="submit"][name!="_continue_editing"]').hide()
            })(jQuery)
        {% endif %}

    </script>

{% endblock %}
