from bson.objectid import (
    InvalidId,
    ObjectId,
)
from flask import (
    request,
    url_for,
)
from flask_babelex import gettext as _
from markupsafe import Markup
from mongoengine import Q

from app.live.models import Channel
from app.settings.channel.models import ChannelVideoSettings


def get_media_already_used_error(channel_with_media: Channel, settings_with_media: ChannelVideoSettings) -> Markup:
    """Generate an error message with links to edit views for models using the media."""
    links = []

    if channel_with_media:
        url = url_for("channel.edit_view", id=str(channel_with_media.id))
        links.append(f'<a href="{url}" target="_blank">{channel_with_media.title}</a>')

    if settings_with_media and settings_with_media.channel:
        url = url_for("channel.video_settings_view.edit_view", id=str(settings_with_media.id))
        links.append(f'<a href="{url}" target="_blank">{settings_with_media.channel.title}</a>')

    message = _("Media is already used") + (f" {_('in')} {_(' and ').join(links)}" if links else "")
    return Markup(message)


def mark_invalid_medias(field, ids):
    for subfield in field:
        if subfield.form.media.data and subfield.form.media.data.id in ids:
            subfield.form.media.errors.append("Invalid media")


def validate_media_already_used(field, current_channel_id):
    if isinstance(field.data, list):
        media_channel_ids = [ObjectId(data["media"].id) for data in field.data]
    else:
        media_channel_ids = [field.data.id]

    channel_with_media: Channel | None = Channel.objects(
        Q(medias__in=media_channel_ids) & Q(id__ne=current_channel_id)
    ).first()

    settings_with_media: ChannelVideoSettings | None = ChannelVideoSettings.objects(
        Q(medias__in=media_channel_ids) & Q(channel_id__ne=current_channel_id)
    ).first()

    if channel_with_media or settings_with_media:
        field.errors.append(get_media_already_used_error(channel_with_media, settings_with_media))

        if isinstance(field.data, list):
            media_ids = []
            if channel_with_media:
                media_ids.extend(channel_with_media.medias)
            if settings_with_media:
                media_ids.extend(settings_with_media.medias)
            if media_ids:
                mark_invalid_medias(field, media_ids)

        return False

    return True


def validate_media_fields(form):
    try:
        current_channel_id = ObjectId(request.args.get("channel_id") or request.args.get("id"))
    except InvalidId:
        return False

    if not request.endpoint.endswith((".edit_view", ".create_view")):
        return True

    valid = True

    media_channel = form.media_default.data
    media_to_region = [data["media"].id for data in form.media_to_region.data]
    media_to_timezone = [data["media"].id for data in form.media_to_timezone.data]

    if media_channel:
        valid &= validate_media_already_used(form.media_default, current_channel_id)

        if media_channel.id in media_to_region or media_channel.id in media_to_timezone:
            # Media default should not be used in media to region and media to timezone
            form.media_default.errors.append(
                _("Value from 'Media Default' should not be used in 'Media To Region' or 'Media To Timezone' fields.")
            )

            mark_invalid_medias(form.media_to_region, [media_channel.id])
            mark_invalid_medias(form.media_to_timezone, [media_channel.id])

            valid = False

    if conflicting_medias := set(media_to_region) & set(media_to_timezone):
        error = _("Media can't be used both in media to region and media to timezone:") + ": "

        form.media_to_region.errors.append(error)
        form.media_to_timezone.errors.append(error)

        mark_invalid_medias(form.media_to_region, conflicting_medias)
        mark_invalid_medias(form.media_to_timezone, conflicting_medias)

        valid = False

    valid &= validate_media_already_used(form.media_to_region, current_channel_id)
    valid &= validate_media_already_used(form.media_to_timezone, current_channel_id)

    return valid
