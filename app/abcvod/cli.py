from flask import Flask

from app.abcvod.providers.amedia2.commands import amedia2_commands
from app.abcvod.providers.etnomedia.commands import etnomedia_commands
from app.abcvod.providers.megogo.commands import megogo_commands
from app.abcvod.providers.premier.cardgroup.commands import premier_cardgroup_commands
from app.abcvod.providers.premier.showcase.commands import premier_showcase_commands
from app.abcvod.providers.start2.commands import start2_commands
from app.abcvod.providers.vipplay.commands import vipplay_commands
from app.abcvod.providers.wink.commands import wink_commands


def init_abcvod_cli(app: Flask):
    app.cli.add_command(amedia2_commands)
    app.cli.add_command(etnomedia_commands)
    app.cli.add_command(wink_commands)
    app.cli.add_command(premier_cardgroup_commands)
    app.cli.add_command(premier_showcase_commands)
    app.cli.add_command(start2_commands)
    app.cli.add_command(vipplay_commands)
    app.cli.add_command(megogo_commands)
