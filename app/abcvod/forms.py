from flask_wtf import FlaskForm
from wtforms.fields.simple import Text<PERSON>reaField
from wtforms.validators import DataRequired


class AbcvodAddMultipleTopicsForm(FlaskForm):
    topics = TextAreaField(
        label="New topics",
        description="Enter topic names, each topic name goes to new line",
        validators=[DataRequired()],
    )


class AbcvodAddMultipleCopyrightHoldersForm(FlaskForm):
    copyright_holders = TextAreaField(
        label="New copyright holders",
        description="Enter copyright holders names, each copyright holder's name goes to new line",
        validators=[DataRequired()],
    )
