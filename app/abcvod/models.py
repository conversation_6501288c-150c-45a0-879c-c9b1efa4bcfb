from __future__ import annotations

from typing import Optional

from mongoengine import (
    DoesNotExist,
    MultipleObjectsReturned,
    Q,
    StringField,
)
from slugify import slugify

from app.cms.models import TaskStatus
from app.common.models import CreatedUpdatedMixin
from cmf.core.models import CmsDocument


class AbcvodDocument(CreatedUpdatedMixin, CmsDocument):
    meta = {
        "abstract": True,
        "db_alias": "abcvod",
    }


class AbcvodCountry(AbcvodDocument):
    name = StringField(verbose_name="Country name", required=True)
    full_name = StringField(verbose_name="Official state name")
    eng_name = StringField(verbose_name="Name in English")
    iso2 = StringField(verbose_name="ISO alpha-2 code", min_length=2, max_length=2)
    iso3 = StringField(verbose_name="ISO alpha-3 code", primary_key=True, min_length=3, max_length=3)
    iso_numeric = StringField(verbose_name="ISO numeric code")  # StringField for easy text search
    location = StringField(verbose_name="Location")
    location_precise = StringField(verbose_name="Precise location")

    ajax_search_fields = (
        "name",
        "full_name",
        "eng_name",
        "iso2",
        "iso3",
        "iso_numeric",
        "location",
        "location_precise",
    )

    def __str__(self):
        return self.name


class AbcvodPerson(AbcvodDocument):
    name = StringField(verbose_name="Name", required=True)
    slug = StringField(
        verbose_name="Slug",
        required=True,
        help_text="Helps to minimize minor spelling errors and variations in names. Created and updated automatically.",
    )
    # amedia_id and wink_id are actually IntField, but you can't text search by IntField.
    amedia_id = StringField(verbose_name="Person ID in Amediateka")
    start_id = StringField(verbose_name="Person ID in Start")
    wink_id = StringField(verbose_name="Person ID in Wink")
    premier_id = StringField(verbose_name="Person ID in Premier")
    megogo_id = StringField(verbose_name="Person ID in Megogo")

    id_fields = (
        "amedia_id",
        "start_id",
        "wink_id",
        "premier_id",
        "megogo_id",
    )

    ajax_search_fields = (
        "name",
        "slug",
        *id_fields,
    )

    def __str__(self):
        return self.name

    @classmethod
    def __fix_and_get_person(cls, q: Q) -> AbcvodPerson:
        """In case we have multiple persons with similar ID - Gather all remote ids for person in one."""
        clones: list[AbcvodPerson] = list(cls.objects(q))
        master: AbcvodPerson = clones[0]
        for clone in clones[1:]:
            for id_name in cls.id_fields:
                value = getattr(clone, id_name)
                if value:
                    setattr(master, id_name, value)
                    setattr(clone, id_name, None)
        for clone in clones:
            clone.save()
        return master

    @classmethod
    def __get_person_by_id(
        cls,
        *,
        amedia_id: str | None = None,
        start_id: str | None = None,
        wink_id: str | None = None,
        premier_id: str | None = None,
        megogo_id: str | None = None,
    ) -> AbcvodPerson | None:
        if not amedia_id and not start_id and not wink_id:
            return None
        q = Q()
        if amedia_id:
            q &= Q(amedia_id=amedia_id)
        if start_id:
            q &= Q(start_id=start_id)
        if wink_id:
            q &= Q(wink_id=wink_id)
        if premier_id:
            q &= Q(premier_id=premier_id)
        if megogo_id:
            q &= Q(megogo_id=megogo_id)

        try:
            return cls.objects.get(q)
        except DoesNotExist:
            return None
        except MultipleObjectsReturned:
            return cls.__fix_and_get_person(q)

    @classmethod
    def get_or_create_person(
        cls,
        *,
        name: str,
        amedia_id: Optional[str] = None,
        start_id: Optional[str] = None,
        wink_id: Optional[str] = None,
        premier_id: Optional[str] = None,
        megogo_id: Optional[str] = None,
    ) -> AbcvodPerson:
        person_by_id = cls.__get_person_by_id(
            amedia_id=amedia_id,
            start_id=start_id,
            wink_id=wink_id,
            premier_id=premier_id,
            megogo_id=megogo_id,
        )
        if person_by_id:
            return person_by_id

        slug = slugify(name)
        try:
            person: AbcvodPerson = cls.objects.get(slug=slug)
            # Update person in case if id is missing or different.
            if amedia_id and person.amedia_id != amedia_id:
                person.amedia_id = amedia_id
                person.save()
            if start_id and person.start_id != start_id:
                person.start_id = start_id
                person.save()
            if wink_id and person.wink_id != wink_id:
                person.wink_id = wink_id
                person.save()
            if premier_id and person.premier_id != premier_id:
                person.premier_id = premier_id
                person.save()
            if megogo_id and person.megogo_id != megogo_id:
                person.megogo_id = megogo_id
                person.save()
            return person
        except DoesNotExist:
            return cls(
                name=name,
                amedia_id=amedia_id,
                start_id=start_id,
                wink_id=wink_id,
                premier_id=premier_id,
                megogo_id=megogo_id,
            ).save()

    def clean(self):
        self.slug = slugify(self.name)
        super(AbcvodPerson, self).clean()


class AbcvodImportInfo(AbcvodDocument):
    db_name = StringField(verbose_name="VOD database name", primary_key=True)
    status = StringField(choices=TaskStatus.choices)


class AbcvodTopic(AbcvodDocument):
    meta = {
        "indexes": [
            {"fields": ["slug"], "unique": True},
        ],
    }

    ajax_search_fields = (
        "name",
        "slug",
    )

    name = StringField(verbose_name="Name of Topic", max_length=100)
    slug = StringField(verbose_name="Slug", help_text="This field is generated automatically.", max_length=300)

    def clean(self):
        self.slug = slugify(self.name)
        super().clean()

    def __str__(self):
        return self.name


class AbcvodCopyrightHolder(AbcvodDocument):
    meta = {
        "indexes": [
            {"fields": ["slug"], "unique": True},
        ],
    }

    ajax_search_fields = (
        "name",
        "slug",
    )

    name = StringField(verbose_name="Name of Copyright Holder", max_length=100)
    slug = StringField(verbose_name="Slug", help_text="This field is generated automatically.", max_length=300)

    def clean(self):
        self.slug = slugify(self.name)
        super().clean()

    def __str__(self):
        return self.name
