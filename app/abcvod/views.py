from __future__ import annotations

from functools import lru_cache
from typing import (
    Iterable,
    Type,
)

from flask import (
    flash,
    redirect,
    render_template,
    url_for,
)
from mongoengine import (
    Document,
    NotUniqueError,
    QuerySet,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCountry,
    AbcvodPerson,
    AbcvodTitle,
)
from app.abcvod.forms import (
    AbcvodAddMultipleCopyrightHoldersForm,
    AbcvodAddMultipleTopicsForm,
)
from app.abcvod.models import (
    AbcvodCopyrightHolder,
    AbcvodTopic,
)
from app.abcvod.providers.basevod.models import BaseVODTitle
from app.abcvod.utils import (
    get_available_abcvod_providers,
    get_vod_provider_name,
)
from app.utils import all_subclasses
from cmf.core.mixins import InvisibleMixin
from cmf.core.view import CmsBaseModelView
from cmf.core.views.base_form_view import BaseFormView
from cmf.core.views.custom_select2_search_view import (
    CustomSelect2ResultItem,
    CustomSelect2SearchView,
)


class AbcvodPersonView(CmsBaseModelView):
    model = AbcvodPerson
    can_view_details = True
    can_edit = False
    can_delete = False

    column_list = (
        "name",
        "slug",
        "amedia_id",
        "start_id",
        "wink_id",
    )

    column_searchable_list = column_list
    form_columns = column_list + (
        "created_at",
        "updated_at",
    )


class AbcvodCountryView(CmsBaseModelView):
    model = AbcvodCountry
    can_view_details = True
    can_edit = False
    can_delete = False

    column_list = (
        "name",
        "full_name",
        "eng_name",
        "iso2",
        "iso3",
        "iso_numeric",
        "location",
        "location_precise",
    )

    column_searchable_list = column_list
    form_columns = column_list + (
        "created_at",
        "updated_at",
    )


class AbcvodSearchTitleView(CustomSelect2SearchView):
    item_template_name = "abcvod/abcvod_select2_title_item.html"
    selection_template_name = "abcvod/abcvod_select2_title_selection.html"
    search_by = ["caption", "remote_id"]
    model = BaseVODTitle  # This is important for '_get_model_fields' method.
    queryset_filter = {"is_published": True}

    @lru_cache
    def get_models(self) -> list[Type[AbcvodTitle]]:
        result = []
        available_providers = get_available_abcvod_providers()
        known_title_models = all_subclasses(AbcvodTitle)

        for model in known_title_models:
            db_alias = model._meta.get("db_alias")
            if db_alias in available_providers:
                result.append(model)
        return result

    def search(self, *, term: str, offset: int = 0, limit: int) -> QuerySet | Iterable[Document]:
        result = []

        for model in self.get_models():
            self.model = model  # dirty :(
            search_result = super(AbcvodSearchTitleView, self).search(term=term, offset=0, limit=limit - len(result))
            result.extend(search_result)
            if len(result) == limit:
                return result
        return result

    def render_item(self, doc: AbcvodTitle) -> str:
        vod_provider_name = get_vod_provider_name(doc)
        poster = doc.poster
        if poster and not poster.startswith("http"):
            poster = url_for("uploads", path=doc.poster)
        return render_template(self.item_template_name, item=doc, vod_verbose_name=vod_provider_name, poster=poster)

    def render_selection(self, doc: AbcvodTitle) -> str:
        vod_provider_name = get_vod_provider_name(doc)
        vod_name = self._get_vod_name(doc)
        title_url = url_for(f"{vod_name}_titles.edit_view", id=doc.id)
        return render_template(
            self.selection_template_name, item=doc, vod_provider_name=vod_provider_name, title_url=title_url
        )

    def make_result(self, doc: AbcvodTitle) -> CustomSelect2ResultItem:
        result = super(AbcvodSearchTitleView, self).make_result(doc)
        result.custom_attrs["data-db-alias"] = doc.db_alias
        return result

    def _get_vod_name(self, doc: AbcvodTitle) -> str:
        db_alias = doc.db_alias
        if db_alias == "amediateka2":
            return "amedia2"
        if db_alias == "premier":
            return "premier_cardgroup"
        return db_alias


class AbcvodTopicView(CmsBaseModelView):
    model = AbcvodTopic
    list_template = "abcvod/abcvod_topics_view_list.html"

    column_list = (
        "name",
        "slug",
        "created_at",
        "updated_at",
    )

    form_create_rules = ("name",)

    form_edit_rules = (
        "name",
        "slug",
    )

    form_widget_args = {
        "slug": {"disabled": True},
    }


class AbcvodAddMultipleTopicsView(InvisibleMixin, BaseFormView):
    form_class = AbcvodAddMultipleTopicsForm
    form: AbcvodAddMultipleTopicsForm

    def form_valid(self):
        topic_names: list[str] = self.form.topics.data.strip().split("\n")
        created_topics = []
        skipped_topics = []
        for topic_name in topic_names:
            topic_name = topic_name.lower().capitalize()

            try:
                AbcvodTopic(name=topic_name).save()
                created_topics.append(topic_name)
            except NotUniqueError:
                skipped_topics.append(topic_name)
        flash(
            f"Success! {len(created_topics)} new topics were created. "
            f"{len(skipped_topics)} were skipped (already exists): {', '.join(skipped_topics)}",
            category="success",
        )
        return redirect(url_for("abcvod_topics.index_view"))


class AbcvodCopyrightHolderView(CmsBaseModelView):
    model = AbcvodCopyrightHolder
    list_template = "abcvod/abcvod_copyright_holders_view_list.html"

    column_list = (
        "name",
        "slug",
        "created_at",
        "updated_at",
    )

    form_create_rules = ("name",)

    form_edit_rules = (
        "name",
        "slug",
    )

    form_widget_args = {
        "slug": {"disabled": True},
    }


class AbcvodAddMultipleCopyrightHoldersView(InvisibleMixin, BaseFormView):
    form_class = AbcvodAddMultipleCopyrightHoldersForm
    form: AbcvodAddMultipleCopyrightHoldersForm
    show_success_message = False

    def get_redirect_url(self) -> str:
        return url_for("abcvod_copyright_holders.index_view")

    def form_valid(self):
        holder_names: list[str] = self.form.copyright_holders.data.strip().split("\n")
        created_holders = []
        skipped_holders = []
        for holder_name in holder_names:
            try:
                AbcvodCopyrightHolder(name=holder_name).save()
                created_holders.append(holder_name)
            except NotUniqueError:
                skipped_holders.append(holder_name)
        flash(
            f"Success! {len(created_holders)} new copyright holders were created. "
            f"{len(skipped_holders)} were skipped (already exists): {', '.join(skipped_holders)}",
            category="success",
        )
        return super().form_valid()
