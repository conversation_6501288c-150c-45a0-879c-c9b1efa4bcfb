from flask import Flask
from markupsafe import Markup

from cmf.core.admin import C<PERSON><PERSON>d<PERSON>


def init_abcvod_admin(app: Flask, admin: CMFAdmin, category=None):
    from app.abcvod.views import (
        AbcvodAddMultipleCopyrightHoldersView,
        AbcvodAddMultipleTopicsView,
        AbcvodCopyrightHolderView,
        AbcvodCountryView,
        AbcvodPersonView,
        AbcvodTopicView,
    )

    if not category:
        category = Markup('<i class="fa fa-file-movie-o"></i> ABCVOD')
        admin.add_category(category)

    from app.abcvod.providers.basevod.views import init_basevod_admin

    init_basevod_admin(admin, parent_name=category)

    if app.config["PREMIER"]:
        from app.abcvod.providers.premier.cardgroup.views import init_premier_cardgroup_admin

        init_premier_cardgroup_admin(admin, parent_name=category)

    if app.config["PREMIER_SHOWCASE"]:
        from app.abcvod.providers.premier.showcase.views import init_premier_showcase_admin

        init_premier_showcase_admin(admin, parent_name=category)

    if app.config["AMEDIA2"]:
        from app.abcvod.providers.amedia2.views import init_amedia2_admin

        init_amedia2_admin(admin, parent_name=category)

    if app.config["VIPPLAY"]:
        from app.abcvod.providers.vipplay.views import init_vipplay_admin

        init_vipplay_admin(admin, "VipPlay", parent_name=category)

    if app.config["WINK"]:
        from app.abcvod.providers.wink.views import init_wink_admin

        init_wink_admin(admin, "Wink", parent_name=category)

    if app.config["START2"]:
        from app.abcvod.providers.start2.views import init_start2_admin

        init_start2_admin(admin, "Start2", parent_name=category)

    if app.config["ETNOMEDIA"]:
        from app.abcvod.providers.etnomedia.views import init_etnomedia_admin

        init_etnomedia_admin(admin, "Etnomedia", parent_name=category)

    if app.config["MEGOGO"]:
        from app.abcvod.providers.megogo.views import init_megogo_admin

        init_megogo_admin(admin, "Megogo", parent_name=category)

    admin.make_category(
        category_name="Common VOD info",
        parent_category_name=category,
        views=[
            AbcvodPersonView(name="Persons", endpoint="persons"),
            AbcvodCountryView(name="Countries", endpoint="countries"),
            AbcvodTopicView(name="Topics", endpoint="abcvod_topics"),
            AbcvodCopyrightHolderView(name="Copyright holders", endpoint="abcvod_copyright_holders"),
        ],
    )

    # Invisible views.
    admin.add_views(
        AbcvodAddMultipleTopicsView(name="Add multiple topics", endpoint="abcvod_add_multiple_topics"),
        AbcvodAddMultipleCopyrightHoldersView(
            name="Add multiple copyright holders", endpoint="abcvod_add_multiple_copyright_holders"
        ),
    )

    from app.abcvod.views import AbcvodSearchTitleView

    app.add_url_rule("/abcvod/search_title", view_func=AbcvodSearchTitleView.as_view(name="abcvod_search_title"))
