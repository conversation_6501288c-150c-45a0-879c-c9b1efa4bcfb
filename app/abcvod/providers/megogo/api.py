from __future__ import annotations

from enum import Enum
from typing import (
    Annotated,
    Optional,
)

from pydantic import (
    Before<PERSON><PERSON><PERSON>tor,
    ConfigDict,
)
from pydantic_xml import (
    BaseXmlModel,
    attr,
    element,
    wrapped,
)
from pydantic_xml.element import SearchMode


def parse_float_or_none(value) -> Optional[float]:
    if not value:
        return None
    return float(value)


# Special field types for this API.
optional_float = Annotated[Optional[float], BeforeValidator(parse_float_or_none)]

# Simple version of `NotImportant` from app.abcvod.core.api <- read more about the idea behind this "Type".
# Cannot use original `NotImportant` here, because `BaseXmlModel` cannot use type `Any`.
XmlNotImportant = None


class BaseMegogoModel(BaseXmlModel, search_mode=SearchMode.UNORDERED):
    """Base model for any Megogo API entities.

    `search_mode=SearchMode.UNORDERED` parameter allows any extra data to be passed into model without
    throwing an exception, which is good.
    """

    model_config = ConfigDict(coerce_numbers_to_str=True)


class Info(BaseMegogoModel, tag="info"):
    year: int | XmlNotImportant = attr(default=None)  # 2008
    country: str | XmlNotImportant = attr(default=None)
    budget: str | XmlNotImportant = attr(default=None)
    premiere: str = attr()  # 14 декабря 2008
    dvd: str | XmlNotImportant = attr(default=None)  # 3 декабря 2009
    duration: str | XmlNotImportant = attr(default=None)


class Category(BaseMegogoModel, tag="category"):
    id: str = attr()
    name: str


class PersonInfo(BaseMegogoModel):
    id: str = attr()
    name: str


class Ratings(BaseMegogoModel, tag="ratings"):
    kinopoisk: optional_float = attr(default=None)
    kinopoisk_id: Optional[str] = attr(default="")
    imdb: optional_float = attr(default=None)
    imdb_id: Optional[str] = attr(default="")
    megogo: optional_float = attr(default=None)


class Dues(BaseMegogoModel, tag="dues"):
    usa: str = attr()
    russian: str = attr()


class Poster(BaseMegogoModel, tag="poster"):
    url: str = attr()
    thumbnail: str = attr()
    big: str = attr()


class Distribution(BaseMegogoModel, tag="distribution"):
    vod_type: str = attr()


class AudioLang(BaseMegogoModel, tag="audio_lang"):
    index: int = attr()
    name: str


class SubtitleLang(BaseMegogoModel, tag="subtitle_lang"):
    name: str


class EpisodeInfo(BaseMegogoModel, tag="seria"):
    id: str = attr()
    index: int = attr()
    title: str = attr()
    poster: str = attr()
    duration_sec: int = attr()


class SeasonInfo(BaseMegogoModel, tag="season"):
    id: str = attr()
    title: str = attr()
    episodes: list[EpisodeInfo] = element("seria", default_factory=list)  # Warning! Episodes may be missing sometimes.


class CanImport(str, Enum):
    YES = "yes"
    BAD_CONTRACT = "Bad contract"
    BAD_DISTRIBUTION = "Bad distribution"


class TitleInfo(BaseMegogoModel, tag="object"):
    id: str = attr()
    title: str = attr()
    title_en: str = attr()
    categories_attr: str = attr(name="categories")
    genres_attr: str = attr(name="genres")  # do not use this attribute in import, use 'genres' property
    serial: bool = attr()
    page: str = attr()
    type: str = attr()
    kinopoisk_id: optional_float = attr()
    vod: str = attr()

    info: Info
    categories: list[Category] = wrapped("categories", default_factory=list)
    actors: list[PersonInfo] = wrapped("actors", element(tag="actor", default_factory=list))
    directors: list[PersonInfo] = wrapped("directors", element(tag="director", default_factory=list))
    ratings: Ratings
    story: str = element()
    poster: Poster = element()
    # closing_date: Optional[datetime] = element()
    contract_id: int = element()
    age_limit: int = element()
    protection: str = element()
    distributions: list[Distribution] = wrapped("distributions")
    seasons: Optional[list[SeasonInfo]] = element("season", default_factory=list)

    @property
    def genres(self):
        if self.genres_attr:
            return self.genres_attr.split(",")
        return []

    def is_distribution_ok(self, allowed_distributions: set[str]):
        for distribution in self.distributions:
            if distribution.vod_type in allowed_distributions:
                return True
        return False

    def is_contract_ok(self, allowed_contracts: set[int]) -> bool:
        return self.contract_id in allowed_contracts

    def can_import(self, allowed_contracts: set[int], allowed_distributions: set[str]) -> CanImport:
        """Check if the title can be imported based on import rights.

        Performs two main validation checks:

        1. Distribution type must be supported by the platform.
        2. If the title is "major" content (protection != "0"), it must have an
           explicitly allowed contract_id.

        See https://dev.tightvideo.com/issues/120713 for more details.
        """
        if allowed_distributions and not self.is_distribution_ok(allowed_distributions):
            return CanImport.BAD_DISTRIBUTION

        # Check for "major" content.
        # If protection is "0", then it is not a major content.
        # So we can import it without any restrictions.
        if self.protection == "0":
            return CanImport.YES

        if allowed_contracts and not self.is_contract_ok(allowed_contracts):
            return CanImport.BAD_CONTRACT
        return CanImport.YES
