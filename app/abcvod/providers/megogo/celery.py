from flask import current_app

from app.abcvod.providers.megogo.importer import MegogoImporter
from app.cms.models import CeleryTaskTracker
from app.cms.tasks.utils import cms_task


@cms_task(task_processor_class=MegogoImporter)
def megogo_import_task(task_trigger: str, task_tracker: CeleryTaskTracker = None):
    importer = MegogoImporter(app=current_app, task_tracker=task_tracker, task_trigger=task_trigger)
    importer.run()
