from __future__ import annotations

import logging
import os
import tempfile
from collections import defaultdict
from datetime import date
from functools import lru_cache
from pathlib import Path
from typing import Generator

import lxml.etree as ET
import pydantic
from flask import (
    Flask,
    current_app,
)
from mongoengine import DoesNotExist
from slugify import slugify

from app.abcvod.core.importer import (
    DoNotAutoupdateMixin,
    ImporterWithImages,
)
from app.abcvod.models import AbcvodPerson
from app.utils import Timer

from .api import (
    CanImport,
    EpisodeInfo,
    PersonInfo,
    SeasonInfo,
    TitleInfo,
)
from .models import (
    MegogoCategory,
    MegogoCollection,
    MegogoEpisode,
    MegogoGenre,
    MegogoImportTracker,
    MegogoSeason,
    MegogoTitle,
)

logger = logging.getLogger(__name__)


_month_map = {
    "января": 1,
    "февраля": 2,
    "марта": 3,
    "апреля": 4,
    "мая": 5,
    "июня": 6,
    "июля": 7,
    "августа": 8,
    "сентября": 9,
    "октября": 10,
    "ноября": 11,
    "декабря": 12,
}


def _parse_megogo_date_string(megogo_date: str) -> date | None:
    """Parse date string like '10 февраля 2007' into actual date."""
    if not megogo_date:
        return None
    try:
        day, month, year = megogo_date.split(" ")
    except ValueError as e:
        print(e)
        raise
    month = _month_map[month.lower()]
    return date(int(year), month, int(day))


class MegogoImporter(DoNotAutoupdateMixin, ImporterWithImages):
    """Class for importing Megogo content."""

    task_tracker_model = MegogoImportTracker

    title_model = MegogoTitle
    collection_model = MegogoCollection
    temp_xml_file_path: Path = ""

    def __init__(self, app: Flask, task_tracker: MegogoImportTracker | None = None, task_trigger: str | None = None):
        super().__init__(
            task_trigger=task_trigger,
            task_tracker=task_tracker,
            max_workers=app.config["MEGOGO_MAX_WORKERS"],
            storage=app.extensions["storage"],
        )
        self.allowed_contracts = set(int(contract) for contract in app.config["MEGOGO_ALLOWED_CONTRACTS"] or [])
        self.allowed_distributions = set(dist for dist in app.config["MEGOGO_ALLOWED_DISTRIBUTIONS"] or [])

        self.add_log(f"Allowed contracts: {self.allowed_contracts or 'ALL'}")
        self.add_log(f"Allowed distributions: {self.allowed_distributions or 'ALL'}")

        # Setup persons cache
        persons = AbcvodPerson.objects.filter(megogo_id__exists=True)
        self.persons_cache: dict[str, AbcvodPerson] = {person.megogo_id: person for person in persons}

        self.denied: dict[CanImport, int] = defaultdict(lambda: 0)

    def __exit__(self, exc_type, exc_val, exc_tb):
        self._remove_temp_file()
        super().__exit__(exc_type, exc_val, exc_tb)

    def import_procedure(self):
        self.download_xml_file()

        self.setup_categories()
        self.import_titles()

    def import_titles(self):
        for title_info in self.iter_xml():
            self.import_title(title_info)

    def import_title(self, title_info: TitleInfo):
        """Import title and it's seasons and episodes."""
        can_import = title_info.can_import(self.allowed_contracts, self.allowed_distributions)
        if can_import != CanImport.YES:
            self.denied[can_import] += 1
            return

        title = self.upsert_title(title_info)
        if title_info.serial:
            for season_number, season_info in enumerate(title_info.seasons):
                self.import_season(season_info, season_number, title)

    def setup_categories(self):
        """Create default categories."""
        self.movies_category = self.get_or_create_category(caption="Фильмы", slug="movies", priority=1)
        self.series_category = self.get_or_create_category(caption="Сериалы", slug="series", priority=2)

    def get_or_create_category(self, caption: str, slug: str, priority: int) -> MegogoCategory:
        # FIXME: This is copy-paste from Wink importer.
        try:
            category = MegogoCategory.objects.get(slug=slug)
        except DoesNotExist:
            category = MegogoCategory(
                slug=slug,
                created_during_import=self.uid,
            )

        category.is_published = True
        category.updated_during_import = self.uid
        if not self.can_update(category):
            return category.save()

        category.caption = caption
        category.priority = priority
        return category.save()

    def download_xml_file(self) -> Path:
        with Timer() as timer:
            result = self._download_xml_file()
        self.results["XML download time"] = timer.time
        self.results["XML size"] = result.stat().st_size
        self.task_tracker.save()
        return result

    def _download_xml_file(self) -> Path:
        """Get raw XML from source and save it to a temporary file in a streaming manner."""
        xml_url = current_app.config["MEGOGO_XML_URL"]
        self.add_log(f"Start downloading XML file from {xml_url}")

        with tempfile.NamedTemporaryFile(mode="wb", delete=False, suffix=".xml") as temp_file:
            with self.http_session.get(xml_url, stream=True) as response:
                response.raise_for_status()

                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        temp_file.write(chunk)
            self.temp_xml_file_path = Path(temp_file.name)

        self.add_log(f"Download complete, size: {self.temp_xml_file_path.stat().st_size}")
        return self.temp_xml_file_path

    def iter_xml(self) -> Generator[TitleInfo, None, None]:
        context = ET.iterparse(self.temp_xml_file_path, events=("start", "end"))
        _, root = next(context)

        for event, element in context:
            if event == "end" and element.tag == "object":
                xml = ET.tostring(element, encoding="unicode")
                try:
                    title_info = TitleInfo.from_xml(xml)
                except pydantic.ValidationError as e:
                    pretty_xml = ET.tostring(element, encoding="unicode", pretty_print=True)
                    self.add_import_error("Parsing error", error_message=pretty_xml, exception=e)
                    raise

                yield title_info
                root.clear()

    def after_import(self):
        super(MegogoImporter, self).after_import()
        self.mark_unpublished(
            {
                "Genres": MegogoGenre,
                "Titles": MegogoTitle,
                "Seasons": MegogoSeason,
                "Episodes": MegogoEpisode,
            },
        )
        # Report denied to import titles.
        if self.denied:
            self.results["Total amount of denied titles"] = sum(self.denied.values())
            for can_import, amount in self.denied.items():
                can_import: CanImport
                deny_reason = f"Denied by {can_import.value}"
                self.results[deny_reason] = amount

    def _remove_temp_file(self):
        if not (self.temp_xml_file_path and os.path.exists(self.temp_xml_file_path)):
            return
        try:
            os.remove(self.temp_xml_file_path)
        except OSError:
            self.add_log(f"Failed to remove temp file: {self.temp_xml_file_path}")

    def upsert_title(self, title_info: TitleInfo) -> MegogoTitle:
        remote_id = title_info.id

        try:
            title = MegogoTitle.objects.get(remote_id=remote_id)
        except DoesNotExist:
            title = MegogoTitle(remote_id=remote_id, created_during_import=self.uid)

        title.is_published = True
        title.updated_during_import = self.uid
        if title.do_not_autoupdate:
            return title.save()

        title.slug = "{}-{}".format(slugify(title_info.title), remote_id)
        title.caption = title_info.title
        title.description = title_info.story
        title.release_date = _parse_megogo_date_string(title_info.info.premiere)
        title.years = [title.release_date.year] if title.release_date else []
        title.is_series = title_info.serial

        # Posters
        if poster_url := title_info.poster.url:
            poster_file_name = f"{title.remote_id}/title_poster.jpg"
            title.poster = self.delay_download_image(
                storage_dir="megogo",
                file_name=poster_file_name,
                image_url=poster_url,
            )
        else:
            title.poster = None

        title.genres = self.get_genres(title_info.genres)
        title.categories = [self.series_category] if title.is_series else [self.movies_category]

        # Set various ratings.
        title.imdb_id = title_info.ratings.imdb_id or None
        title.imdb_rating = title_info.ratings.imdb or None
        title.kp_id = title_info.ratings.kinopoisk_id or None
        title.kp_rating = title_info.ratings.kinopoisk or None
        title.age_rating = title_info.age_limit or None

        # Set persons
        title.actors = [self.get_or_create_person(person_info) for person_info in title_info.actors]
        title.directors = [self.get_or_create_person(person_info) for person_info in title_info.directors]

        # Set DRM
        title.drm_required = title_info.protection and title_info.protection != "0"

        return title.save()

    def import_season(self, season_info: SeasonInfo, season_number: int, title: MegogoTitle):
        season: MegogoSeason = self.upsert_season(season_info, season_number, title)
        for episode_info in season_info.episodes:
            self.upsert_episode(episode_info, season)

    def upsert_season(self, season_info: SeasonInfo, season_number: int, title: MegogoTitle) -> MegogoSeason:
        remote_id = season_info.id
        slug = f"{title.slug}-s{season_number}"

        try:
            season = MegogoSeason.objects.get(remote_id=remote_id)
        except DoesNotExist:
            season = MegogoSeason(remote_id=remote_id, created_during_import=self.uid)

        season.is_published = True
        season.updated_during_import = self.uid
        if season.do_not_autoupdate:
            return season.save()

        season.title = title
        season.slug = slug
        season.number = season_number
        season.caption = season_info.title
        return season.save()

    def upsert_episode(self, episode_info: EpisodeInfo, season: MegogoSeason):
        remote_id = episode_info.id
        slug = season.slug + f"-e{episode_info.index}"

        try:
            episode = MegogoEpisode.objects.get(remote_id=remote_id)
        except DoesNotExist:
            episode = MegogoEpisode(remote_id=remote_id, created_during_import=self.uid)

        episode.is_published = True
        episode.season = season
        episode.number = episode_info.index

        # Posters
        if poster_url := episode_info.poster:
            poster_file_name = f"{season.title.remote_id}/episode_{episode.number}_poster.jpg"
            episode.poster = self.delay_download_image(
                storage_dir="megogo",
                file_name=poster_file_name,
                image_url=poster_url,
            )
        else:
            episode.poster = None

        episode.updated_during_import = self.uid
        if episode.do_not_autoupdate:
            return episode.save()

        episode.caption = episode_info.title
        episode.slug = slug
        return episode.save()

    def get_genres(self, genres: list[str]) -> list[MegogoGenre]:
        return [self.upsert_genre(genre_name) for genre_name in genres]

    @lru_cache
    def upsert_genre(self, genre_name: str) -> MegogoGenre:
        slug = slugify(genre_name)

        try:
            genre = MegogoGenre.objects.get(slug=slug)
        except DoesNotExist:
            genre = MegogoGenre(slug=slug, created_during_import=self.uid)

        genre.is_published = True
        genre.updated_during_import = self.uid
        if genre.do_not_autoupdate:
            return genre.save()

        genre.caption = genre_name
        return genre.save()

    def get_or_create_person(self, person_info: PersonInfo):
        """Get existing or create new person, using person info from API."""
        # FIXME: This is 100% copy-paste from WINK and it just works.
        person_id = person_info.id
        if person_id not in self.persons_cache:
            with self.lock:
                person = AbcvodPerson.get_or_create_person(name=person_info.name, wink_id=person_id)
                self.persons_cache[person_id] = person
        return self.persons_cache[person_id]
