from flask_mongoengine import Document
from mongoengine import (
    CASCADE,
    PULL,
)
from mongoengine.fields import (
    ListField,
    ReferenceField,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodEpisode,
    AbcvodGenre,
    AbcvodSeason,
    AbcvodTitle,
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.abcvod.core.models.task import AbcvodImportTracker
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)


class MegogoDocument(Document):
    meta = {
        "db_alias": "megogo",
        "abstract": True,
    }


class MegogoGenre(MegogoDocument, AbcvodGenre):
    pass


class MegogoCategory(MegogoDocument, AbcvodCategory):
    genres = ListField(ReferenceField(MegogoGenre, reverse_delete_rule=PULL))


class MegogoTitle(MegogoDocument, AbcvodTitle):
    meta = {
        "indexes": ["categories", "genres"],
    }

    categories = ListField(ReferenceField(MegogoCategory, reverse_delete_rule=PULL))
    genres = ListField(ReferenceField(MegogoGenre, reverse_delete_rule=PULL))
    collections = ListField(ReferenceField("MegogoCollection"))


class MegogoSeason(MegogoDocument, AbcvodSeason):
    meta = {
        "indexes": ["title"],
    }

    title: MegogoTitle = ReferenceField(MegogoTitle, reverse_delete_rule=CASCADE)


class MegogoEpisode(MegogoDocument, AbcvodEpisode):
    meta = {
        "indexes": [
            "title",
            "season",
            "default_caption",
        ],
    }

    title = ReferenceField(MegogoTitle, reverse_delete_rule=CASCADE)
    season = ReferenceField(MegogoSeason, reverse_delete_rule=CASCADE)


class MegogoImportTracker(AbcvodImportTracker):
    job_title = "Megogo VOD content import"
    job_id = "megogo-import"


class MegogoTitleReference(AbcvodTitleReference):
    title = ReferenceField(MegogoTitle)


class MegogoCollection(MegogoDocument, AbcvodCollection):
    titles = TitlesField(MegogoTitleReference)
    titles_excluded = TitlesExcludedField(MegogoTitleReference)
    categories = ReferenceCriteriaField(MegogoTitle.categories)
    genres = ReferenceCriteriaField(MegogoTitle.genres)
    imdb_rating = RatingCriteriaField(MegogoTitle.imdb_rating)
    kp_rating = RatingCriteriaField(MegogoTitle.kp_rating)


MegogoCollection.register_delete_rule(MegogoTitle, "collections", PULL)
