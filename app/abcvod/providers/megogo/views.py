from flask_admin import Admin

from app.abcvod.core.views import (
    AbcVodCategoryView,
    AbcVodCollectionView,
    AbcVodEpisodeView,
    AbcVodGenreView,
    AbcvodImportTaskView,
    AbcVodSeasonView,
    AbcVodTitleView,
    AbcVodViewMixin,
    GetJsonFromApiMixin,
    init_vod_admin,
)
from app.abcvod.providers.megogo.celery import megogo_import_task
from app.abcvod.providers.megogo.models import (
    MegogoCategory,
    MegogoCollection,
    MegogoEpisode,
    MegogoGenre,
    MegogoImportTracker,
    MegogoSeason,
    MegogoTitle,
)
from app.cms.tasks.utils import ReimportException


class MegogoMixin(AbcVodViewMixin):
    vod_name = "megogo"
    genre_class = MegogoGenre
    category_class = MegogoCategory
    title_class = MegogoTitle
    season_class = MegogoSeason
    episode_class = MegogoEpisode
    collection_class = MegogoCollection


class MegogoTitleView(MegogoMixin, GetJsonFromApiMixin, AbcVodTitleView):
    model = MegogoTitle

    def get_api_response(self, document):
        title: MegogoTitle = document
        title_json = self.api.get_title_info_json(title.remote_id)
        return title_json or f"This title with remote_id {title.remote_id} was not found in api :("

    def reimport_procedure(self, document):
        title: MegogoTitle = document
        title_info = self.api.get_title_info(remote_id=title.remote_id)
        if not title_info.license_is_valid():
            raise ReimportException("License restrictions. 'paidType' field value is not 'AVOD' or 'SVOD'.")
        importer = self.get_importer()
        with importer:
            importer.results["Reimport title with id"] = title.remote_id
            importer._setup_genres_cache()
            importer.setup_categories()
            with importer.ignore_do_not_autoupdate:
                importer.import_title(remote_id=title.remote_id)
            importer.import_seasons()
            importer.import_episodes()
            importer.download_all_images()


class MegogoCategoryView(MegogoMixin, AbcVodCategoryView):
    model = MegogoCategory


class MegogoGenreView(MegogoMixin, GetJsonFromApiMixin, AbcVodGenreView):
    model = MegogoGenre

    def get_api_response(self, document):
        genre: MegogoGenre = document
        genre_json = self.api.get_genre_info_json(genre.remote_id)
        return genre_json or f"Genre with remote_id '{genre.remote_id}' was not found in api :("

    def reimport_procedure(self, document):
        genre: MegogoGenre = document
        importer = self.get_importer()
        with importer:
            importer.results["Reimport genre with id"] = genre.remote_id
            genre_info = self.api.get_genre_info(remote_id=genre.remote_id)
            with importer.ignore_do_not_autoupdate:
                importer.import_genre(genre_info)


class MegogoSeasonView(MegogoMixin, GetJsonFromApiMixin, AbcVodSeasonView):
    model = MegogoSeason

    def reimport_procedure(self, document):
        season: MegogoSeason = document
        season_info = self.api.get_season_info(season.remote_id)
        if not season_info.license_is_valid():
            raise ReimportException("License restrictions: 'paidType' field value is not 'AVOD' or 'SVOD'.")

        importer = self.get_importer()
        with importer:
            importer.results["Reimport season with id"] = season.remote_id
            importer.series_cache[season.title.remote_id] = season.title
            with importer.ignore_do_not_autoupdate:
                importer.import_season(season_info)
            for episode_info in self.api.get_episodes_info_for_season(season.remote_id):
                if episode_info.license_is_valid():
                    importer.import_episode(episode_info)

    def get_api_response(self, document):
        season: MegogoSeason = document
        season_json = self.api.get_season_info_json(season.remote_id)
        return season_json or f"Season with remote_id '{season.remote_id}' was not found in api :("


class MegogoEpisodeView(MegogoMixin, GetJsonFromApiMixin, AbcVodEpisodeView):
    model = MegogoEpisode

    def get_api_response(self, document):
        episode: MegogoEpisode = document
        episode_json = self.api.get_episode_info_json(episode.remote_id)
        return episode_json or f"Episode with remote_id '{episode.remote_id}' was not found in api :("

    def reimport_procedure(self, document):
        episode: MegogoEpisode = document
        episode_info = self.api.get_episode_info(episode.remote_id)
        if not episode_info.license_is_valid():
            raise ReimportException("License restrictions. 'paidType' field value is not 'AVOD' or 'SVOD'.")
        importer = self.get_importer()
        with importer:
            importer.results["Reimport episode with id"] = episode.remote_id
            importer.seasons_cache[episode.season.remote_id] = episode.season
            with importer.ignore_do_not_autoupdate:
                importer.import_episode(episode_info)


class MegogoCollectionView(MegogoMixin, AbcVodCollectionView):
    model = MegogoCollection
    can_create = True
    can_edit = True


class MegogoImportTaskView(MegogoMixin, AbcvodImportTaskView):
    model = MegogoImportTracker
    celery_task = megogo_import_task
    can_delete = True


def init_megogo_admin(admin: Admin, caption="Megogo", parent_name=None):
    init_vod_admin(
        admin,
        caption,
        vod_mixin=MegogoMixin,
        category_view=MegogoCategoryView,
        genre_view=MegogoGenreView,
        title_view=MegogoTitleView,
        season_view=MegogoSeasonView,
        episode_view=MegogoEpisodeView,
        collection_view=MegogoCollectionView,
        parent_name=parent_name,
    )
    vod_name = MegogoMixin.vod_name
    admin.add_view(MegogoImportTaskView(name="Import tasks", category=caption, endpoint=f"{vod_name}_tasks"))
