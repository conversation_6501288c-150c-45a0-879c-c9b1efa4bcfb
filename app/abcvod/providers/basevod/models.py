from datetime import datetime
from typing import (
    Optional,
    Type,
)

import pydantic
from flask_admin.contrib.mongoengine.fields import Model<PERSON><PERSON><PERSON><PERSON>
from pydantic import (
    BaseModel,
    field_validator,
)
from pydantic_core.core_schema import ValidationInfo

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodGenre,
)
from app.abcvod.models import (
    AbcvodCopyrightHolder,
    AbcvodCountry,
)
from app.abcvod.providers.basevod.models_factory import (
    BaseVODModelsFactory,
    BaseVODModelsRepository,
)
from cmf.core.fields import MultilangString

# For backward compatibility, use with caution. Consider using `models_factory` instead.
repo: BaseVODModelsRepository = BaseVODModelsFactory.get_models_for_alias("basevod")
BaseVODCategory = repo.Category
BaseVODCollection = repo.Collection
BaseVODEpisode = repo.Episode
BaseVODGenre = repo.Genre
BaseVODSeason = repo.Season
BaseVODTitle = repo.Title
BaseVODTitleReference = repo.TitleReference
BaseVODImportInfo = repo.ImportProject


# Pydantic models.
class ImportCache(BaseModel):
    countries: dict[str, AbcvodCountry]
    genres: dict[str, AbcvodGenre]
    categories: dict[str, AbcvodCategory]
    copyright_holders: dict[str, AbcvodCopyrightHolder]
    existing_slugs: set[str]
    candidate_slugs: set[str] = set()

    class Config:
        arbitrary_types_allowed = True  # to allow 'Document' types

    @classmethod
    def generate_cache(cls, db_alias: str = "basevod") -> "ImportCache":
        repo: BaseVODModelsRepository = BaseVODModelsFactory.get_models_for_alias(db_alias)
        genre_model = repo.Genre
        category_model = repo.Category
        title_model = repo.Title

        # Fetch all countries.
        countries_queryset = AbcvodCountry.objects.all()
        countries_dict = {country.iso2: country for country in countries_queryset}
        countries_dict.update({country.iso3: country for country in countries_queryset})

        # Fetch all copyright holders.
        copyright_holders_dict = {holder.slug: holder for holder in AbcvodCopyrightHolder.objects.all()}

        # Fetch all genres & categories for this BaseVOD.
        genres_dict = {genre.slug: genre for genre in genre_model.objects.all()}
        categories_dict = {category.slug: category for category in category_model.objects.all()}

        # Fetch all existing slugs.
        title_slugs = set(title_model.objects.distinct("slug"))

        return cls(
            countries=countries_dict,
            genres=genres_dict,
            categories=categories_dict,
            copyright_holders=copyright_holders_dict,
            existing_slugs=title_slugs,
        )

    def get_countries(self, countries: str) -> list[AbcvodCountry]:
        if not countries:
            return []
        country_codes = countries.split(",")
        country_codes = [code.strip().upper() for code in country_codes]  # eliminate primitive user's typos
        for code in country_codes:
            if not code:
                continue
            if code not in self.countries:
                raise ValueError(f"Country with code {code} does not exist.")
        return [self.countries[code] for code in country_codes if code]

    def get_categories(self, categories: str) -> list[AbcvodCategory]:
        if not categories:
            return []
        slug_list = categories.split(",")
        slug_list = [slug.strip().lower() for slug in slug_list]  # eliminate primitive user's typos
        for slug in slug_list:
            if not slug:
                continue
            if slug not in self.categories:
                raise ValueError(f"Category with slug '{slug}' does not exist.")
        return [self.categories[slug] for slug in slug_list if slug]

    def get_genres(self, genres: str) -> list[AbcvodGenre]:
        if not genres:
            return []
        slug_list = genres.split(",")
        slug_list = [slug.strip().lower() for slug in slug_list]  # eliminate primitive user's typos
        for slug in slug_list:
            if not slug:
                continue
            if slug not in self.genres:
                raise ValueError(f"Genre with slug '{slug}' does not exist.")
        return [self.genres[slug] for slug in slug_list if slug]

    def get_copyright_holders(self, copyright_holders: str) -> list[AbcvodCopyrightHolder]:
        if not copyright_holders:
            return []
        slug_list = copyright_holders.split(",")
        slug_list = [slug.strip().lower() for slug in slug_list]  # eliminate primitive user's typos
        for slug in slug_list:
            if not slug:
                continue
            if slug not in self.copyright_holders:
                raise ValueError(f"Copyright holder with slug '{slug}' does not exist.")
        return [self.copyright_holders[slug] for slug in slug_list if slug]


class PublicationData(BaseModel):
    db_alias: str
    created_during_import: str

    slug: str
    caption: str
    description: str
    years: list[int]

    original_title: Optional[str] = None
    release_date: Optional[datetime] = None
    end_publish_date: Optional[datetime] = None
    poster: Optional[str] = None

    categories: list[AbcvodCategory]
    countries: list[AbcvodCountry]
    copyright_holders: list[AbcvodCopyrightHolder]
    genres: list[AbcvodGenre]

    age_rating: int
    rating: Optional[float] = None
    kp_rating: Optional[float] = None
    imdb_rating: Optional[float] = None

    is_published: bool = False

    is_series: bool = False
    season_numbers: Optional[list[int]] = None
    episodes_count: Optional[list[int]] = None
    default_season_name: str = "Сезон"
    default_episode_name: str = "Серия"

    class Config:
        arbitrary_types_allowed = True  # to allow 'Document' types
        validate_default = True  # Validate default values, because they may not be accepted in certain combinations.
        populate_by_name = True  # I use this to add default values to `ValidationInfo.data` attribute when values were not provided # noqa

    @classmethod
    def _validate_categories(cls, form_data: dict, cache: ImportCache, item):
        try:
            form_data["categories"] = cache.get_categories(form_data["categories"])
        except ValueError as e:
            form_data["categories"] = []  # mute error "input should be a valid list"
            item.form._fields["categories"].errors.append(str(e))

    @classmethod
    def _validate_genres(cls, form_data: dict, cache: ImportCache, item):
        try:
            form_data["genres"] = cache.get_genres(form_data["genres"])
        except ValueError as e:
            form_data["genres"] = []  # mute error "input should be a valid list"
            item.form._fields["genres"].errors.append(str(e))

    @classmethod
    def _validate_countries(cls, form_data: dict, cache: ImportCache, item):
        try:
            form_data["countries"] = cache.get_countries(form_data["countries"])
        except ValueError as e:
            form_data["countries"] = []  # mute error "input should be a valid list"
            item.form._fields["countries"].errors.append(str(e))

    @classmethod
    def _validate_copyright_holders(cls, form_data: dict, cache: ImportCache, item):
        try:
            form_data["copyright_holders"] = cache.get_copyright_holders(form_data["copyright_holders"])
        except ValueError as e:
            form_data["copyright_holders"] = []  # mute error "input should be a valid list"
            item.form._fields["copyright_holders"].errors.append(str(e))

    @classmethod
    def _validate_slugs(cls, form_data: dict, cache: ImportCache, item):
        if slug := form_data["slug"]:
            if slug in cache.candidate_slugs:
                item.form._fields["slug"].errors.append(f"Slug '{slug}' already used by another item in this table.")
            if slug in cache.existing_slugs:
                item.form._fields["slug"].errors.append(f"Slug '{slug}' already used by another title in database.")
            cache.candidate_slugs.add(slug)

    @classmethod
    def from_item(
        cls, item: ModelFormField, *, cache: ImportCache, import_info: BaseVODImportInfo
    ) -> Optional["PublicationData"]:
        """Generate & validate data for publication using data from table row.

        All validation errors will be registered on item's form.

        :param item: "row" from table.
        :param cache: recently fetched data about countries, categories and genres
        :return:
        """
        db_alias = import_info.db_alias
        created_during_import = str(import_info.id)

        form_data = item.form.data.copy()
        cls._validate_categories(form_data, cache, item)
        cls._validate_genres(form_data, cache, item)
        cls._validate_countries(form_data, cache, item)
        cls._validate_copyright_holders(form_data, cache, item)
        cls._validate_slugs(form_data, cache, item)

        try:
            return cls(**form_data, db_alias=db_alias, created_during_import=created_during_import)
        except pydantic.ValidationError as e:
            for error_info in e.errors():
                for field_name in error_info["loc"]:
                    if field_name not in item.form._fields:
                        continue
                    item.form._fields[field_name].errors.append(error_info["msg"])

    @field_validator("years", "season_numbers", "episodes_count", mode="before")
    def split_comma_separated_integers(cls, value: str) -> list[int]:
        if not value:
            return []
        return [int(i) for i in value.split(",")]

    @field_validator("years", mode="after")
    def validate_years(cls, value: list[int]) -> list[int]:
        if not value:
            raise ValueError("Value required.")

        for year in value:
            if year > 2100 or year < 1900:
                raise ValueError(f"Year '{year}' looks incorrect.")
        return value

    @field_validator("episodes_count", mode="after")
    def validate_seasons_and_episodes(cls, episodes_count: list[int], info: ValidationInfo) -> list[int]:
        if not info.data["is_series"]:
            return episodes_count
        if len(episodes_count) != len(info.data.get("season_numbers") or []):
            raise ValueError("Amount of values in 'Season numbers' and 'Episodes count' fields does not match.")
        return episodes_count

    @field_validator("season_numbers", "episodes_count", mode="after")
    def validate_series_fields(cls, value: list[int], info: ValidationInfo) -> list[int]:
        is_series = info.data["is_series"]
        if value and not is_series:
            raise ValueError("This field must be empty when 'Is series' flag is not checked.")
        if is_series and not value:
            raise ValueError("This field is required when 'Is series' flag is checked.")
        return value

    def publish(self):
        title: BaseVODTitle = self._publish_title()
        if self.is_series:
            self._publish_series(title)

    def _publish_title(self) -> BaseVODTitle:
        repo = BaseVODModelsFactory.get_models_for_alias(self.db_alias)
        title_model: Type[BaseVODTitle] = repo.Title
        return title_model(
            created_during_import=self.created_during_import,
            slug=self.slug,
            caption=self.caption,
            description=self.description,
            years=self.years,
            original_title=self.original_title,
            release_date=self.release_date,
            end_publish_date=self.end_publish_date,
            poster=self.poster,
            categories=self.categories,
            countries=self.countries,
            copyright_holders=self.copyright_holders,
            genres=self.genres,
            age_rating=self.age_rating,
            rating=self.rating,
            kp_rating=self.kp_rating,
            imdb_rating=self.imdb_rating,
            is_published=self.is_published,
            is_series=self.is_series,
        ).save()

    def _create_season(self, title: BaseVODTitle, season_number: int) -> BaseVODSeason:
        repo = BaseVODModelsFactory.get_models_for_alias(self.db_alias)
        season_model: Type[BaseVODSeason] = repo.Season
        return season_model(
            caption=MultilangString(f"{self.default_season_name} {season_number}"),
            slug=f"{self.slug}-s{season_number}",
            title=title,
            number=season_number,
            is_published=self.is_published,
        ).save()

    def _create_episodes(self, season: BaseVODSeason, episodes_count: int):
        repo = BaseVODModelsFactory.get_models_for_alias(self.db_alias)
        episode_model: Type[BaseVODEpisode] = repo.Episode

        for number in range(1, episodes_count + 1):
            episode_model(
                caption=MultilangString(f"{self.default_episode_name} {number}"),
                slug=f"{season.slug}-e{number}",
                title=season.title,
                season=season,
                number=number,
                is_published=self.is_published,
            ).save()

    def _publish_series(self, title: BaseVODTitle):
        for season_number, episodes_count in zip(self.season_numbers, self.episodes_count):
            season = self._create_season(title, season_number)
            self._create_episodes(season, episodes_count)
