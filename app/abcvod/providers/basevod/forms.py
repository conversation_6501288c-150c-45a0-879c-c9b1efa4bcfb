from typing import Type

from flask_admin.form import DatePickerWidget
from flask_wtf import <PERSON>laskForm
from mongoengine import DoesNotExist
from wtforms import ValidationError
from wtforms.fields import (
    <PERSON><PERSON>an<PERSON>ield,
    StringField,
)
from wtforms.fields.html5 import Inte<PERSON><PERSON>ield
from wtforms.validators import (
    <PERSON><PERSON>equired,
    NumberRange,
)
from wtforms.widgets.core import TextInput
from wtforms.widgets.html5 import NumberInput

from app.abcvod.core.models.abcvod import (
    AbcvodEpisode,
    AbcvodSeason,
    AbcvodTitle,
)
from app.abcvod.providers.basevod.models import (
    BaseVODEpisode,
    BaseVODSeason,
    BaseVODTitle,
)
from app.abcvod.providers.basevod.models_factory import ImportItem
from app.abcvod.providers.basevod.validators import CustomSlugValidator
from app.common.fields import AjaxSelectCustomField
from cmf.core.form.fields import <PERSON>lang<PERSON>ield
from cmf.core.form.form import CmsEmbeddedForm
from cmf.core.image_field.form import CmsI<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ield
from cmf.core.image_field.utils import upload_temp_file_to_storage


class CustomSlugMixin:

    default_season_slug_template = "{slug}-s{season_number}"
    default_episode_slug_template = "{season_slug}-e{episode_number}"

    custom_season_slug = StringField(
        "Custom season slug",
        description=f"If empty - slug will be created automatically, pattern: {default_season_slug_template}",
        validators=[CustomSlugValidator()],
    )

    custom_episode_slug = StringField(
        "Custom episode slug",
        description=f"If empty - slug will be created automatically, pattern: {default_episode_slug_template}",
        validators=[CustomSlugValidator()],
    )

    def get_slug_prefix(self) -> str:
        return ""

    def get_slug(self, season_number: int) -> str:
        if custom_slug := self.custom_season_slug.data:
            return custom_slug.replace("{number}", str(season_number))

        if prefix := self.get_slug_prefix():
            return f"{prefix}-s{season_number}"

        return f"s{season_number}"

    def get_episode_slug(self, season_slug: str, episode_number: int):
        if custom_slug := self.custom_episode_slug.data:
            slug = custom_slug.replace("{number}", str(episode_number))
            return f"{season_slug}-{slug}"

        return f"{season_slug}-e{episode_number}"


class BaseVODSeriesWizardForm(CustomSlugMixin, FlaskForm):
    caption = MultilangField(label="Caption", text_type="text", value_type="dict", validators=[DataRequired()])
    slug = StringField("Slug", validators=[DataRequired()])
    seasons_count = IntegerField(
        "Amount of seasons",
        validators=[DataRequired(), NumberRange(min=1, message="Minimum amount of seasons is 1.")],
    )
    episodes_count = IntegerField(
        "Amount of episodes per season",
        validators=[DataRequired(), NumberRange(min=1, message="Minimum amount of episodes is 1.")],
    )
    publish_seasons = BooleanField(
        "Publish seasons", description="If checked - all new seasons will be created with 'is published' flag."
    )
    publish_episodes = BooleanField(
        "Publish episodes",
        description="If checked - all new episodes will be created with 'is published' flag.",
        default=True,
    )
    default_season_caption = StringField(
        "Default season caption",
        description=(
            "Will be used for default 'caption'. For value 'Season' result will be 'Season 1', 'Season 2' etc."
        ),
        default="Сезон",
        validators=[DataRequired()],
    )
    default_episode_caption = StringField(
        "Default episode caption",
        description=(
            "Will be used for default 'caption'. For value 'Episode' result will be 'Episode 1', 'Episode 2' etc."
        ),
        default="Серия",
        validators=[DataRequired()],
    )
    episodes_poster = CmsImageSimpleFormField(
        storage_dir="basevod_images",
        label="Episodes poster",
        description="If provided, this poster will be assigned to all episodes.",
    )


class CreateBaseVODSeasonForm(CustomSlugMixin, FlaskForm):
    """New form for `basevod_season.create_view`.

    Better not use directly (except type hinting), use 'create_season_form_factory' instead.
    """

    season_model: Type[BaseVODSeason] = BaseVODSeason
    episode_model: Type[BaseVODEpisode] = BaseVODEpisode

    title = AjaxSelectCustomField(label="Title", loader=BaseVODTitle.get_ajax_loader(), validators=[DataRequired()])
    caption = MultilangField(label="Caption", text_type="text", value_type="dict", validators=[DataRequired()])
    season_number = IntegerField(
        "Season number", validators=[DataRequired(), NumberRange(min=1, message="Season number cannot be less than 1.")]
    )
    episodes_count = IntegerField(
        "Episodes count",
        description="Amount of episodes to create.",
        validators=[DataRequired(), NumberRange(min=1, message="Minimum amount of episodes is 1.")],
    )
    episodes_poster = CmsImageSimpleFormField(
        storage_dir="basevod_images",
        label="Episodes poster",
        description="If provided, this poster will be assigned to all episodes.",
    )
    publish_seasons = BooleanField(
        "Publish seasons", description="If checked - all new seasons will be created with 'is published' flag."
    )
    publish_episodes = BooleanField(
        "Publish episodes",
        description="If checked - all new episodes will be created with 'is published' flag.",
        default=True,
    )

    default_season_slug_template = "{title_slug}-s{season_number}"

    def get_slug_prefix(self) -> str:
        title: BaseVODTitle = self.title.data
        if not title:
            raise ValidationError("'title' object is required for generating slug.")
        return f"{title.slug}"

    def validate(self):
        try:
            self.validate_custom_slug()
        except ValidationError as e:
            getattr(self, e.args[1]).errors = (e.args[0],)
            return False
        return super().validate()

    def validate_custom_slug(self):
        # Check slug for season itself.
        slug = self.get_slug(self.season_number.data)
        try:
            self.season_model.objects.get(slug=slug)
            raise ValidationError(f"Season with slug '{slug}' already exists.", "custom_season_slug")
        except DoesNotExist:
            pass  # OK
        # Predict and check slugs for episodes.
        if not self.episodes_count.data:
            return  # Validation will fail earlier, because of 'DataRequired()' validation.
        slugs = [self.get_episode_slug(slug, number) for number in range(1, self.episodes_count.data + 1)]
        existing_episodes = self.episode_model.objects(slug__in=slugs)
        if existing_episodes:
            existing_slugs = ", ".join(episode.slug for episode in existing_episodes)
            raise ValidationError(
                f"Impossible to create episodes - there is episodes with slugs: {existing_slugs}", "custom_episode_slug"
            )

    def upload_poster(self) -> str:
        if not (temp_file_path := self.episodes_poster.data):
            return ""

        if "temp_files/" not in temp_file_path:
            return temp_file_path  # already uploaded.

        title_slug = self.title.data.slug
        extension = self.episodes_poster.data.rsplit(".", 1)[1].lower()
        poster_path = f"basevod_images/{title_slug}/season-{self.season_number.data}-poster.{extension}"

        return upload_temp_file_to_storage(temp_file_path=temp_file_path, storage_path=poster_path)

    def create_season_and_episodes(self) -> BaseVODSeason:
        poster_path = self.upload_poster()
        print(f"{self.publish_seasons.data=}")
        season: BaseVODSeason = self.season_model(
            title=self.title.data,
            caption=self.caption.data,
            number=self.season_number.data,
            slug=self.get_slug(self.season_number.data),
            is_published=self.publish_seasons.data,
        ).save()
        for episode_number in range(1, self.episodes_count.data + 1):
            print(f"{self.publish_episodes.data=}")
            self.episode_model(
                caption=f"Episode {episode_number}",
                title=self.title.data,
                season=season,
                number=episode_number,
                slug=self.get_episode_slug(season.slug, episode_number),
                poster=poster_path,
                is_published=self.publish_episodes.data,
            ).save()
        return season


def create_season_form_factory(
    title_model: Type[AbcvodTitle], season_model_: Type[AbcvodSeason], episode_model_: Type[AbcvodEpisode]
) -> Type[CreateBaseVODSeasonForm]:
    class _CreateBaseVODSeasonForm(CreateBaseVODSeasonForm):
        season_model = season_model_
        episode_model = episode_model_
        title = AjaxSelectCustomField(label="Title", loader=title_model.get_ajax_loader(), validators=[DataRequired()])

        def __init__(self, *args, **kwargs):
            super(_CreateBaseVODSeasonForm, self).__init__(*args, **kwargs)
            self._fields.move_to_end("title", False)  # Inheritance moves 'title' to end, this keeps 'title' at 1 pos.

    return _CreateBaseVODSeasonForm


class ImportInfoItemForm(CmsEmbeddedForm):
    model: ImportItem  # for reference

    form_args = {
        "slug": {"widget": TextInput()},
        "caption": {"widget": TextInput()},
        "years": {"widget": TextInput()},
        "original_title": {"widget": TextInput()},
        "categories": {"widget": TextInput()},
        "genres": {"widget": TextInput()},
        "countries": {"widget": TextInput()},
        "rating": {"widget": NumberInput(step=0.1, min=0, max=10)},
        "imdb_rating": {"widget": NumberInput(step=0.1, min=0, max=10)},
        "kp_rating": {"widget": NumberInput(step=0.1, min=0, max=10)},
        "age_rating": {"widget": NumberInput(step=1, min=0, max=21)},
        "release_date": {
            "widget": DatePickerWidget(),
            "format": "%Y-%m-%d",
        },
        "end_publish_date": {
            "widget": DatePickerWidget(),
            "render_kw": {"data-drops": "up"},
            "format": "%Y-%m-%d",
        },
        "season_numbers": {"widget": TextInput()},
        "episodes_count": {"widget": TextInput()},
        "default_season_name": {"widget": TextInput()},
        "default_episode_name": {"widget": TextInput()},
    }
