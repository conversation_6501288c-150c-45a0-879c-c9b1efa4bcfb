import re

from flask_babelex import gettext as _
from wtforms.validators import StopValidation


class CustomSlugValidator:
    """Validates that a custom slug includes placeholder for number."""

    PATTERN = r"^[a-z0-9-]*{number}[a-z0-9-]*$"

    def __init__(self, message=None):
        self.message = _(
            "Must contain placeholder {number} and only lowercase latin letters, numbers, dashes or underscores"
        )  # noqa

    def __call__(self, form, field):
        if not field.data:
            return

        if not re.match(self.PATTERN, field.data):
            raise StopValidation(self.message)
