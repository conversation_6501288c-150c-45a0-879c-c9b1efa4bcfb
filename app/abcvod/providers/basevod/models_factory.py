from dataclasses import dataclass
from typing import Type

from mongoengine import (
    CASCADE,
    PULL,
    BooleanField,
    DateTimeField,
    EmbeddedDocumentField,
    FloatField,
    IntField,
    ListField,
    ReferenceField,
    StringField,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodEpisode,
    AbcvodGenre,
    AbcvodSeason,
    AbcvodTitle,
)
from app.abcvod.core.models.fields import (
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)
from app.common.models import CreatedUpdatedMixin
from app.security.models import User
from cmf.core.fields import MultilangField
from cmf.core.models import (
    CmsDocument,
    CmsEmbeddedDocument,
    CoreTopLevelDocumentMetaclass,
)


class ImportItem(CmsEmbeddedDocument):
    slug = StringField(required=True)
    caption = StringField(required=True)
    description = StringField(required=True)
    years = StringField(required=True, help_text="Comma separated years, i.e.: 2008,2009,2010")
    original_title = StringField()
    release_date = DateTimeField()
    categories = StringField(required=True, help_text="Comma separated category slugs, i.e.: 'movies,18plus'")
    genres = StringField(required=True, help_text="Comma separated genre slugs, i.e.: 'drama,comedy,fantasy'")
    copyright_holders = StringField(
        help_text="Comma separated copyright holders slugs, i.e. 'neflix' or 'mosfilm,lenfilm'."
    )
    rating = FloatField(help_text="Value from 0 to 10.")
    imdb_rating = FloatField()
    kp_rating = FloatField()
    age_rating = IntField(required=True)
    countries = StringField(
        required=True, help_text="Comma separated ISO2 or ISO3 country codes, i.e.: 'RUS,GE,RS'. See ABCVOD->Countries."
    )
    end_publish_date = DateTimeField()
    is_published = BooleanField()

    is_series = BooleanField()
    season_numbers = StringField(
        verbose_name="Season numbers",
        help_text="Required when flag 'Is series' is checked. "
        "Comma separated season numbers, i.e.: '1' or '1,2,3,4,5'.",
    )
    episodes_count = StringField(
        verbose_name="Episodes count",
        help_text="Required when flag 'Is series' is checked. "
        "Comma-separated number of episodes for each season. For example, '9' or '9,9,9' or '10,8,11,4'. "
        "The number of comma-separated values must match the number of seasons in the 'Season numbers' field.",
    )
    default_season_name = StringField(default="Сезон")
    default_episode_name = StringField(default="Серия")

    def get_csv_data(self) -> str:
        return self._data

    def clean(self):
        self.default_season_name = self.default_season_name or "Сезон"
        self.default_episode_name = self.default_episode_name or "Серия"
        super().clean()


class AbstractImportProject(CreatedUpdatedMixin, CmsDocument):
    meta = {"abstract": True}

    name = StringField(verbose_name="Name", required=True, max_length=200)
    published_by = ReferenceField(verbose_name="Published by", document_type=User, reverse_delete_rule=PULL)
    items = ListField(EmbeddedDocumentField(ImportItem))
    is_published = BooleanField()

    def __str__(self):
        if not self.id:
            return "New Import Project"
        return f"Import Project '{self.name}'"  # Instead of `AbstractImportProject object` in edit page


@dataclass
class BaseVODModelsRepository:
    Category: Type[AbcvodCategory]
    Collection: Type[AbcvodCollection]
    Episode: Type[AbcvodEpisode]
    Genre: Type[AbcvodGenre]
    Season: Type[AbcvodSeason]
    Title: Type[AbcvodTitle]
    TitleReference: Type[AbcvodTitleReference]
    ImportProject: Type[AbstractImportProject]


class BaseVODMeta(CoreTopLevelDocumentMetaclass):
    def __new__(mcs, name, bases, attrs, *, db_alias: str):
        if db_alias != "basevod":
            name = f"{name}_{db_alias}"  # add suffix for "extra" instances of BaseVOD.
        meta = attrs.setdefault("meta", {})
        meta["db_alias"] = db_alias
        newclass = super(BaseVODMeta, mcs).__new__(mcs, name, bases, attrs)
        return newclass


class BaseVODModelsFactory:
    """Factory for models for extra instances of BaseVOD."""

    _repositories: dict[str, BaseVODModelsRepository] = {}

    @classmethod
    def get_models_for_alias(cls, db_alias: str) -> BaseVODModelsRepository:
        if db_alias in cls._repositories:
            return cls._repositories[db_alias]
        result = cls._make_repo_for_alias(db_alias)
        cls._repositories[db_alias] = result
        return result

    @classmethod
    def _make_repo_for_alias(cls, db_alias: str) -> BaseVODModelsRepository:
        collection_reference_name = "BaseVODCollection" if db_alias == "basevod" else f"BaseVODCollection_{db_alias}"

        class BaseVODGenre(AbcvodGenre, metaclass=BaseVODMeta, db_alias=db_alias):
            meta = {"collection": "basevod_genre"}

        class BaseVODCategory(AbcvodCategory, metaclass=BaseVODMeta, db_alias=db_alias):
            meta = {"collection": "basevod_category"}

            genres = ListField(ReferenceField(BaseVODGenre, reverse_delete_rule=PULL))

        class BaseVODTitle(AbcvodTitle, metaclass=BaseVODMeta, db_alias=db_alias):
            meta = {
                "collection": "basevod_title",
                "indexes": [
                    "categories",
                    "genres",
                ],
            }

            categories = ListField(ReferenceField(BaseVODCategory, reverse_delete_rule=PULL))
            genres = ListField(ReferenceField(BaseVODGenre, reverse_delete_rule=PULL))
            collections = ListField(ReferenceField(collection_reference_name))

        class BaseVODTitleReference(AbcvodTitleReference):
            meta = {"abstract": True}

            title = ReferenceField(BaseVODTitle)

            def __str__(self):
                return str(self.title.caption.translations.get("default") or self.title.id)

        class BaseVODCollection(AbcvodCollection, metaclass=BaseVODMeta, db_alias=db_alias):
            meta = {"collection": "basevod_collection"}

            titles = TitlesField(BaseVODTitleReference)
            titles_excluded = TitlesExcludedField(BaseVODTitleReference)
            categories = ReferenceCriteriaField(BaseVODTitle.categories)
            genres = ReferenceCriteriaField(BaseVODTitle.genres)
            imdb_rating = RatingCriteriaField(BaseVODTitle.imdb_rating)
            kp_rating = RatingCriteriaField(BaseVODTitle.kp_rating)

        class BaseVODSeason(AbcvodSeason, metaclass=BaseVODMeta, db_alias=db_alias):
            meta = {
                "collection": "basevod_season",
                "indexes": ["title"],
            }

            title = ReferenceField(BaseVODTitle, reverse_delete_rule=CASCADE)
            caption = MultilangField(max_length=300)

            def _set_default_caption(self):
                if self.caption:
                    super(BaseVODSeason, self)._set_default_caption()
                else:
                    # Season without title looks impossible, but previously there were check for this.
                    if self.title:
                        self.default_caption = f"{self.title} - Season {self.number}"
                    else:
                        self.default_caption = f"Season {self.number}"

            def __str__(self):
                if self.caption:
                    return super(BaseVODSeason, self).__str__()
                if not self.title:
                    return f"{self.number} season"
                return f"{(self.title.caption.translations.get('default') or str(self.title.id))} {self.number} season"

        class BaseVODEpisode(AbcvodEpisode, metaclass=BaseVODMeta, db_alias=db_alias):
            meta = {
                "collection": "basevod_episode",
                "indexes": [
                    "title",
                    "season",
                ],
            }

            title = ReferenceField(BaseVODTitle, reverse_delete_rule=CASCADE)
            season = ReferenceField(BaseVODSeason, reverse_delete_rule=CASCADE)

        class BaseVODImportProject(AbstractImportProject, metaclass=BaseVODMeta, db_alias=db_alias):
            meta = {"collection": "basevod_import_project"}

        repo = BaseVODModelsRepository(
            Category=BaseVODCategory,
            Collection=BaseVODCollection,
            Episode=BaseVODEpisode,
            Genre=BaseVODGenre,
            Season=BaseVODSeason,
            Title=BaseVODTitle,
            TitleReference=BaseVODTitleReference,
            ImportProject=BaseVODImportProject,
        )
        return repo
