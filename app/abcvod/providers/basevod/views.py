from __future__ import annotations

import io
from io import String<PERSON>
from typing import (
    Generator,
    Iterable,
    List,
    Type,
)

import mongoengine
import pandas as pd
from flask import (
    Response,
    current_app,
    flash,
    g,
    redirect,
    request,
)
from flask_admin import Admin
from flask_admin.form import (
    BaseForm,
    rules,
)
from flask_login import current_user
from mongoengine import Document
from werkzeug.datastructures import FileStorage

from app.abcvod.core.views import (
    AbcVodCategoryView,
    AbcVodCollectionView,
    AbcVodEpisodeView,
    AbcVodGenreView,
    AbcVodSeasonView,
    AbcVodTitleView,
    AbcVodViewMixin,
    FieldsOrderMixin,
)
from app.abcvod.providers.basevod.forms import (
    BaseVODSeriesWizardForm,
    CreateBaseVODSeasonForm,
    ImportInfoItemForm,
    create_season_form_factory,
)
from app.abcvod.providers.basevod.models import (
    BaseVODCategory,
    BaseVODCollection,
    BaseVODEpisode,
    BaseVODGenre,
    BaseVODImportInfo,
    BaseVODSeason,
    BaseVODTitle,
    ImportCache,
    PublicationData,
)
from app.abcvod.providers.basevod.models_factory import (
    BaseVODModelsFactory,
    ImportItem,
)
from app.abcvod.providers.basevod.widgets import ImportInfoTableWidget
from app.auth import expose
from app.cms.utils import CmsJsonResponse
from cmf.core.fields import MultilangString
from cmf.core.image_field.form import CmsImageSimpleFormField
from cmf.core.image_field.utils import upload_temp_file_to_storage
from cmf.core.view import (
    CmsBaseModelView,
    CopyMixin,
)
from cmf.core.views.base_form_view import (
    BaseFormView,
    FormValidationError,
)
from cmf.core.views.mass_actions_mixin import (
    EditMultipleAction,
    MassActionMixin,
)


class BaseVodFactoryMixin(CmsBaseModelView):
    _mute_kwargs_model_warning = True  # After #81974, it is OK to initialize BaseVOD ModelViews with custom models.

    def __init__(self, vod_name: str, *args, **kwargs):
        self.vod_name = vod_name  # used in views for urls and formatters
        self.models = BaseVODModelsFactory.get_models_for_alias(vod_name)
        super().__init__(*args, **kwargs)


class BaseVODMixin(BaseVodFactoryMixin, AbcVodViewMixin, FieldsOrderMixin):
    # It is allowed to create and delete documents in BaseVOD manually
    can_create = True
    can_delete = True

    _mute_kwargs_model_warning = True  # After #81974, it is OK to initialize BaseVOD ModelViews with custom models.

    def __init__(self, vod_name: str, *args, **kwargs):
        # It is allowed to edit "slug" field in all BaseVOD documents
        if "slug" not in self._top_fields:
            self._top_fields = self._top_fields + ("slug",)
        self._system_fields = tuple(field for field in self._system_fields if field != "slug")
        self.column_editables += (
            "slug",
            "is_published",
        )
        super().__init__(vod_name=vod_name, *args, **kwargs)

    @property
    def genre_class(self):
        return self.models.Genre

    @property
    def category_class(self):
        return self.models.Category

    @property
    def title_class(self):
        return self.models.Title

    @property
    def season_class(self):
        return self.models.Season

    @property
    def episode_class(self):
        return self.models.Episode

    @property
    def collection_class(self):
        return self.models.Collection


class BaseVODTitleView(CopyMixin, BaseVODMixin, AbcVodTitleView):
    model = BaseVODTitle


class BaseVODCategoryView(BaseVODMixin, AbcVodCategoryView):
    model = BaseVODCategory


class BaseVODGenreView(BaseVODMixin, AbcVodGenreView):
    model = BaseVODGenre


class BaseVODSeasonView(CopyMixin, BaseVODMixin, AbcVodSeasonView):
    model = BaseVODSeason

    list_template = "abcvod/basevod_season_list.html"

    @property
    def form_widget_args(self) -> dict:
        result = super(BaseVODSeasonView, self).form_widget_args
        result.pop("title", None)  # #71029 BaseVOD season is special
        return result

    def _create_season(self, form: CreateBaseVODSeasonForm):
        if not self.validate_form(form):
            return self.render(self.create_template, form=form)
        season = form.create_season_and_episodes()
        return redirect(self.get_url(".edit_view", id=season.id))

    @expose("/create_season_wizard/", methods=("GET", "POST"))
    def create_season_wizard(self):
        form: CreateBaseVODSeasonForm = create_season_form_factory(
            self.models.Title, self.models.Season, self.models.Episode
        )()

        if request.method == "POST":
            return self._create_season(form)

        return self.render(self.create_template, form=form, form_opts=None)


class BaseVODEpisodeView(CopyMixin, MassActionMixin, BaseVODMixin, AbcVodEpisodeView):
    model = BaseVODEpisode

    mass_actions = [
        EditMultipleAction(name="set_is_published", text="Set 'Is published'", fields=("is_published",)),
        EditMultipleAction(name="set_poster", text="Set 'Poster'", fields=("poster",)),
    ]


class BaseVODCollectionView(BaseVODMixin, AbcVodCollectionView):
    model = BaseVODCollection


class BaseVODSeriesWizardView(BaseFormView):
    form_class = BaseVODSeriesWizardForm
    form: BaseVODSeriesWizardForm
    season_model: Type[BaseVODSeason]
    title_model: Type[BaseVODTitle]
    episode_model: Type[BaseVODEpisode]
    vod_name: str

    def __init__(
        self,
        *args,
        vod_name: str,
        season_model: Type[BaseVODSeason],
        title_model: Type[BaseVODTitle],
        episode_model: Type[BaseVODEpisode],
        **kwargs,
    ):
        self.vod_name = vod_name
        self.season_model = season_model
        self.title_model = title_model
        self.episode_model = episode_model
        super().__init__(*args, **kwargs)

    def _get_possible_season_slugs(self) -> List[str]:
        count = self.form.seasons_count.data
        return [self.form.get_slug(x) for x in range(1, count + 1)]

    def validate_slug(self):
        slug = self.form.slug.data
        existing_titles = self.title_model.objects.no_dereference().filter(slug=slug)
        if len(existing_titles):
            raise FormValidationError(f"Title with slug '{slug}' already exists.", "slug")

    def validate_seasons_count(self):
        season_slugs = self._get_possible_season_slugs()
        seasons = self.season_model.objects.filter(slug__in=season_slugs)
        if len(seasons):
            existing_slugs = ", ".join((season.slug for season in seasons))
            raise FormValidationError(f"These slugs are already taken: {existing_slugs}", "custom_season_slug")

    def validate_episodes_count(self):
        episodes_count = self.form.episodes_count.data
        season_slugs = self._get_possible_season_slugs()
        possible_episodes_slugs = [
            self.form.get_episode_slug(slug, x) for slug in season_slugs for x in range(1, episodes_count + 1)
        ]
        episodes = self.episode_model.objects.filter(slug__in=possible_episodes_slugs)
        if len(episodes):
            existing_slugs = ", ".join((episode.slug for episode in episodes))
            raise FormValidationError(f"These slugs are already taken: {existing_slugs}", "custom_episode_slug")

    def _create_seasons(self, title: BaseVODTitle) -> Generator[BaseVODSeason, None, None]:
        season_numbers = range(1, self.form.seasons_count.data + 1)
        slug = title.slug
        season_caption = self.form.default_season_caption.data
        for number in season_numbers:
            yield self.season_model(
                caption=MultilangString(f"{season_caption} {number}"),
                slug=f"{slug}-s{number}",
                title=title,
                number=number,
                is_published=self.form.publish_seasons.data,
            ).save()

    def upload_poster(self) -> str:
        poster_field: CmsImageSimpleFormField = self.form.episodes_poster

        if not (temp_file_path := poster_field.data):
            return ""

        if not "temp_files/" not in temp_file_path:
            # May be already uploaded.
            return temp_file_path

        title_slug = self.form.slug.data
        extension = poster_field.data.rsplit(".", 1)[1].lower()
        storage_path = f"basevod_images/{title_slug}/episodes-poster.{extension}"

        return upload_temp_file_to_storage(temp_file_path=temp_file_path, storage_path=storage_path)

    def _create_episodes(self, title: BaseVODTitle, seasons: Iterable[BaseVODSeason]):
        episode_numbers = range(1, self.form.episodes_count.data + 1)
        episode_caption = self.form.default_episode_caption.data
        poster_link = self.upload_poster()
        for season in seasons:
            for number in episode_numbers:
                self.episode_model(
                    caption=MultilangString(f"{episode_caption} {number}"),
                    slug=f"{season.slug}-e{number}",
                    title=title,
                    season=season,
                    number=number,
                    is_published=self.form.publish_episodes.data,
                    poster=poster_link,
                ).save()

    def validate_form(self):
        try:
            self.validate_slug()
            self.validate_seasons_count()
            self.validate_episodes_count()
        except FormValidationError as e:
            getattr(self.form, e.args[1]).errors = (e.args[0],)
            return False
        return super().validate_form()

    def form_valid(self):
        slug = self.form.slug.data
        caption = self.form.caption.data
        title = self.title_model(caption=caption, slug=slug, is_series=True)
        try:
            # Create title, seasons, episodes
            title.save()
            seasons_generator = self._create_seasons(title)
            self._create_episodes(title, seasons_generator)
        except Exception:
            # Fallback - delete title, reraise exception. Seasons and episodes will to be deleted automatically.
            if title.id:
                title.delete()
            raise
        flash(f"Successfully created series '{title.default_caption}'", category="success")
        return redirect(self.get_url(f"{self.vod_name}_titles.edit_view", id=str(title.id)))


class BaseVODImportView(BaseVodFactoryMixin, CmsBaseModelView):
    model = BaseVODImportInfo
    has_menu = False
    create_template = "abcvod/basevod/import_info_create_view.html"
    edit_template = "abcvod/basevod/import_info_edit_view.html"
    form_template_name = "abcvod/basevod/import_info_form.html"

    column_default_sort = "-created_at"

    form_excluded_columns = {
        "created_at",
        "updated_at",
        "published_by",
        "is_published",
    }

    form_create_rules = {
        rules.Field("name"),
    }

    column_sortable_list = (
        "is_published",
        "created_at",
        "updated_at",
    )

    column_list = (
        "name",
        "created_at",
        "updated_at",
        "is_published",
        "published_by",
    )

    form_args = {
        "items": {"widget": ImportInfoTableWidget()},
    }

    form_subdocuments = {
        "items": {
            "form_subdocuments": {
                None: ImportInfoItemForm(),
            }
        }
    }

    def _get_current_document(self) -> Document | None:
        """Optimize requests to database."""
        if "document" not in g:
            _id = request.args.get("id")
            if not _id:
                return
            g.document = self.model.objects.get(id=_id)
        return g.document

    @expose("/save", methods=["POST"])
    def save(self) -> Response:
        form_class: Type[BaseForm] = self.get_form()
        form: BaseForm = form_class(request.form)
        if form.validate():
            document = self._get_current_document()
            self.update_model(form, document)
        else:
            return self._invalid_form_json_response(form)
        return self._successful_form_json_response(form)

    @expose("/validate", methods=["POST"])
    def validate(self) -> Response:
        form_class: Type[BaseForm] = self.get_form()
        form: BaseForm = form_class(request.form)

        # This is basic validation, it is not strict.
        # Passing this validation is enough to save document, but not enough to publish.
        if form.validate():
            document = self._get_current_document()
            self.update_model(form, document)
        else:
            return self._invalid_form_json_response(form)
        _, is_valid = self._validate_publication_data(form)
        if is_valid:
            return self._successful_form_json_response(
                form, "Document saved, all data is valid and ready to be published!"
            )
        return self._warning_form_json_response(form, message="Document is saved, but data contain errors.")

    @expose("/save_and_publish", methods=["POST"])
    def save_and_publish(self) -> Response:
        document: BaseVODImportInfo = self._get_current_document()
        if document.is_published:
            return CmsJsonResponse.error(message="This import is already published.")
        form_class: Type[BaseForm] = self.get_form()
        form: BaseForm = form_class(request.form)

        # This is basic validation, it is not strict.
        # Passing this validation is enough to save document, but not enough to publish.
        if form.validate():
            self.update_model(form, document)
        else:
            return self._invalid_form_json_response(form)
        return self._validate_and_publish(form)

    @expose("/unpublish", methods=["POST"])
    def unpublish(self) -> Response:
        import_info: BaseVODImportInfo = self._get_current_document()
        import_id = str(import_info.id)
        repo = BaseVODModelsFactory.get_models_for_alias(self.vod_name)
        episodes_deleted = repo.Episode.objects(created_during_import=import_id).delete()
        seasons_deleted = repo.Season.objects(created_during_import=import_id).delete()
        titles_deleted = repo.Title.objects(created_during_import=import_id).delete()
        import_info.is_published = False
        import_info.save()
        url = self.get_url(".edit_view", id=import_id)
        message = (
            "All related data was deleted from db.\n"
            f"Titles deleted: {titles_deleted}\n"
            f"Seasons deleted: {seasons_deleted}\n"
            f"Episodes deleted: {episodes_deleted}"
        )
        return CmsJsonResponse.redirect(url, message=message, timeout=5000)

    @expose("/export_csv", methods=["GET"])
    def export_csv(self) -> Response:
        import_info: BaseVODImportInfo = self._get_current_document()
        if not import_info:
            # Just in case. Should not be possible if UI is correct.
            return CmsJsonResponse.error(message="Cannot find ImportInfo in database. Is this document saved?")

        csv_data_list: list[dict] = [item.get_csv_data() for item in import_info.items]
        df = pd.DataFrame(csv_data_list)

        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, sep=";", index=False)

        return Response(
            csv_buffer.getvalue(),
            status=200,
            content_type="text/csv",
            headers={"Content-Disposition": f"attachment;  filename=import_info_{import_info.id}.csv"},
        )

    @staticmethod
    def _convert_value(value):
        if value.lower() in ["true", "false"]:
            return value.lower() == "true"
        return value

    def _read_import_items_from_csv(self, stream: StringIO) -> list[ImportItem]:
        df = pd.read_csv(stream, dtype={"years": str}, keep_default_na=False, delimiter=";")
        df = df.replace("", None)  # Replace empty strings with None
        list_csv_data: list[dict] = df.to_dict(orient="records")
        return [ImportItem._from_son(son) for son in list_csv_data]

    @expose("/import_csv", methods=["POST"])
    def import_csv(self) -> Response:
        import_info: BaseVODImportInfo = self._get_current_document()
        if import_info.is_published:
            return CmsJsonResponse.error(message="You cannot 'Import CSV' for published documents.")
        file: FileStorage = request.files.get("file")

        if not file:
            CmsJsonResponse.error(message="Expected file, but no file received.")

        stream = StringIO(file.stream.read().decode("UTF-8"))
        items = self._read_import_items_from_csv(stream)
        import_info.items = items
        try:
            import_info.validate()
        except mongoengine.ValidationError as e:
            return CmsJsonResponse.error(message=f"CSV data is invalid: {e}")

        import_info.save()
        redirect_url = self.get_url(".edit_view", id=str(import_info.id))
        return CmsJsonResponse.redirect(redirect_url, message="Import succeeded.")

    def _render_form(self, form) -> str:
        """Shortcut."""
        return self.render(self.form_template_name, form=form, model=self._get_current_document())

    def _invalid_form_json_response(self, form, message="Form contains errors.") -> Response:
        return CmsJsonResponse.error(data={"html": self._render_form(form)}, message=message)

    def _successful_form_json_response(self, form, message="Changes saved successfully."):
        return CmsJsonResponse.success(data={"html": self._render_form(form)}, message=message)

    def _warning_form_json_response(self, form, message):
        return CmsJsonResponse.warning(data={"html": self._render_form(form)}, message=message)

    def _set_info_published(self):
        document: BaseVODImportInfo = self._get_current_document()
        document.published_by = current_user
        document.is_published = True
        document.save()

    def _validate_publication_data(self, form) -> tuple[list[PublicationData], bool]:
        # Collect publication data AND! apply extra validation for items.
        # All found errors will be added to form.
        cache = ImportCache.generate_cache(db_alias=self.vod_name)
        import_info: BaseVODImportInfo = self._get_current_document()
        publication_data = [
            PublicationData.from_item(item=item, cache=cache, import_info=import_info) for item in form.items
        ]

        is_valid = True
        # `PublicationData.from_item()` registers all errors directly on item's form.
        # So we check if any item contain errors.
        for item in form.items:
            if item.errors:
                is_valid = False
                break

        return publication_data, is_valid

    def _validate_and_publish(self, form: BaseForm) -> Response:
        """Really strict and expensive validation."""
        publication_data, is_valid = self._validate_publication_data(form)
        if not is_valid:
            return self._invalid_form_json_response(form, "Some items cannot be published, see details.")
        return self._publish(publication_data)

    def _publish(self, publication_data: list[PublicationData]) -> Response:
        for data in publication_data:
            data.publish()
        self._set_info_published()
        redirect_url = self.get_url(".index_view")
        return CmsJsonResponse.redirect(redirect_url, message="All items were created successfully!")


def _init_basevod_admin(admin: Admin, *, db_alias: str, caption: str, parent_name: str = None):
    models = BaseVODModelsFactory.get_models_for_alias(db_alias)
    admin.add_sub_category(caption, parent_name=parent_name)
    view_kwargs = {
        "category": caption,
        "vod_name": db_alias,  # used internally for urls
    }
    admin.add_views(
        BaseVODCategoryView(name="Categories", model=models.Category, endpoint=f"{db_alias}_categories", **view_kwargs),
        BaseVODGenreView(name="Genres", model=models.Genre, endpoint=f"{db_alias}_genres", **view_kwargs),
        BaseVODTitleView(name="Titles", model=models.Title, endpoint=f"{db_alias}_titles", **view_kwargs),
        BaseVODSeasonView(name="Seasons", model=models.Season, endpoint=f"{db_alias}_seasons", **view_kwargs),
        BaseVODEpisodeView(name="Episodes", model=models.Episode, endpoint=f"{db_alias}_episodes", **view_kwargs),
        BaseVODCollectionView(
            name="Collections", model=models.Collection, endpoint=f"{db_alias}_collections", **view_kwargs
        ),
        BaseVODSeriesWizardView(
            name="Series Wizard",
            endpoint=f"{db_alias}_series_wizard",
            season_model=models.Season,
            title_model=models.Title,
            episode_model=models.Episode,
            **view_kwargs,
        ),
        BaseVODImportView(
            name="Import Projects", model=models.ImportProject, endpoint=f"{db_alias}_import_view", **view_kwargs
        ),
    )


def init_basevod_admin(admin: Admin, parent_name=None):
    _init_basevod_admin(admin, db_alias="basevod", caption="Base VOD", parent_name=parent_name)

    extra_instances_config = current_app.config.get("BASEVOD_EXTRA_INSTANCES")
    if extra_instances_config:
        for instance_settings in extra_instances_config:
            menu_caption = instance_settings["menu_caption"]
            db_alias = instance_settings["db_alias"]
            _init_basevod_admin(admin, db_alias=db_alias, caption=menu_caption, parent_name=parent_name)
