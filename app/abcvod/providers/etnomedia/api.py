from __future__ import annotations

import logging
from enum import Enum
from typing import (
    Annotated,
    Generator,
    Optional,
    Union,
)

from flask import Flask
from pydantic import (
    BaseModel,
    ConfigDict,
)
from pydantic.functional_validators import BeforeValidator

from app.abcvod.core.api import (
    BaseAPI,
    NotImportant,
    catch_api_error,
)

logger = logging.getLogger(__name__)


# Special type, that allows float to be passed to int fields. This matches behavior of 'int' field from pydantic v1.
forced_int = Annotated[Union[int, float], BeforeValidator(lambda x: int(x))]


class GenreInfo(BaseModel):
    model_config = ConfigDict(coerce_numbers_to_str=True)

    isMain: Optional[bool] = None  # appears only in TitleInfo
    id: str  # 1  actually int, but we need this as str.
    name: str  # "Драма"
    featured: bool | NotImportant = None  # false


class TagInfo(BaseModel):
    title: str


class GenresPage(BaseModel):
    genres: list[GenreInfo]


class BasePage(BaseModel):
    """Base info for endpoints with pagination."""

    currentPage: int
    totalPage: int
    totalCount: int


class BaseInfo(BaseModel):
    model_config = ConfigDict(coerce_numbers_to_str=True)

    id: str  # also known as "remote_id". It is an integer value,  but we convert this to `str for convenience.


class LicenseMixin(BaseModel):
    paidType: Optional[str] = None

    def license_is_valid(self):
        return self.paidType in ("SVOD", "AVOD")


class TitleType(str, Enum):
    movie = "movie"
    series = "series"


class PersonInfo(BaseModel):
    name: str
    image: Optional[str] = None
    role: Optional[str] = None


class TrailerInfo(BaseInfo):
    id: str | NotImportant = None
    image: Optional[str] = None
    duration: forced_int | NotImportant = None
    url: str | NotImportant = None


class PersonsData(BaseModel):
    actors: Optional[list[PersonInfo]] = None
    directors: Optional[list[PersonInfo]] = None
    producers: Optional[list[PersonInfo]] = None
    composers: Optional[list[PersonInfo]] = None
    operators: Optional[list[PersonInfo]] = None
    screenwriters: Optional[list[PersonInfo]] = None


class BriefTitleInfo(LicenseMixin, BaseInfo):
    """Basic info about title, used on pages with info about multiple titles."""

    title: str  # "ewdds"
    type: TitleType  # "movie" or "series"
    ageRestrictions: int  # 6
    badge: Optional[str] = None  # null
    year: int  # 2022
    ratingEtnomedia: Optional[float] = None  # null
    ratingCount: int | NotImportant = None  # 0
    shortDescription: Optional[str] = None  # null
    description: Optional[str] = None  # null
    quote: Optional[str] = None  # null
    duration: forced_int | NotImportant = None  # 0
    views: int | NotImportant = None  # 5
    poster: Optional[str] = None  # null
    bgImage: Optional[str] = None  # null
    logo: Optional[str] = None  # null
    allowedOnWeb: bool | NotImportant = None  # false
    featured: bool | NotImportant = None  # false
    createdAt: str | NotImportant = None  # "2023-04-05T14:13:47.000Z"
    tags: Optional[list[TagInfo]] = None  # [{"title": "2018"}, {"title": "Комедия"}, {"title": "Мелодрама"}]


class EpisodeInfo(LicenseMixin, BaseInfo):
    createdAt: str | NotImportant = None  # "2023-01-13T06:20:00.000Z"
    description: Optional[str] = None  # null
    duration: forced_int | NotImportant = None  # 1293
    image: Optional[str] = None  # null
    isFree: bool | NotImportant = None  # false
    number: int  # 1
    seasonId: str  # 1014  originally int, but used as "remote_id", so we convert it to "str".
    title: str  # "Стимул - 1 серия


class SeasonInfo(LicenseMixin, BaseInfo):
    createdAt: str | NotImportant = None  # "2023-01-13T12:26:13.000Z"
    episodes: list[EpisodeInfo]
    isFree: bool | NotImportant = None  # false
    number: int  # 1
    title: str  # "Стимул - 1 сезон"
    videoId: str  # 20521  originally int, but used as "remote_id", so we convert it to "str".


class TitleInfo(BriefTitleInfo):
    """Full info about certain title."""

    trailer: Optional[TrailerInfo] = None  # null
    persons: PersonsData
    genres: list[GenreInfo]  # {id: 2, name: "Комедия", featured: false}
    seasons: list[SeasonInfo]


class VideosPage(BasePage):
    items: list[BriefTitleInfo]


class EpisodesPage(BasePage):
    items: list[EpisodeInfo]


class EtnomediaAPI(BaseAPI):
    """Class for connection to Etnomedia API."""

    def __init__(self, api_url, api_key, http_retries=5):
        """Init.

        :param api_url: Base URL for API
        :param api_key: access key from Etnomedia
        :param http_retries: Retries count for HTTP requests
        """
        super().__init__(http_retries=http_retries)

        # self.http_session.headers.update({"Content-Type": "application/json"})
        self.api_url = api_url

        self.default_request_params = {"apiKey": api_key}

        # Full description: https://dev.tightvideo.com/issues/80764
        self.genres_url = f"{api_url}/genres"
        self.videos_url = f"{api_url}/videos"
        self.seasons_url = f"{api_url}/seasons"
        self.episodes_url = f"{api_url}/episodes"

    def _get_all_pages(self, url, get_params: dict | None = None) -> Generator[dict, None, None]:
        page_number: int = 0
        total_pages: int = 999
        get_params = get_params.copy() if get_params else {}

        while page_number < total_pages:
            get_params["page"] = page_number + 1
            page_json = self.get_json_response(url=url, get_params=get_params)
            page_number = page_json["currentPage"]
            total_pages = page_json["totalPage"]
            yield page_json

    @catch_api_error
    def get_all_genres(self) -> GenresPage:
        json_response = self.get_json_response(self.genres_url)
        return GenresPage(genres=json_response)

    @catch_api_error
    def get_genre_info(self, remote_id: str) -> GenreInfo:
        return GenreInfo(**self.get_genre_info_json(remote_id))

    @catch_api_error
    def get_title_info(self, remote_id: str) -> TitleInfo:
        title_url = f"{self.videos_url}/{remote_id}"
        response = self.get_json_response(title_url)
        return TitleInfo(**response)

    @catch_api_error
    def get_season_info(self, remote_id: str) -> SeasonInfo:
        return SeasonInfo(**self.get_season_info_json(remote_id))

    @catch_api_error
    def get_episode_info(self, remote_id: str) -> EpisodeInfo:
        return EpisodeInfo(**self.get_episode_info_json(remote_id))

    @catch_api_error
    def get_episodes_info_for_season(self, season_remote_id: str) -> Generator[EpisodeInfo, None, None]:
        pages = self._get_all_pages(self.episodes_url, {"filter[seasonId]": season_remote_id})
        for page in pages:
            episodes_page = EpisodesPage(**page)
            for episode_info in episodes_page.items:
                yield episode_info

    @catch_api_error
    def get_all_titles_info(self) -> Generator[BriefTitleInfo, None, None]:
        """Get info about all titles from Etnomedia API."""
        pages = self._get_all_pages(self.videos_url)
        for page_json in pages:
            page = VideosPage(**page_json)
            for title_info in page.items:
                yield title_info

    @catch_api_error
    def get_genre_info_json(self, remote_id: str) -> dict:
        genre_url = f"{self.genres_url}/{remote_id}"
        return self.get_json_response(genre_url)

    @catch_api_error
    def get_title_info_json(self, remote_id: str) -> dict:
        title_url = f"{self.videos_url}/{remote_id}"
        return self.get_json_response(title_url)

    @catch_api_error
    def get_season_info_json(self, remote_id: str) -> dict:
        season_url = f"{self.seasons_url}/{remote_id}"
        return self.get_json_response(season_url)

    @catch_api_error
    def get_episode_info_json(self, remote_id: str) -> dict:
        episode_url = f"{self.episodes_url}/{remote_id}"
        return self.get_json_response(episode_url)


def init_etnomedia_api(app: Flask):
    """Creates EtnomediaAPI client using app's config and adds it to app's extensions under `etnomedia_api` key."""
    etnomedia_api = EtnomediaAPI(
        api_url=app.config["ETNOMEDIA_API_URL"],
        api_key=app.config["ETNOMEDIA_API_KEY"],
    )

    app.extensions = getattr(app, "extensions", {})
    app.extensions["etnomedia_api"] = etnomedia_api
