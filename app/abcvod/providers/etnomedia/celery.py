from flask import current_app

from app.abcvod.providers.etnomedia.importer import EtnomediaImporter
from app.cms.models import CeleryTaskTracker
from app.cms.tasks.utils import cms_task


@cms_task(task_processor_class=EtnomediaImporter)
def etnomedia_import_task(task_trigger: str, task_tracker: CeleryTaskTracker = None):
    importer = EtnomediaImporter(app=current_app, task_tracker=task_tracker, task_trigger=task_trigger)
    importer.run()
