from flask_mongoengine import Document
from mongoengine import (
    CASCADE,
    PULL,
)
from mongoengine.fields import (
    ListField,
    ReferenceField,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodEpisode,
    AbcvodGenre,
    AbcvodSeason,
    AbcvodTitle,
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.abcvod.core.models.task import AbcvodImportTracker
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)


class EtnomediaDocument(Document):
    meta = {
        "db_alias": "etnomedia",
        "abstract": True,
    }


class EtnomediaGenre(EtnomediaDocument, AbcvodGenre):
    pass


class EtnomediaCategory(EtnomediaDocument, AbcvodCategory):
    genres = ListField(ReferenceField(EtnomediaGenre, reverse_delete_rule=PULL))


class EtnomediaTitle(EtnomediaDocument, AbcvodTitle):
    meta = {
        "indexes": ["categories", "genres"],
    }

    categories = ListField(ReferenceField(EtnomediaCategory, reverse_delete_rule=PULL))
    genres = ListField(ReferenceField(EtnomediaGenre, reverse_delete_rule=PULL))
    collections = ListField(ReferenceField("EtnomediaCollection"))


class EtnomediaSeason(EtnomediaDocument, AbcvodSeason):
    meta = {
        "indexes": ["title"],
    }

    title: EtnomediaTitle = ReferenceField(EtnomediaTitle, reverse_delete_rule=CASCADE)


class EtnomediaEpisode(EtnomediaDocument, AbcvodEpisode):
    meta = {
        "indexes": [
            "title",
            "season",
            "default_caption",
        ],
    }

    title = ReferenceField(EtnomediaTitle, reverse_delete_rule=CASCADE)
    season = ReferenceField(EtnomediaSeason, reverse_delete_rule=CASCADE)


class EtnomediaImportTracker(AbcvodImportTracker):
    job_title = "Etnomedia VOD content import"
    job_id = "etnomedia-import"


class EtnomediaTitleReference(AbcvodTitleReference):
    title = ReferenceField(EtnomediaTitle)


class EtnomediaCollection(EtnomediaDocument, AbcvodCollection):
    titles = TitlesField(EtnomediaTitleReference)
    titles_excluded = TitlesExcludedField(EtnomediaTitleReference)
    categories = ReferenceCriteriaField(EtnomediaTitle.categories)
    genres = ReferenceCriteriaField(EtnomediaTitle.genres)
    imdb_rating = RatingCriteriaField(EtnomediaTitle.imdb_rating)
    kp_rating = RatingCriteriaField(EtnomediaTitle.kp_rating)


EtnomediaCollection.register_delete_rule(EtnomediaTitle, "collections", PULL)
