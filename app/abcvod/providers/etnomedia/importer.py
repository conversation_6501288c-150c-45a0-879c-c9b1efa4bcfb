from __future__ import annotations

import logging

from flask import Flask
from mongoengine import DoesNotExist
from slugify import slugify

from app.abcvod.core.exceptions import ImportException
from app.abcvod.core.importer import (
    DoNotAutoupdateMixin,
    ImporterWithImages,
)
from app.abcvod.core.models.abcvod import (
    AbcvodCountry,
    AbcvodPerson,
)

from .api import (
    EpisodeInfo,
    EtnomediaAPI,
    GenreInfo,
    PersonInfo,
    SeasonInfo,
    TitleInfo,
    TitleType,
)
from .models import (
    EtnomediaCategory,
    EtnomediaCollection,
    EtnomediaEpisode,
    EtnomediaGenre,
    EtnomediaImportTracker,
    EtnomediaSeason,
    EtnomediaTitle,
)

logger = logging.getLogger(__name__)


class EtnomediaImporter(DoNotAutoupdateMixin, ImporterWithImages):
    """Class for importing Etnomedia content."""

    task_tracker_model = EtnomediaImportTracker

    # Constant categories, should be setup before all other imports.
    MOVIES_CATEGORY: EtnomediaCategory
    SERIES_CATEGORY: EtnomediaCategory

    title_model = EtnomediaTitle
    collection_model = EtnomediaCollection

    def __init__(self, app: Flask, task_tracker: EtnomediaImportTracker | None = None, task_trigger: str | None = None):
        max_workers = app.config["ETNOMEDIA_MAX_WORKERS"]
        super().__init__(
            task_trigger=task_trigger,
            task_tracker=task_tracker,
            max_workers=max_workers,
            storage=app.extensions["storage"],
        )

        self.etnomedia_api: EtnomediaAPI = app.extensions["etnomedia_api"]
        self.images_path = app.config["ETNOMEDIA_STORAGE_FOLDER"]

        # Create cache for persons and countries
        self.persons_cache = {person.name: person for person in AbcvodPerson.objects.all()}
        self.countries_cache = {country.iso2: country for country in AbcvodCountry.objects.all()}
        self.genres_cache: dict[str, EtnomediaGenre] = {}  # {"genre_remote_id": EtnomediaGenre}
        self.titles_cache: dict[str, EtnomediaTitle] = {}  # {"title_remote_id": EtnomediaTitle}
        self.series_cache: dict[str, EtnomediaTitle] = {}  # {"series_remote_id": EtnomediaTitle}
        self.seasons_cache: dict[str, EtnomediaSeason] = {}  # {"season_remote_id": EtnomediaSeason}
        self.delayed_seasons: list[SeasonInfo] = []
        self.delayed_episodes: list[EpisodeInfo] = []

    def _setup_genres_cache(self):
        genres = EtnomediaGenre.objects.filter(is_published=True)
        for genre in genres:
            genre: EtnomediaGenre
            self.genres_cache[genre.remote_id] = genre

    def import_procedure(self):
        self.setup_categories()
        self.import_genres()
        self.import_titles()
        self.import_seasons()
        self.import_episodes()

    def after_import(self):
        super(EtnomediaImporter, self).after_import()
        self.mark_unpublished(
            {
                "Genres": EtnomediaGenre,
                "Titles": EtnomediaTitle,
                "Seasons": EtnomediaSeason,
                "Episodes": EtnomediaEpisode,
            },
        )

    def _upsert_category(self, caption: str, slug: str) -> EtnomediaCategory:
        try:
            category = EtnomediaCategory.objects.get(slug=slug)
        except DoesNotExist:
            category = EtnomediaCategory(slug=slug, created_during_import=self.uid)
        if category.do_not_autoupdate:
            return category
        category.caption = caption
        category.is_published = True
        category.updated_during_import = self.uid
        return category.save()

    def _upsert_genre(self, genre_info: GenreInfo) -> EtnomediaGenre:
        try:
            genre = EtnomediaGenre.objects.get(remote_id=genre_info.id)
        except DoesNotExist:
            genre = EtnomediaGenre(remote_id=genre_info.id, created_during_import=self.uid)

        genre.is_published = True
        genre.updated_during_import = self.uid
        if not self.can_update(genre):
            return genre.save()

        genre.caption = genre_info.name
        genre.slug = slugify(genre_info.name)
        return genre.save()

    def _upsert_title(self, title_info: TitleInfo) -> EtnomediaTitle:
        try:
            title = EtnomediaTitle.objects.get(remote_id=title_info.id)
        except DoesNotExist:
            title = EtnomediaTitle(remote_id=title_info.id, created_during_import=self.uid)

        title.is_published = True
        title.updated_during_import = self.uid
        if not self.can_update(title):
            return title.save()

        title.caption = title_info.title
        title.description = title_info.description or ""
        title.is_series = title_info.type == TitleType.series
        title.years = [title_info.year]
        try:
            title.genres = [self.genres_cache[genre_info.id] for genre_info in title_info.genres]
        except KeyError as e:
            raise ImportException(f"Missing genre in database: {e}")

        title.slug = f"{slugify(title_info.title)}-{title_info.id}"
        title.actors = self.get_persons(title_info.persons.actors)
        title.directors = self.get_persons(title_info.persons.directors)
        title.categories = [self.SERIES_CATEGORY] if title.is_series else [self.MOVIES_CATEGORY]
        title.age_rating = title_info.ageRestrictions

        # Prepare to download images. At the current moment it is safe to assume, that images will be ".jpg".
        storage_dir = f"{self.images_path}/titles/{title.remote_id}/"
        # Download poster.
        if title_info.poster:
            title.poster = self.delay_download_image(storage_dir, "poster.jpg", title_info.poster)
        else:
            title_info.poster = ""
        # Download bgImage (also used by episodes).
        if title_info.bgImage:
            title.poster_background = self.delay_download_image(storage_dir, "bg_image.jpg", title_info.bgImage)
        else:
            title.poster_background = ""

        return title.save()

    def _upsert_season(self, season_info: SeasonInfo) -> EtnomediaSeason:
        try:
            season = EtnomediaSeason.objects.get(remote_id=season_info.id)
        except DoesNotExist:
            season = EtnomediaSeason(remote_id=season_info.id, created_during_import=self.uid)

        season.is_published = True
        season.updated_during_import = self.uid
        if not self.can_update(season):
            return season.save()

        season.title = self.series_cache[season_info.videoId]
        season.number = season_info.number
        season.slug = f"{season.title.slug}-season-{season.number}"
        season.caption = season_info.title
        return season.save()

    def _upsert_episode(self, episode_info: EpisodeInfo) -> EtnomediaEpisode:
        try:
            episode = EtnomediaEpisode.objects.get(remote_id=episode_info.id)
        except DoesNotExist:
            episode = EtnomediaEpisode(remote_id=episode_info.id, created_during_import=self.uid)

        episode.is_published = True
        episode.updated_during_import = self.uid
        if not self.can_update(episode):
            return episode.save()

        episode.season = self.seasons_cache[episode_info.seasonId]
        episode.title = episode.season.title
        episode.number = episode_info.number
        episode.slug = f"{episode.season.slug}-episode-{episode.number}"
        episode.caption = episode_info.title
        episode.description = episode_info.description or ""
        episode.poster = episode.title.poster_background
        return episode.save()

    def import_genre(self, genre_info: GenreInfo):
        genre = self._upsert_genre(genre_info)
        self.genres_cache[genre_info.id] = genre

    def import_title(self, remote_id: str):
        title_info: TitleInfo = self.etnomedia_api.get_title_info(remote_id)
        title: EtnomediaTitle = self._upsert_title(title_info)
        self.titles_cache[title.remote_id] = title
        if title.is_series:
            self.series_cache[title.remote_id] = title
            for season_info in title_info.seasons:
                self._delay_import_seasons_and_episodes(season_info)

    def import_season(self, season_info: SeasonInfo):
        season = self._upsert_season(season_info)
        self.seasons_cache[season.remote_id] = season

    def import_episode(self, episode_info: EpisodeInfo):
        self._upsert_episode(episode_info)

    def setup_categories(self):
        self.MOVIES_CATEGORY = self._upsert_category("movies", "Фильмы")
        self.SERIES_CATEGORY = self._upsert_category("series", "Сериалы")

    def import_genres(self):
        result = self.etnomedia_api.get_all_genres()
        self._setup_genres_cache()
        for genre_info in result.genres:
            self.import_genre(genre_info)

    def import_titles(self):
        title_remote_ids = (
            brief_title_info.id
            for brief_title_info in self.etnomedia_api.get_all_titles_info()
            if brief_title_info.license_is_valid()
        )
        with self.bunch_of_tasks() as submit:
            for remote_id in title_remote_ids:
                submit(self.import_title, remote_id)

    def import_seasons(self):
        with self.bunch_of_tasks() as submit:
            for season_info in self.delayed_seasons:
                submit(self.import_season, season_info)

    def import_episodes(self):
        with self.bunch_of_tasks() as submit:
            for episode_info in self.delayed_episodes:
                submit(self.import_episode, episode_info)

    def get_or_create_person(self, person_info: PersonInfo) -> AbcvodPerson:
        """Get person from existing cache or create new AbcvodPerson."""
        if person_info.name in self.persons_cache:
            return self.persons_cache[person_info.name]
        with self.lock:
            person = AbcvodPerson.get_or_create_person(name=person_info.name)
            self.persons_cache[person_info.name] = person
            return person

    def get_persons(self, person_info_list: list[PersonInfo] | None) -> list[AbcvodPerson]:
        if not person_info_list:
            return []
        return [self.get_or_create_person(person_info) for person_info in person_info_list]

    def _delay_import_seasons_and_episodes(self, season_info: SeasonInfo):
        if not season_info.license_is_valid():
            return
        self.delayed_seasons.append(season_info)
        for episode_info in season_info.episodes:
            if episode_info.license_is_valid():
                self.delayed_episodes.append(episode_info)
