from flask import current_app
from flask_admin import Admin

from app.abcvod.core.views import (
    AbcVodCategoryView,
    AbcVodCollectionView,
    AbcVodEpisodeView,
    AbcVodGenreView,
    AbcvodImportTaskView,
    AbcVodSeasonView,
    AbcVodTitleView,
    AbcVodViewMixin,
    GetJsonFromApiMixin,
    ReimportMixin,
    init_vod_admin,
)
from app.abcvod.providers.etnomedia.api import EtnomediaAPI
from app.abcvod.providers.etnomedia.celery import etnomedia_import_task
from app.abcvod.providers.etnomedia.importer import EtnomediaImporter
from app.abcvod.providers.etnomedia.models import (
    EtnomediaCategory,
    EtnomediaCollection,
    EtnomediaEpisode,
    EtnomediaGenre,
    EtnomediaImportTracker,
    EtnomediaSeason,
    EtnomediaTitle,
)
from app.cms.models import TaskTrigger
from app.cms.tasks.utils import ReimportException


class EtnomediaMixin(AbcVodViewMixin):
    vod_name = "etnomedia"
    genre_class = EtnomediaGenre
    category_class = EtnomediaCategory
    title_class = EtnomediaTitle
    season_class = EtnomediaSeason
    episode_class = EtnomediaEpisode
    collection_class = EtnomediaCollection

    @property
    def api(self) -> EtnomediaAPI:
        return current_app.extensions["etnomedia_api"]


class EtnomediaReimportMixin(ReimportMixin):
    def get_importer(self) -> EtnomediaImporter:
        importer = EtnomediaImporter(app=current_app, task_trigger=TaskTrigger.MANUAL)
        importer.reraise_all_log_errors = True
        return importer


class EtnomediaTitleView(EtnomediaMixin, EtnomediaReimportMixin, GetJsonFromApiMixin, AbcVodTitleView):
    model = EtnomediaTitle

    def get_api_response(self, document):
        title: EtnomediaTitle = document
        title_json = self.api.get_title_info_json(title.remote_id)
        return title_json or f"This title with remote_id {title.remote_id} was not found in api :("

    def reimport_procedure(self, document):
        title: EtnomediaTitle = document
        title_info = self.api.get_title_info(remote_id=title.remote_id)
        if not title_info.license_is_valid():
            raise ReimportException("License restrictions. 'paidType' field value is not 'AVOD' or 'SVOD'.")
        importer = self.get_importer()
        with importer:
            importer.results["Reimport title with id"] = title.remote_id
            importer._setup_genres_cache()
            importer.setup_categories()
            with importer.ignore_do_not_autoupdate:
                importer.import_title(remote_id=title.remote_id)
            importer.import_seasons()
            importer.import_episodes()
            importer.download_all_images()


class EtnomediaCategoryView(EtnomediaMixin, AbcVodCategoryView):
    model = EtnomediaCategory


class EtnomediaGenreView(EtnomediaMixin, EtnomediaReimportMixin, GetJsonFromApiMixin, AbcVodGenreView):
    model = EtnomediaGenre

    def get_api_response(self, document):
        genre: EtnomediaGenre = document
        genre_json = self.api.get_genre_info_json(genre.remote_id)
        return genre_json or f"Genre with remote_id '{genre.remote_id}' was not found in api :("

    def reimport_procedure(self, document):
        genre: EtnomediaGenre = document
        importer = self.get_importer()
        with importer:
            importer.results["Reimport genre with id"] = genre.remote_id
            genre_info = self.api.get_genre_info(remote_id=genre.remote_id)
            with importer.ignore_do_not_autoupdate:
                importer.import_genre(genre_info)


class EtnomediaSeasonView(EtnomediaMixin, EtnomediaReimportMixin, GetJsonFromApiMixin, AbcVodSeasonView):
    model = EtnomediaSeason

    def reimport_procedure(self, document):
        season: EtnomediaSeason = document
        season_info = self.api.get_season_info(season.remote_id)
        if not season_info.license_is_valid():
            raise ReimportException("License restrictions: 'paidType' field value is not 'AVOD' or 'SVOD'.")

        importer = self.get_importer()
        with importer:
            importer.results["Reimport season with id"] = season.remote_id
            importer.series_cache[season.title.remote_id] = season.title
            with importer.ignore_do_not_autoupdate:
                importer.import_season(season_info)
            for episode_info in self.api.get_episodes_info_for_season(season.remote_id):
                if episode_info.license_is_valid():
                    importer.import_episode(episode_info)

    def get_api_response(self, document):
        season: EtnomediaSeason = document
        season_json = self.api.get_season_info_json(season.remote_id)
        return season_json or f"Season with remote_id '{season.remote_id}' was not found in api :("


class EtnomediaEpisodeView(EtnomediaMixin, EtnomediaReimportMixin, GetJsonFromApiMixin, AbcVodEpisodeView):
    model = EtnomediaEpisode

    def get_api_response(self, document):
        episode: EtnomediaEpisode = document
        episode_json = self.api.get_episode_info_json(episode.remote_id)
        return episode_json or f"Episode with remote_id '{episode.remote_id}' was not found in api :("

    def reimport_procedure(self, document):
        episode: EtnomediaEpisode = document
        episode_info = self.api.get_episode_info(episode.remote_id)
        if not episode_info.license_is_valid():
            raise ReimportException("License restrictions. 'paidType' field value is not 'AVOD' or 'SVOD'.")
        importer = self.get_importer()
        with importer:
            importer.results["Reimport episode with id"] = episode.remote_id
            importer.seasons_cache[episode.season.remote_id] = episode.season
            with importer.ignore_do_not_autoupdate:
                importer.import_episode(episode_info)


class EtnomediaCollectionView(EtnomediaMixin, AbcVodCollectionView):
    model = EtnomediaCollection
    can_create = True
    can_edit = True


class EtnomediaImportTaskView(EtnomediaMixin, AbcvodImportTaskView):
    model = EtnomediaImportTracker
    celery_task = etnomedia_import_task
    can_delete = True


def init_etnomedia_admin(admin: Admin, caption="Etnomedia", parent_name=None):
    init_vod_admin(
        admin,
        caption,
        vod_mixin=EtnomediaMixin,
        category_view=EtnomediaCategoryView,
        genre_view=EtnomediaGenreView,
        title_view=EtnomediaTitleView,
        season_view=EtnomediaSeasonView,
        episode_view=EtnomediaEpisodeView,
        collection_view=EtnomediaCollectionView,
        parent_name=parent_name,
    )
    vod_name = EtnomediaMixin.vod_name
    admin.add_view(EtnomediaImportTaskView(name="Import tasks", category=caption, endpoint=f"{vod_name}_tasks"))
