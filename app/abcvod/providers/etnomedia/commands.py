from flask import current_app
from flask.cli import AppGroup

from app.abcvod.providers.etnomedia.importer import EtnomediaImporter
from app.cms.models import TaskTrigger

etnomedia_commands = AppGroup("etnomedia")


@etnomedia_commands.command("import")
def etnomedia_import():
    importer = EtnomediaImporter(app=current_app, task_trigger=TaskTrigger.CLI)
    importer.skip_download_images = True
    importer.run()
