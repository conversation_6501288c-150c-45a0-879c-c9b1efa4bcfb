from mongoengine import (
    PULL,
    Document,
    ListField,
    ReferenceField,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodEpisode,
    AbcvodGenre,
    AbcvodSeason,
    AbcvodTitle,
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.abcvod.core.models.task import AbcvodImportTracker
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)


class WinkMixin(Document):
    meta = {
        "db_alias": "wink",
        "abstract": True,
        "strict": False,
    }


class WinkImportTracker(AbcvodImportTracker):
    job_title = "Wink VOD content import"
    job_id = "wink-import"


class WinkGenre(WinkMixin, AbcvodGenre):
    meta = {"collection": "more_tv_genre"}


class WinkCategory(WinkMixin, AbcvodCategory):
    meta = {"collection": "more_tv_category"}

    genres = ListField(ReferenceField(WinkGenre, reverse_delete_rule=PULL))


class WinkTitle(WinkMixin, AbcvodTitle):
    meta = {"collection": "more_tv_title"}

    genres = ListField(ReferenceField(WinkGenre, reverse_delete_rule=PULL))
    collections = ListField(ReferenceField("WinkCollection"))
    categories = ListField(ReferenceField(WinkCategory, reverse_delete_rule=PULL))


class WinkSeason(WinkMixin, AbcvodSeason):
    meta = {"collection": "more_tv_season"}

    title = ReferenceField(WinkTitle)


class WinkEpisode(WinkMixin, AbcvodEpisode):
    meta = {"collection": "more_tv_episode"}

    title = ReferenceField(WinkTitle)
    season = ReferenceField(WinkSeason)


class WinkTitleReference(AbcvodTitleReference):
    title = ReferenceField(WinkTitle)


class WinkCollection(WinkMixin, AbcvodCollection):
    meta = {"collection": "more_tv_collection"}

    titles = TitlesField(WinkTitleReference)
    titles_excluded = TitlesExcludedField(WinkTitleReference)
    categories = ReferenceCriteriaField(WinkTitle.categories)
    genres = ReferenceCriteriaField(WinkTitle.genres)
    imdb_rating = RatingCriteriaField(WinkTitle.imdb_rating)
    kp_rating = RatingCriteriaField(WinkTitle.kp_rating)


WinkCollection.register_delete_rule(WinkTitle, "collections", PULL)
