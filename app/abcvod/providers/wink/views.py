from flask import (
    current_app,
    request,
)
from flask_admin import Admin

from app.abcvod.core.views import (
    AbcVodCategoryView,
    AbcVodCollectionView,
    AbcVodEpisodeView,
    AbcVodGenreView,
    AbcvodImportTaskView,
    AbcVodSeasonView,
    AbcVodTitleView,
    AbcVodViewMixin,
    GetJsonFromApiMixin,
    ReimportMixin,
    init_vod_admin,
)
from app.abcvod.providers.wink.api import WinkAPI
from app.abcvod.providers.wink.celery import wink_import_task
from app.abcvod.providers.wink.importer import WinkImporter
from app.abcvod.providers.wink.models import (
    WinkCategory,
    WinkCollection,
    WinkEpisode,
    WinkGenre,
    WinkImportTracker,
    WinkSeason,
    WinkTitle,
)
from app.cms.models import TaskTrigger


class WinkMixin(AbcVodViewMixin):
    vod_name = "wink"
    genre_class = WinkGenre
    category_class = WinkCategory
    title_class = WinkTitle
    season_class = WinkSeason
    episode_class = WinkEpisode
    collection_class = WinkCollection

    @property
    def api(self) -> WinkAPI:
        return current_app.extensions["wink_api"]


class WinkTitleView(WinkMixin, ReimportMixin, GetJsonFromApiMixin, AbcVodTitleView):
    def get_api_response(self, document):
        title: WinkTitle = document
        return self.api.get_title_info_json(title.remote_id)

    def reimport_procedure(self, document):
        document: WinkTitle
        importer = WinkImporter(current_app, task_trigger=TaskTrigger.MANUAL)
        with importer:
            importer.results["Reimport title with id"] = document.remote_id
            importer.reraise_all_log_errors = True
            importer.setup_categories()
            title_info = self.api.get_title_info(document.remote_id)
            with importer.ignore_do_not_autoupdate:
                title: WinkTitle = importer.import_title(title_info)
            if title.is_series:
                importer.import_episodes_for_title(title)


class WinkCategoryView(WinkMixin, AbcVodCategoryView):
    pass


class WinkGenreView(WinkMixin, AbcVodGenreView):
    pass


class WinkSeasonView(WinkMixin, GetJsonFromApiMixin, AbcVodSeasonView):
    def get_api_response(self, document):
        season: WinkSeason = document
        return self.api.get_seasons_info(season.title.remote_id)


class WinkEpisodeView(WinkMixin, GetJsonFromApiMixin, AbcVodEpisodeView):
    extra_json_api_args = {
        "page": 1,
    }

    def get_api_response(self, document):
        page_number = request.args.get("page", 1)
        episode: WinkEpisode = document
        title_remote_id = episode.season.title.remote_id
        return self.api.get_title_episodes_page(title_remote_id, page_number)


class WinkCollectionView(WinkMixin, AbcVodCollectionView):
    model = WinkCollection


class WinkImportTaskView(AbcvodImportTaskView):
    model = WinkImportTracker
    celery_task = wink_import_task


def init_wink_admin(admin: Admin, caption="Wink", parent_name=None):
    init_vod_admin(
        admin,
        caption,
        vod_mixin=WinkMixin,
        category_view=WinkCategoryView,
        genre_view=WinkGenreView,
        title_view=WinkTitleView,
        season_view=WinkSeasonView,
        episode_view=WinkEpisodeView,
        collection_view=WinkCollectionView,
        parent_name=parent_name,
    )
    vod_name = WinkMixin.vod_name
    admin.add_view(WinkImportTaskView(name="Import tasks", category=caption, endpoint=f"{vod_name}_tasks"))
