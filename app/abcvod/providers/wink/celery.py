from flask import current_app

from app.abcvod.providers.wink.importer import WinkImporter
from app.cms.models import CeleryTaskTracker
from app.cms.tasks.utils import cms_task


@cms_task(task_processor_class=WinkImporter)
def wink_import_task(task_trigger: str, task_tracker: CeleryTaskTracker = None):
    importer = WinkImporter(app=current_app, task_trigger=task_trigger, task_tracker=task_tracker)
    importer.run()
