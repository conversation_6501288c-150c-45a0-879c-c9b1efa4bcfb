from __future__ import annotations

import logging
from datetime import datetime
from functools import lru_cache

from flask import Flask
from mongoengine import DoesNotExist
from slugify import slugify

from app.abcvod.core.importer import (
    BaseImporter,
    DoNotAutoupdateMixin,
)
from app.abcvod.core.models.abcvod import (
    AbcvodCountry,
    AbcvodPerson,
)
from app.abcvod.providers.wink.api import (
    EpisodeInfo,
    PersonInfo,
    TitleInfo,
    TitlesPage,
    WinkAPI,
)
from app.abcvod.providers.wink.models import (
    WinkCategory,
    WinkCollection,
    WinkEpisode,
    WinkGenre,
    WinkImportTracker,
    WinkSeason,
    WinkTitle,
)
from cmf.core.fields import MultilangString

logger = logging.getLogger(__name__)


class WinkImporter(DoNotAutoupdateMixin, BaseImporter):
    task_tracker_model = WinkImportTracker

    title_model = WinkTitle
    collection_model = WinkCollection

    # 73567 list of genre slugs, which are accepted by "For kids" profile.
    MOVIE_GENRES_FOR_KIDS = {"multfilm"}
    SERIES_GENRES_FOR_KIDS = {"semeinyi", "multfilm"}

    def __init__(self, app: Flask, task_tracker: WinkImportTracker | None = None, task_trigger: str | None = None):
        max_workers = app.config["WINK_MAX_WORKERS"]
        super().__init__(max_workers=max_workers, task_tracker=task_tracker, task_trigger=task_trigger)

        self.wink_api: WinkAPI = app.extensions["wink_api"]
        # info for queued import
        self.imported_titles: list[WinkTitle] = []
        # Populate countries and persons cache
        self.countries_cache = {country.name: country for country in AbcvodCountry.objects.all()}
        persons = AbcvodPerson.objects.filter(wink_id__exists=True)
        self.persons_cache = {person.wink_id: person for person in persons}

    def import_procedure(self):
        self.task_tracker.add_log_message("Setup categories...")
        self.setup_categories()
        self.task_tracker.add_log_message("Import titles...")
        self.import_titles()
        self.task_tracker.add_log_message(f"Import episodes for {len(self.imported_titles)} imported titles...")
        self.import_episodes()
        self.task_tracker.add_log_message("Update genres in categories...")
        self.update_genres_in_categories()

    def after_import(self):
        super(WinkImporter, self).after_import()
        self.mark_unpublished(
            {
                "Titles": WinkTitle,
                "Seasons": WinkSeason,
                "Episodes": WinkEpisode,
                "Genres": WinkGenre,
            },
        )

    @lru_cache
    def get_or_create_genre(self, remote_id: str, caption: str):
        try:
            genre = WinkGenre.objects.get(remote_id=remote_id)
        except DoesNotExist:
            genre = WinkGenre(remote_id=remote_id, created_during_import=self.uid)

        genre.is_published = True
        genre.updated_during_import = self.uid
        if not self.can_update(genre):
            return genre.save()

        genre.caption = caption
        genre.slug = slugify(caption)
        return genre.save()

    def import_titles_page(self, titles_page: TitlesPage):
        for brief_title_info in titles_page.data:
            title = self.import_title(brief_title_info)
            self.imported_titles.append(title)

    def import_titles(self):
        with self.bunch_of_tasks() as submit:
            for page_number in range(1, 100000):  # Intended oversize, we will never hit 100000.
                titles_page = self.wink_api.get_titles_page(page_number=page_number)
                has_more_pages = titles_page.meta.pagination.has_more_pages
                submit(self.import_titles_page, titles_page=titles_page)
                if not has_more_pages:
                    break

    def import_title(self, title_info: TitleInfo) -> WinkTitle:
        remote_id = title_info.id
        try:
            title = WinkTitle.objects.get(remote_id=remote_id)
        except DoesNotExist:
            title = WinkTitle(
                remote_id=remote_id,
                created_during_import=self.uid,
            )

        title.is_published = self._get_title_is_published(title_info)
        title.updated_during_import = self.uid
        if not self.can_update(title):
            return title.save()

        title.caption = title_info.title
        title.description = title_info.description
        slug = title_info.slug
        title.slug = f"{remote_id}-{slug}"
        title.is_series = title_info.is_serial
        title.kp_rating = title_info.rating_kp
        title.imdb_rating = title_info.rating_imdb
        title.age_rating = title_info.age
        title.years = [title_info.year]
        title.release_date = title_info.published_at

        title.genres = []
        for genre_info in title_info.genres:
            with self.lock:
                genre = self.get_or_create_genre(genre_info.id, genre_info.name)
            title.genres.append(genre)

        if title.is_series:
            title.categories = [self.series_category]
            self.series_genres.update(title.genres)
        else:
            title.categories = [self.movies_category]
            self.movies_genres.update(title.genres)

        # setup "for_kids"
        title.for_kids = False
        if title.age_rating <= 12:
            for genre in title.genres:
                genre: WinkGenre
                if (title.is_series and genre.slug in self.SERIES_GENRES_FOR_KIDS) or (
                    title.is_movie and genre.slug in self.MOVIE_GENRES_FOR_KIDS
                ):
                    title.for_kids = True
                    break

        title.countries = []
        for country_info in title_info.countries:
            country: AbcvodCountry = self.get_country(country_info.name, title.remote_id)
            if country:
                title.countries.append(country)
        title.actors = self.get_persons(title_info.actors)
        title.directors = self.get_persons(title_info.directors)

        try:
            poster_url = title_info.poster.url_680x900
        except (KeyError, TypeError):
            logger.warning(f"Not found image for title {remote_id}, title_info['poster'] = {title_info['poster']}")
            poster_url = None
        title.poster = poster_url
        return title.save()

    def get_or_create_season(
        self, title: WinkTitle, season_number: int, season_caption: str | None
    ) -> WinkSeason | None:
        with self.lock:
            with self.log_import_error(custom_message=f"{season_number=} {season_caption=}"):
                return self._get_or_create_season(title, season_number, season_caption)

    @lru_cache
    def _get_or_create_season(self, title: WinkTitle, season_number: int, season_caption: str | None):
        slug = f"{title.slug}-season-{season_number}"
        try:
            season: WinkSeason = WinkSeason.objects.get(slug=slug)
        except DoesNotExist:
            season = WinkSeason(
                slug=slug,
                created_during_import=self.uid,
            )

        season.is_published = True
        season.updated_during_import = self.uid
        if not self.can_update(season):
            return season.save()

        season.title = title
        season.number = season_number
        season.caption = MultilangString(season_caption or f"Сезон {season_number}")
        return season.save()

    def import_episodes_for_title(self, title: WinkTitle):
        title_remote_id = title.remote_id
        for page_number in range(1, 100):  # intentionally oversized range
            episodes_page = self.wink_api.get_title_episodes_page(title_remote_id, page_number=page_number)
            for episode_info in episodes_page.data:
                self.import_episode(episode_info, title)
            if not episodes_page.meta.pagination.has_more_pages:
                break

    def import_episode(self, episode_info: EpisodeInfo, title: WinkTitle):
        remote_id = episode_info.id
        try:
            episode = WinkEpisode.objects.get(remote_id=remote_id)
        except DoesNotExist:
            episode = WinkEpisode(
                remote_id=remote_id,
                created_during_import=self.uid,
            )

        episode.is_published = True
        episode.updated_during_import = self.uid
        if not self.can_update(episode):
            return episode.save()

        season_number = episode_info.serial_season
        season_caption = episode_info.serial_season_name
        episode_number = episode_info.serial_episode
        if season_number is None or episode_number is None:
            # This fields may be missing, it happens. We skip episodes without season number or episode number.
            # There also may be season_number == 0 and episode_number == 0, so we need explicit check "is None".
            return
        season = self.get_or_create_season(title, season_number=season_number, season_caption=season_caption)
        if not season:
            self.add_import_error("Import episode error", f"No season can be created for episode #{episode_number}")
            return

        episode.title = title
        episode.season = season
        episode.number = episode_number
        episode.caption = episode_info.serial_episode_name or f"Эпизод {episode.number}"
        episode.slug = f"{season.slug}-episode-{episode.number}-{remote_id}"
        poster_url = episode_info.image.url_340x192
        episode.poster = poster_url
        episode.release_date = episode_info.published_at
        episode.save()

    def import_episodes(self):
        with self.bunch_of_tasks() as submit:
            for title in self.imported_titles:
                submit(self.import_episodes_for_title, title=title)

    # noinspection PyAttributeOutsideInit
    def setup_categories(self):
        """Create default categories.

        Wink has no own categories, so we create default ones.
        """
        self.movies_category = self.get_or_create_category(caption="Фильмы", slug="movies", priority=1)
        self.series_category = self.get_or_create_category(caption="Сериалы", slug="series", priority=2)
        # Category genres come from titles, which belong to category.
        self.movies_genres = set()
        self.series_genres = set()

    def update_genres_in_categories(self):
        self.movies_category.genres = list(self.movies_genres)
        self.movies_category.save()
        self.series_category.genres = list(self.series_genres)
        self.series_category.save()

    def get_or_create_category(self, caption, slug, priority) -> WinkCategory:
        try:
            category = WinkCategory.objects.get(slug=slug)
        except DoesNotExist:
            category = WinkCategory(
                slug=slug,
                created_during_import=self.uid,
            )

        category.is_published = True
        category.updated_during_import = self.uid
        if not self.can_update(category):
            return category.save()

        category.caption = caption
        category.priority = priority
        return category.save()

    def get_country(self, country_name, title_remote_id) -> AbcvodCountry | None:
        try:
            return self.countries_cache[country_name]
        except KeyError:
            self.add_import_error("Unknown country", f"{title_remote_id=}, '{country_name=}'")

    def get_persons(self, persons_info: list[PersonInfo]) -> list[AbcvodPerson]:
        """Shortcut."""
        return [self.get_or_create_person(person_info) for person_info in persons_info]

    def get_or_create_person(self, person_info: PersonInfo):
        """Get existing or create new person, using person info from API."""
        person_id = person_info.id
        if person_id not in self.persons_cache:
            with self.lock:
                person = AbcvodPerson.get_or_create_person(name=person_info.name, wink_id=person_id)
                self.persons_cache[person_id] = person
        return self.persons_cache[person_id]

    def _get_title_is_published(self, title_info: TitleInfo) -> bool:
        available_at = title_info.available_at
        title_id = title_info.id
        if not available_at:
            return True
        if available_at > datetime.now():
            self.add_log(f"Title id '{title_id}' has '{available_at=}' (future date) and will not be published.")
            return False
        return True
