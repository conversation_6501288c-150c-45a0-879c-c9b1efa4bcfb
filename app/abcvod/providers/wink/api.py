from __future__ import annotations

from datetime import datetime
from typing import Optional
from urllib.parse import urljoin

from flask import Flask
from pydantic import (
    BaseModel,
    ConfigDict,
)

from app.abcvod.core.api import (
    BaseAPI,
    NotImportant,
)


class PaginationInfo(BaseModel):
    total: int  # 614
    per_page: int  # 50
    current_page: int  # 1
    has_more_pages: bool  # True
    next_page_url: Optional[str]  # 'https://api.allplay.uz/api/v1/movies?per_page=50&category=moretv&embed_wm=1&page=2'


class WinkModel(BaseModel):
    model_config = ConfigDict(coerce_numbers_to_str=True)

    id: str  # also known as `remote_id`. It is an integer value, but we convert this to `str` for convenience.


class PageMeta(BaseModel):
    pagination: PaginationInfo
    t_ms: float  # 317.8138732910156


class GenreInfo(WinkModel):
    localized_name: str  # 'Комедия'
    name: str  # 'Комедия'


class CountryInfo(WinkModel):
    localized_name: str  # 'Россия'
    name: str  # 'Россия'


class TitlePosterInfo(BaseModel):
    url_100x100: str | NotImportant  # 'https://i.allmovies.uz/i/1368332/eyJ3IjoxMDAsImgiOjEwMCwiYyI6dHJ1ZSwid20iOlsid2luayJdfQ/image.jpg?t=T-jOREjJqL7lAqwenIhHgQ',  # noqa
    url_340x450: str | NotImportant  # 'https://i.allmovies.uz/i/1368332/eyJ3IjozNDAsImgiOjQ1MCwiYyI6dHJ1ZSwid20iOlsid2luayJdfQ/image.jpg?t=lVKN6uOBtTnS53YtRRwSrw',  # noqa
    url_680x900: str  # 'https://i.allmovies.uz/i/1368332/eyJ3Ijo2ODAsImgiOjkwMCwiYyI6dHJ1ZSwid20iOlsid2luayJdfQ/image.jpg?t=lU7fev9H6n3dX4fltf09gQ'  # noqa


class EpisodePosterInfo(BaseModel):
    url_340x192: str  # 'https://i.allmovies.uz/i/1368343/eyJ3IjozNDAsImgiOjE5MiwiYyI6dHJ1ZX0/image.jpg?t=iYEMrA_l5uRjfHwAEc0qrg'  # noqa


class PersonInfo(WinkModel):
    localized_name: str | NotImportant  # 'Павел Чинарёв'
    name: str  # 'Павел Чинарёв'
    pivot: dict | NotImportant  # {'description': None, 'movie_id': 67138, 'num': 0, 'person_id': 59175}
    used_count: int | NotImportant  # 29


class TitleInfo(WinkModel):
    category_id: int  # 10
    title: str  # 'Домохозяин'
    description: str  # 'Опер Антон Шаталин из Семиморска погряз в работе и перестал уделять внимание семье...'
    budget: Optional[str]  # '$21 000 000'
    slogan: Optional[str]  # None
    year: int  # 2025
    quality: str  # 'fullhd'
    is_3d: bool  # False
    is_serial: bool  # True
    is_cartoon: bool  # False
    file_count: int  # 6
    trailers_count: int  # 0
    rating_allplay: float  # 7.5
    rating_count_allplay: int  # 8
    rating_imdb: Optional[float]  # None
    rating_count_imdb: Optional[int]  # None
    age: int  # 16
    is_free: bool  # False
    est_price: int  # 0
    tvod_price: int  # 0
    langs: Optional[list]  # ['ru']
    in_cinema_till: Optional[str]  # None
    published_at: datetime  # '2025-06-04 01:10:25'
    available_at: Optional[datetime]  # None, 2025-06-04 01:10:25
    title_orig: str  # ''
    url: str  # 'https://allplay.uz/movie/67138/domoxoziain'
    slug: str  # 'domoxoziain'
    rating_kp: Optional[float]  # 7.747
    rating_count_kp: Optional[int]  # 15242
    is_new: bool  # False
    featured: Optional[bool]  # None
    required_services: list  # [34, 1, 31, 11, 35, 39, 41, 42, 45]
    tvod_days: int  # 0
    score: float  # 2217.3
    localized_description: str  # 'Опер Антон Шаталин из Семиморска погряз в работе и перестал уделять...'
    poster: TitlePosterInfo
    genres: list[GenreInfo]
    countries: list[CountryInfo]
    actors: list[PersonInfo]
    directors: list[PersonInfo]


class EpisodeInfo(WinkModel):
    movie_id: int  # 67138
    duration: int  # 2642
    intro_duration: Optional[int]  # 7
    serial_season: Optional[int]  # 1
    serial_season_name: str  # ''
    serial_episode: Optional[int]  # 1
    serial_episode_name: str  # ''
    is_3d: bool  # False
    quality: str  # 'fullhd'
    published_at: datetime  # '2025-05-27 01:40:56'
    title: Optional[str]  # None
    url: Optional[str]  # None
    is_ended: bool  # False
    time_position: Optional[str]  # None
    position: Optional[str]  # None
    is_payment_required: bool  # False
    is_new: bool  # False
    image: EpisodePosterInfo


class TitlesPage(BaseModel):
    data: list[TitleInfo]
    meta: PageMeta


class EpisodesPage(BaseModel):
    data: list[EpisodeInfo]
    meta: PageMeta


class WinkAPI(BaseAPI):
    """API for extraction Wink (ex. MoreTV) content from allplay.uz.

    'https://api.allplay.uz/api/v1/featured?category=moretv' - new content
    'https://api.allplay.uz/api/v1/movies?per_page=12&category=moretv&page=1' - movies and series
    'https://api.allplay.uz/api/v1/movie/48535' - content unit description
    'https://api.allplay.uz/api/v1/file/seasons/48535' - season description
    'https://api.allplay.uz/api/v1/files/48535?per_page=50&sort[]=serial_order,asc&filter[]=serial_season,1&page=1'
         - list of episodes in particular season
    """

    def __init__(self, api_host, http_retries=5, **kwargs):
        self.api_host = api_host
        super().__init__(http_retries=http_retries, **kwargs)

    def get_titles_page(self, page_number=1) -> TitlesPage:
        url = urljoin(self.api_host, "/api/v1/movies")
        titles_page_json = self.get_json_response(
            url,
            get_params={
                "per_page": 50,
                "category": "moretv",
                "page": page_number,
                "embed_wm": 1,  # add content provider's logo to posters
            },
        )
        return TitlesPage(**titles_page_json)

    def get_title_info_json(self, title_remote_id: str) -> dict:
        url = urljoin(self.api_host, f"/api/v1/movie/{title_remote_id}")
        return self.get_json_response(url)["data"]

    def get_title_info(self, title_remote_id: str) -> TitleInfo:
        title_json = self.get_title_info_json(title_remote_id)
        # title_json contains a bit more information, but existing TitleInfo is enough for description.
        return TitleInfo(**title_json)

    def get_seasons_info(self, title_remote_id: str | int) -> dict:
        url = urljoin(self.api_host, f"/api/v1/file/seasons/{title_remote_id}")
        return self.get_json_response(url)

    def get_title_episodes_page(self, title_remote_id: str, page_number=1) -> EpisodesPage:
        url = urljoin(self.api_host, f"/api/v1/files/{title_remote_id}")
        # ?per_page=12&sort[]=serial_order,asc&filter[]=serial_season,1&page=1
        get_params = {
            "per_page": 50,
            "sort[]": "serial_order,asc",
            "page": page_number,
        }
        episodes_page_json = self.get_json_response(url, get_params)
        return EpisodesPage(**episodes_page_json)


def init_wink_api(app: Flask):
    wink_api = WinkAPI(
        api_host=app.config["WINK_API_HOST"],
        http_retries=app.config["WINK_MAX_RETRIES"],
    )

    app.extensions = getattr(app, "extensions", {})
    app.extensions["wink_api"] = wink_api
