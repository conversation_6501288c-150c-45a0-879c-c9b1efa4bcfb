from flask_mongoengine import Document
from mongoengine import (
    CASCADE,
    PULL,
)
from mongoengine.fields import (
    DateTimeField,
    EmbeddedDocumentField,
    IntField,
    ListField,
    ReferenceField,
    StringField,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodEpisode,
    AbcvodGenre,
    AbcvodSeason,
    AbcvodTitle,
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.abcvod.core.models.task import AbcvodImportTracker
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)
from cmf.core.models import CmsEmbeddedDocument


class VipPlayDocument(Document):
    meta = {
        "db_alias": "vipplay",
        "abstract": True,
    }


class VipPlayGenre(VipPlayDocument, AbcvodGenre):
    meta = {"collection": "vipplay_genre"}


class VipPlayCategory(VipPlayDocument, AbcvodCategory):
    meta = {"collection": "vipplay_category"}

    genres = ListField(ReferenceField(VipPlayGenre, reverse_delete_rule=PULL))


class VipPlayLicence(CmsEmbeddedDocument):
    start_date = DateTimeField()
    end_date = DateTimeField()
    countries = ListField(StringField())


class VipPlayTitle(VipPlayDocument, AbcvodTitle):
    meta = {
        "indexes": ["categories", "genres"],
        "collection": "vipplay_title",
    }

    original_title = StringField()
    categories = ListField(ReferenceField(VipPlayCategory, reverse_delete_rule=PULL))
    genres = ListField(ReferenceField(VipPlayGenre, reverse_delete_rule=PULL))
    licences = ListField(EmbeddedDocumentField(VipPlayLicence))
    collections = ListField(ReferenceField("VipPlayCollection"))


class VipPlaySeason(VipPlayDocument, AbcvodSeason):
    meta = {
        "indexes": ["title"],
        "collection": "vipplay_season",
    }

    title: VipPlayTitle = ReferenceField(VipPlayTitle, reverse_delete_rule=CASCADE)


class VipPlayEpisode(VipPlayDocument, AbcvodEpisode):
    meta = {
        "indexes": [
            "title",
            "season",
            "default_caption",
        ],
        "collection": "vipplay_episode",
    }

    title = ReferenceField(VipPlayTitle, reverse_delete_rule=CASCADE)
    season = ReferenceField(VipPlaySeason, reverse_delete_rule=CASCADE)
    release_year = IntField()
    licences = ListField(EmbeddedDocumentField(VipPlayLicence))


class VipPlayImportTracker(AbcvodImportTracker):
    job_title = "VipPlay VOD content import"
    job_id = "vipplay-import"


class VipPlayTitleReference(AbcvodTitleReference):
    title = ReferenceField(VipPlayTitle)


class VipPlayCollection(VipPlayDocument, AbcvodCollection):
    meta = {"collection": "vipplay_collection"}

    titles = TitlesField(VipPlayTitleReference)
    titles_excluded = TitlesExcludedField(VipPlayTitleReference)
    categories = ReferenceCriteriaField(VipPlayTitle.categories)
    genres = ReferenceCriteriaField(VipPlayTitle.genres)
    imdb_rating = RatingCriteriaField(VipPlayTitle.imdb_rating)
    kp_rating = RatingCriteriaField(VipPlayTitle.kp_rating)


VipPlayCollection.register_delete_rule(VipPlayTitle, "collections", PULL)
