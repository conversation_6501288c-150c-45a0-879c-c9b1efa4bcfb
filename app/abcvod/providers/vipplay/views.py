from __future__ import annotations

from flask import current_app
from flask_admin import Admin

from app.abcvod.core.views import (
    AbcVodCategoryView,
    AbcVodCollectionView,
    AbcVodEpisodeView,
    AbcVodGenreView,
    AbcvodImportTaskView,
    AbcVodSeasonView,
    AbcVodTitleView,
    AbcVodViewMixin,
    GetJsonFromApiMixin,
    ReimportMixin,
    init_vod_admin,
)
from app.abcvod.providers.vipplay.api import (
    MovieInfo,
    SeriesInfo,
    VipPlayAPI,
)
from app.abcvod.providers.vipplay.celery import vipplay_import_task
from app.abcvod.providers.vipplay.importer import VipPlayImporter
from app.abcvod.providers.vipplay.models import (
    VipPlayCategory,
    VipPlayCollection,
    VipPlayEpisode,
    VipPlayGenre,
    VipPlayImportTracker,
    VipPlaySeason,
    VipPlayTitle,
)
from app.cms.models import TaskTrigger


class VipPlayMixin(AbcVodViewMixin):
    vod_name = "vipplay"
    genre_class = VipPlayGenre
    category_class = VipPlayCategory
    title_class = VipPlayTitle
    season_class = VipPlaySeason
    episode_class = VipPlayEpisode
    collection_class = VipPlayCollection

    @property
    def api(self) -> VipPlayAPI:
        return current_app.extensions["vipplay_api"]


class VipPlayTitleView(VipPlayMixin, ReimportMixin, GetJsonFromApiMixin, AbcVodTitleView):
    model = VipPlayTitle

    def get_title_info_dict(self, title: VipPlayTitle) -> dict | None:
        self.api.auth()
        if title.is_series:
            all_titles_info = self.api.get_all_series_info_dicts()
        else:
            all_titles_info = self.api.get_all_movies_info_dicts()
        for title_info in all_titles_info:
            if title_info["id"] == title.remote_id:
                return title_info
        return

    def get_title_info(self, title: VipPlayTitle) -> MovieInfo | SeriesInfo | None:
        title_info_dict = self.get_title_info_dict(title)
        if not title_info_dict:
            return
        if title.is_series:
            return SeriesInfo(**title_info_dict)
        return MovieInfo(**title_info_dict)

    def get_api_response(self, document):
        title: VipPlayTitle = document
        return self.get_title_info_dict(title) or f"This title with remote_id {title.remote_id} was not found in api :("

    def reimport_procedure(self, document):
        title: VipPlayTitle = document
        title_info = self.get_title_info(title)
        if title_info is None:
            raise Exception("Title not found in API.")
        importer = VipPlayImporter(app=current_app, task_trigger=TaskTrigger.MANUAL)

        with importer:
            importer.reraise_all_log_errors = True
            importer.results["Reimport title with id"] = title.remote_id
            importer._setup_default_categories()
            with importer.ignore_do_not_autoupdate:
                importer.import_title(title_info)


class VipPlayCategoryView(VipPlayMixin, AbcVodCategoryView):
    model = VipPlayCategory


class VipPlayGenreView(VipPlayMixin, AbcVodGenreView):
    model = VipPlayGenre


class VipPlaySeasonView(VipPlayMixin, AbcVodSeasonView):
    model = VipPlaySeason


class VipPlayEpisodeView(VipPlayMixin, AbcVodEpisodeView):
    model = VipPlayEpisode


class VipPlayCollectionView(VipPlayMixin, AbcVodCollectionView):
    model = VipPlayCollection


class VipPlayImportTaskView(AbcvodImportTaskView):
    model = VipPlayImportTracker
    celery_task = vipplay_import_task


def init_vipplay_admin(admin: Admin, caption="VipPlay", parent_name=None):
    init_vod_admin(
        admin,
        caption,
        vod_mixin=VipPlayMixin,
        category_view=VipPlayCategoryView,
        genre_view=VipPlayGenreView,
        title_view=VipPlayTitleView,
        season_view=VipPlaySeasonView,
        episode_view=VipPlayEpisodeView,
        collection_view=VipPlayCollectionView,
        parent_name=parent_name,
    )
    vod_name = VipPlayMixin.vod_name
    admin.add_view(VipPlayImportTaskView(name="Import tasks", category=caption, endpoint=f"{vod_name}_tasks"))
