from __future__ import annotations

import logging
from collections import defaultdict
from datetime import datetime
from functools import lru_cache
from typing import (
    DefaultDict,
    Optional,
)

from bson.objectid import ObjectId
from flask import Flask
from mongoengine import DoesNotExist
from slugify import slugify

from app.abcvod.core.importer import (
    BaseImporter,
    DoNotAutoupdateMixin,
)
from app.abcvod.core.models.abcvod import (
    AbcvodCountry,
    AbcvodPerson,
)

from .api import (
    EpisodeInfo,
    LicenceInfo,
    MovieInfo,
    SeriesInfo,
    TitleErrorInfo,
    VipPlayAPI,
)
from .models import (
    VipPlayCategory,
    VipPlayCollection,
    VipPlayEpisode,
    VipPlayGenre,
    VipPlayImportTracker,
    VipPlayLicence,
    VipPlaySeason,
    VipPlayTitle,
)

SERIES, MOVIES = "series", "movies"
SERIES_TYPE, MOVIES_TYPE = "series", "movie"
SERIES_NAME, MOVIES_NAME = "Сериалы", "Фильмы"
logger = logging.getLogger(__name__)


def _release_date_from_licences(licences: list[VipPlayLicence], license_country: str) -> Optional[datetime]:
    if not licences:
        return None

    result = datetime.min
    for licence in licences:
        if license_country in licence.countries and licence.start_date > result:
            result = licence.start_date

    return result if result != datetime.min else None


def _is_licences_actual(licences: list[VipPlayLicence], license_country: str) -> bool:
    """Returns `true` in case the given list of licences contains valid license for current time.

    See https://dev.tightvideo.com/issues/63299 for details on this logic.
    """
    now = datetime.now()
    for licence in licences:
        if license_country in licence.countries and licence.start_date < now < licence.end_date:
            return True

    return False


def _get_license_end_date(licences: list[VipPlayLicence], license_country: str) -> None | datetime:
    end_date = None
    for licence in licences:
        if license_country not in licence.countries:
            continue
        if not end_date:
            end_date = licence.end_date
        elif end_date < licence.end_date:
            end_date = licence.end_date
    return end_date


def _get_licenses(licences: list[LicenceInfo]) -> list[VipPlayLicence]:
    result = []
    if not licences:
        return result

    for licence in licences:
        countries_list = []
        countries = licence.countries
        for country in countries:
            countries_list += country.keys()

        result.append(
            VipPlayLicence(
                start_date=datetime.fromtimestamp(licence.start_date),
                end_date=datetime.fromtimestamp(licence.end_date),
                countries=countries_list,
            )
        )

    return result


class VipPlayImporter(DoNotAutoupdateMixin, BaseImporter):
    """Class for importing VipPlay content."""

    task_tracker_model = VipPlayImportTracker
    title_model = VipPlayTitle
    collection_model = VipPlayCollection
    # #73567 set of slugs. Movies with these genres are accepted by "for_kids" profile.
    MOVIE_GENRES_FOR_KIDS = {"semeinyi", "multfilm"}

    def __init__(self, app: Flask, task_tracker: VipPlayImportTracker | None = None, task_trigger: str | None = None):
        max_workers = app.config["VIPPLAY_MAX_WORKERS"]
        super().__init__(max_workers=max_workers, task_trigger=task_trigger, task_tracker=task_tracker)

        self.license_country = app.config["VIPPLAY_LICENSE_COUNTRY"]
        self.vipplay_api: VipPlayAPI = app.extensions["vipplay_api"]
        self._all_genres = set()
        self.category_genres: DefaultDict[ObjectId, set[VipPlayGenre]] = defaultdict(set)

        # Create cache for persons and countries
        self.persons_cache = {person.slug: person for person in AbcvodPerson.objects.all()}
        self.countries_cache = {country.iso2: country for country in AbcvodCountry.objects.all()}
        # Fill known Vipplay's deviations from ISO
        self.countries_cache["UK"] = self.countries_cache["GB"]

    @lru_cache
    def get_or_create_category(self, name, slug) -> VipPlayCategory:
        try:
            category = VipPlayCategory.objects.get(slug=slug)
            # save existing category genres for further updates
            self.category_genres[category.id].update(category.genres)
        except DoesNotExist:
            category = VipPlayCategory(slug=slug)

        category.is_published = True
        category.updated_during_import = self.uid
        if not self.can_update(category):
            return category.save()

        category.caption = name
        return category.save()

    def _setup_default_categories(self):
        """Import categories in mongoDB if not present."""
        self.series_category_id = self.get_or_create_category(name=SERIES_NAME, slug=SERIES).id
        self.movies_category_id = self.get_or_create_category(name=MOVIES_NAME, slug=MOVIES).id

    def upsert_episode(self, episode_info: EpisodeInfo, season: VipPlaySeason) -> VipPlayEpisode:
        remote_id = episode_info.id
        try:
            episode = VipPlayEpisode.objects.get(remote_id=remote_id)
        except DoesNotExist:
            episode = VipPlayEpisode(remote_id=remote_id, created_during_import=self.uid)

        licences = _get_licenses(episode_info.licences)
        episode.is_published = _is_licences_actual(licences, self.license_country)
        episode.updated_during_import = self.uid
        if not self.can_update(episode):
            return episode.save()

        episode_caption = episode_info.title
        title_name = season.title.default_caption
        episode_number = episode_info.episode
        slug = f"{slugify(title_name)}_s{season.number}_e{episode_number}_{remote_id}"
        episode.slug = slug
        episode.poster = episode_info.files.poster
        episode.number = episode_number
        episode.caption = episode_caption
        episode.default_caption = episode_caption
        episode.release_year = episode_info.year
        episode.end_publish_date = _get_license_end_date(licences, self.license_country)
        episode.title = season.title
        episode.season = season
        episode.licences = licences
        episode.release_date = _release_date_from_licences(licences, self.license_country)
        return episode.save()

    def upsert_season(self, season_number: int, title: VipPlayTitle) -> VipPlaySeason:
        slug = f"{slugify(title.default_caption)}_{season_number}"
        try:
            season = VipPlaySeason.objects.get(slug=slug)
        except DoesNotExist:
            season = VipPlaySeason(slug=slug, created_during_import=self.uid)

        season.is_published = True
        season.updated_during_import = self.uid
        if not self.can_update(season):
            return season.save()

        season.caption = f"Сезон {season_number}"
        season.number = season_number
        season.title = title
        return season.save()

    def _get_title_category_id(self, is_series: bool):
        if is_series:
            return self.series_category_id
        return self.movies_category_id

    def _get_movie_years(self, movie_info: MovieInfo) -> list[int]:
        return [movie_info.year]

    def _get_series_years(self, series_info: SeriesInfo) -> list[int]:
        years = []
        for season_info in series_info.seasons:
            for episode_info in season_info.episodes:
                if episode_info.year:
                    years.append(episode_info.year)
        return sorted(list(set(years)))

    def upsert_title(self, title_info: MovieInfo | SeriesInfo) -> VipPlayTitle:
        remote_id = title_info.id

        try:
            title = VipPlayTitle.objects.get(remote_id=remote_id)
        except DoesNotExist:
            title = VipPlayTitle(remote_id=remote_id, created_during_import=self.uid)

        is_series = isinstance(title_info, SeriesInfo)
        category_id = self._get_title_category_id(is_series)
        title.is_published = True
        title.updated_during_import = self.uid

        if not self.can_update(title):
            self.category_genres[category_id].update(title.genres)
            return title.save()

        with self.lock:
            title_genres = self.get_or_create_genres(title_info.genres)
        if is_series:
            years = self._get_series_years(title_info)
        else:
            years = self._get_movie_years(title_info)
        licences = _get_licenses(title_info.licences)
        title_caption = title_info.title
        description = title_info.description
        original_title = title_info.title_original
        age_rating = title_info.rating
        slug = slugify(f"{original_title}_{title_info.id}".lower())
        countries = self.get_countries_for_title(remote_id, title_info.countries)
        directors = self._get_persons(title_info.persons.directors)
        actors = self._get_persons(title_info.persons.artists)

        title.slug = slug
        title.genres = title_genres
        title.original_title = original_title
        title.genres = title_genres
        title.caption = title_caption
        title.default_caption = title_caption
        title.description = description
        title.poster = title_info.files.poster
        title.poster_background = title_info.files.background
        title.age_rating = age_rating
        title.years = years
        title.countries = countries
        title.directors = directors
        title.actors = actors
        title.licences = licences
        title.kp_id = title_info.kinopoisk_id
        title.imdb_id = title_info.imdb_id
        title.categories = [category_id]
        title.is_series = is_series
        title.updated_at = datetime.utcnow()

        # Set a release date for a single title
        # See https://dev.tightvideo.com/issues/61377#note-10
        if not is_series:
            title.release_date = _release_date_from_licences(licences, self.license_country)
            title.is_published = _is_licences_actual(licences, self.license_country)
            title.end_publish_date = _get_license_end_date(licences, self.license_country)

        # Set "for_kids" flag
        if not is_series:
            for_kids = False
            if age_rating <= 12:
                for genre in title_genres:
                    if genre.slug in self.MOVIE_GENRES_FOR_KIDS:
                        for_kids = True
                        break
            title.for_kids = for_kids

        self.category_genres[category_id].update(title.genres)
        return title.save()

    def import_title(self, title_info: MovieInfo | SeriesInfo):
        """Imports film or series to mongoDB."""
        title = self.upsert_title(title_info)
        is_series = isinstance(title_info, SeriesInfo)

        if is_series:
            has_published_episode = False
            seasons = []
            for season_info in title_info.seasons:
                season_number = season_info.season
                season = self.upsert_season(season_number, title=title)
                seasons.append(season)
                for episode_info in season_info.episodes:
                    episode = self.upsert_episode(episode_info=episode_info, season=season)
                    if episode.is_published:
                        has_published_episode = True

            if not has_published_episode:
                title.is_published = False
                title.save()

            # Find a release date for series.
            # See https://dev.tightvideo.com/issues/61377#note-11
            if seasons and has_published_episode:
                last_season = sorted(seasons, key=lambda s: s.number, reverse=True)[0]

                first_episode = (
                    VipPlayEpisode.objects(season=last_season, is_published=True).order_by("release_date").first()
                )
                if first_episode:
                    title.release_date = first_episode.release_date
                    title.save()

    def import_procedure(self):
        """Imports all films and series and dependent objects."""
        all_titles_info: list[MovieInfo | SeriesInfo | TitleErrorInfo] = []
        all_titles_info += [movie_info for movie_info in self.vipplay_api.get_all_movies_info()]
        all_titles_info += [series_info for series_info in self.vipplay_api.get_all_series_info()]

        with self.bunch_of_tasks() as submit:
            for title_info in all_titles_info:
                if isinstance(title_info, TitleErrorInfo):
                    self.add_import_error("Title error", title_info.get_error_message())
                else:
                    submit(self.import_title, title_info)

    def _flush_category_genres(self):
        """Save all changes in category genres."""
        category_do_not_update_ids = VipPlayCategory.get_do_not_autoupdate_ids()
        for category_remote_id, category_genres in self.category_genres.items():
            if category_remote_id in category_do_not_update_ids:
                continue
            category = VipPlayCategory.objects.get(id=category_remote_id)
            category.genres = list(category_genres)
            category.save()

    def before_import(self):
        super(VipPlayImporter, self).before_import()
        self.vipplay_api.auth()
        self._setup_default_categories()

    def after_import(self):
        super(VipPlayImporter, self).after_import()
        self._flush_category_genres()
        self.mark_unpublished(
            {
                "Titles": VipPlayTitle,
                "Episodes": VipPlayEpisode,
                "Seasons": VipPlaySeason,
                "Genres": VipPlayGenre,
            },
        )

    @lru_cache
    def upsert_genre(self, genre_name) -> VipPlayGenre:
        genre_slug = slugify(genre_name)
        try:
            genre = VipPlayGenre.objects.get(slug=genre_slug)
        except DoesNotExist:
            genre = VipPlayGenre(slug=genre_slug, created_during_import=self.uid)

        genre.is_published = True
        genre.updated_during_import = self.uid
        if not self.can_update(genre):
            return genre.save()

        genre.caption = genre_name
        return genre.save()

    def get_or_create_genres(self, genre_name_list: list[str]) -> list[VipPlayGenre]:
        genres = []
        for genre_name in genre_name_list:
            genre = self.upsert_genre(genre_name)
            genres.append(genre)
            self._all_genres.add(genre.slug)
        return genres

    def get_countries_for_title(self, remote_id, countries_info: list[dict]) -> list[AbcvodCountry]:
        result = []
        for country_info in countries_info:
            for country_name, country_iso2 in country_info.items():
                try:
                    result.append(self.countries_cache[country_iso2])
                except KeyError:
                    self.add_import_error(
                        "Unknown country", f"Unexpected country code: '{country_iso2}' for title {remote_id=}"
                    )
        return result

    def _get_persons(self, person_names: list[str]) -> list[AbcvodPerson]:
        result: list[AbcvodPerson] = []
        for name in person_names:
            name_slug = slugify(name)
            try:
                result.append(self.persons_cache[name_slug])
            except KeyError:
                with self.lock:
                    person = AbcvodPerson.get_or_create_person(name=name)
                    result.append(person)
        return result
