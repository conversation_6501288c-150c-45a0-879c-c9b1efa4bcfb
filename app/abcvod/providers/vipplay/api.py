from __future__ import annotations

import json
from typing import (
    Generator,
    Optional,
)

from flask import Flask
from pydantic import (
    BaseModel,
    ConfigDict,
    ValidationError,
    field_validator,
)

from app.abcvod.core.api import BaseAPI


class BaseFilesInfo(BaseModel):
    background: Optional[
        str
    ] = None  # "https://viasat-small.cdnvideo.ru/.../backgrounds/22814fa772ec260067ff8d6e1329d988.jpg"
    poster: str  # "https://viasat-small.cdnvideo.ru/.../posters/69decb4fae75d3ee07348c93cd8ded79.jpg"


class MovieFilesInfo(BaseFilesInfo):
    link: Optional[str] = None  # "https://vipplay.ru/filmy/lovi-volnu-2007/?utm_campaign=partner-integration&utm_...
    source_file: Optional[str] = None  # "https://.../contents/ddf5b9a1-c513-4624-a43b-88e8849fc414/download"
    # FIXME: probably stream cannot be `Optional` here.
    stream: Optional[str] = None  # "https://.../contents/ddf5b9a1-c513-4624-a43b-88e8849fc414/streams.m3u8


class EpisodeFilesInfo(BaseFilesInfo):
    source_file: Optional[str] = None  # "https://.../contents/57c94b2e-9064-4241-b6d2-7065e4fdd675/download"


class SeriesFilesInfo(BaseFilesInfo):
    link: Optional[str] = None  # "https://vipplay.ru/serialy/khiler/?utm_campaign=partner-integration&utm_medium=...


class LicenceInfo(BaseModel):
    countries: list[dict[str, str]]  # [{"Россия": "RU"}]
    end_date: int  # 1695934800
    start_date: int  # 1648674000


class PersonsInfo(BaseModel):
    artists: list[str]  # ["Тимати", "Александр Цекало", ...]
    directors: list[str]  # ["Эш Браннон", "Крис Бак"]


class MovieInfo(BaseModel):
    model_config = ConfigDict(coerce_numbers_to_str=True)

    id: str
    title: str  # "Лови волну!"
    title_original: str  # "Surf's Up"
    imdb_id: Optional[str] = None
    kinopoisk_id: Optional[int] = None
    countries: list[dict[str, str]]  # [{"Соединенные Штаты": "US"}]
    description: str  # Юный пингвин Коди Маверик - восходящая звезда серфинга - собирается...
    files: MovieFilesInfo
    genres: list[str]  # ["Мультфильм", "Комедия"]
    licences: list[LicenceInfo]
    offer_ids: list[str] = []
    persons: PersonsInfo
    rating: int  # 12
    year: int  # 2007

    @field_validator("offer_ids", mode="before")
    def parse_offers(cls, value):
        if value == [None] or not value:
            return []
        return value


class EpisodeInfo(BaseModel):
    id: str  # "57c94b2e-9064-4241-b6d2-7065e4fdd675"
    episode: int  # 18
    title: str  # "18 серия"
    title_original: str  # "Episode 18"
    year: Optional[int] = None  # 2014
    files: EpisodeFilesInfo
    licences: list[LicenceInfo]
    persons: PersonsInfo


class SeasonInfo(BaseModel):
    episodes: list[EpisodeInfo]
    files: BaseFilesInfo
    season: int  # 1
    title: str  # "1 сезон"
    title_original: str  # "Season 1"


class SeriesInfo(MovieInfo):
    files: SeriesFilesInfo
    year: Optional[int] = None  # seems to be required for Movies, but may be missing for Series
    seasons: list[SeasonInfo]
    licences: None = None  # licenses should be in episodes.


class TitleErrorInfo(BaseModel):
    id: str
    errors_json: str

    def get_error_message(self) -> str:
        return f"{self.id} -> {self.errors_json}"


class VipPlayAPI(BaseAPI):
    """Adapter for connection to VipPlay API.

    Full documentation - https://api-partner.vipplay.ru/
    """

    def __init__(self, api_url, access_key, password, http_retries=5):
        """Init.

        :param api_url: Base URL for API
        :param access_key: access key from VipPlay
        :param password: password from VipPlay
        :param http_retries: Retries count for HTTP requests
        """
        super().__init__(http_retries=http_retries)

        self.http_session.headers.update({"Content-Type": "application/json"})
        self.api_url = api_url
        self.password = password
        self.access_key = access_key

        self.movies_url = f"{api_url}/movies/"
        self.series_url = f"{api_url}/series/"
        self.token_url = f"{api_url}/auth/"

    def get_json_response(self, url: str, get_params: dict[str, int] = None) -> tuple[dict, int]:
        """Get JSON response from API.

        :return: Tuple(response for current page, next page number if available)
        """
        response = self.get_response(url, get_params)
        next_page = 0
        headers = response.headers
        if int(headers["Total-Pages"]) > int(headers["Current-Page"]):
            next_page = int(headers["Current-Page"]) + 1

        return response.json(), next_page

    def auth(self):
        response = self.http_session.post(
            self.token_url,
            data=json.dumps({"auth": {"access_key": self.access_key, "password": self.password}}),
        )
        response.raise_for_status()
        access_token = response.json()["access_token"]
        self.http_session.headers["Authorization"] = access_token

    def _get_all_info_from_all_pages(self, url) -> Generator[dict, None, None]:
        next_page_number = 1
        while next_page_number:
            get_params = {"page": next_page_number}
            page, next_page_number = self.get_json_response(url=url, get_params=get_params)
            page: list[dict]
            for info in page:
                yield info

    def get_all_movies_info(self) -> Generator[MovieInfo | TitleErrorInfo, None, None]:
        """Get all info about all movies from VipPlay API."""
        for info in self._get_all_info_from_all_pages(self.movies_url):
            try:
                yield MovieInfo(**info)
            except ValidationError as validation_error:
                yield self._get_title_error_info(validation_error, info)

    def get_all_series_info(self) -> Generator[SeriesInfo | TitleErrorInfo, None, None]:
        """Get info about all series from VipPlay API."""
        for info in self._get_all_info_from_all_pages(self.series_url):
            try:
                yield SeriesInfo(**info)
            except ValidationError as validation_error:
                yield self._get_title_error_info(validation_error, info)

    def get_all_movies_info_dicts(self) -> Generator[dict, None, None]:
        """Same as `get_all_movies_info`, but return dicts, required in views."""
        for info in self._get_all_info_from_all_pages(self.movies_url):
            yield info

    def get_all_series_info_dicts(self) -> Generator[dict, None, None]:
        """Same as `get_all_series_info`, but return dicts, required in views."""
        for info in self._get_all_info_from_all_pages(self.series_url):
            yield info

    def _get_title_error_info(self, validation_error: ValidationError, title_info_dict: dict) -> TitleErrorInfo:
        return TitleErrorInfo(
            id=title_info_dict.get("id", "Title ID is unknown!"),
            errors_json=validation_error.json(),
        )


def init_vipplay_api(app: Flask):
    """Creates VipPlayAPI client using app's config and adds it to app's extensions under `vipplay_api` key."""
    vipplay_api = VipPlayAPI(
        api_url=app.config["VIPPLAY_API_URL"],
        access_key=app.config["VIPPLAY_ACCESS_KEY"],
        password=app.config["VIPPLAY_PASSWORD"],
        http_retries=app.config["VIPPLAY_MAX_RETRIES"],
    )

    app.extensions = getattr(app, "extensions", {})
    app.extensions["vipplay_api"] = vipplay_api
