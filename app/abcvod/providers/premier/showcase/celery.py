from __future__ import annotations

from flask import current_app

from app.abcvod.providers.premier.common.tasks import CommonPremierExportToClickhouseProcessor
from app.abcvod.providers.premier.showcase.importer import PremierShowcaseImporter
from app.abcvod.providers.premier.showcase.models import (
    PremierShowcaseEpisode,
    PremierShowcaseExportToClickhouseTracker,
    PremierShowcaseImportTracker,
    PremierShowcaseTitle,
)
from app.cms.models import (
    CeleryTaskTracker,
    TaskTrigger,
)
from app.cms.tasks.utils import cms_task


@cms_task(task_processor_class=PremierShowcaseImporter)
def premier_showcase_import_task(task_trigger: str, task_tracker: PremierShowcaseImportTracker | None = None):
    importer = PremierShowcaseImporter(app=current_app, task_trigger=task_trigger, task_tracker=task_tracker)
    importer.run()
    premier_showcase_export_to_clickhouse.delay(task_trigger=TaskTrigger.TASK)


class PremierShowcaseExportToClickhouseProcessor(CommonPremierExportToClickhouseProcessor):
    task_tracker_model = PremierShowcaseExportToClickhouseTracker
    title_model = PremierShowcaseTitle
    episode_model = PremierShowcaseEpisode


@cms_task(task_processor_class=PremierShowcaseExportToClickhouseProcessor)
def premier_showcase_export_to_clickhouse(task_trigger: str, task_tracker: CeleryTaskTracker = None):
    processor = PremierShowcaseExportToClickhouseProcessor(task_trigger=task_trigger, task_tracker=task_tracker)
    processor.run()
