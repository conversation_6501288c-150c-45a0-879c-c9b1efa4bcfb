from __future__ import annotations

import logging
from collections import defaultdict
from datetime import datetime
from typing import Iterable

from bson import ObjectId
from flask import Flask
from mongoengine import DoesNotExist
from slugify import slugify

from app.abcvod.core.models.task import AbcvodImportTracker
from app.abcvod.providers.premier.common.api import (
    CategoryInfo,
    EpisodeInfo,
    ErrorInfo,
    ResourceInfo,
)
from app.abcvod.providers.premier.common.importer import BasePremierImporter
from app.abcvod.providers.premier.showcase.models import (
    PremierShowcaseCategory,
    PremierShowcaseCollection,
    PremierShowcaseEpisode,
    PremierShowcaseGenre,
    PremierShowcaseImportTracker,
    PremierShowcaseSeason,
    PremierShowcaseTitle,
)

logger = logging.getLogger(__name__)


class PremierShowcaseImporter(BasePremierImporter):
    """Class for importing Premier content with showcase ID."""

    category_model = PremierShowcaseCategory
    genre_model = PremierShowcaseGenre
    title_model = PremierShowcaseTitle
    season_model = PremierShowcaseSeason
    episode_model = PremierShowcaseEpisode
    collection_model = PremierShowcaseCollection
    task_tracker_model = PremierShowcaseImportTracker

    def __init__(self, *, app: Flask, task_tracker: AbcvodImportTracker | None = None, task_trigger: str | None = None):
        super().__init__(
            app=app,
            images_path=app.config["PREMIER_SHOWCASE_STORAGE_FOLDER"],
            task_tracker=task_tracker,
            task_trigger=task_trigger,
        )
        # Some parameters, required by current importer.
        self.cardgroup_id = 471  # FIXME: WHAT IS THIS?
        self.showcase_id = app.config["PREMIER_SHOWCASE_ID"]

        # Bonds between titles and categories for further usage, {title_remote_id: set(category_id...)}
        self.titles_categories: dict[str, set[ObjectId]] = defaultdict(set)
        # Bonds between titles and genres for further usage, {title_remote_id: set(genre_id...)}
        self.titles_genres: dict[str, set[ObjectId]] = defaultdict(set)
        self.genres_cache = {}

    def import_title(
        self, remote_id: str, category_ids: Iterable[PremierShowcaseCategory], genre_ids: Iterable[PremierShowcaseGenre]
    ):
        with self.log_import_error(custom_message=f"Error during import title with remote_id {remote_id}"):
            logger.info(f"Importing title {remote_id}...")
            self._import_title(remote_id, category_ids, genre_ids)
            logger.info(f"Title {remote_id} imported.")

    def _prepare_episodes_info(self, episodes_info: Iterable[EpisodeInfo | ErrorInfo]) -> list[EpisodeInfo]:
        """Make list from generator, then filter and report all possible 'ErrorInfo' out of it."""
        all_info = []
        for info in episodes_info:
            if isinstance(info, ErrorInfo):
                self.add_import_error("Bad episode info", info.get_error_message())
                continue
            all_info.append(info)
        return all_info

    def _import_title(
        self, remote_id: str, categories: Iterable[PremierShowcaseCategory], genres: Iterable[PremierShowcaseGenre]
    ):
        """Import film or series to mongoDB."""
        title_info = self.premier_api.get_title_info(remote_id=remote_id)
        raw_episodes_info: Iterable[EpisodeInfo | ErrorInfo] = self.premier_api.get_episodes_info(remote_id)
        episodes_info: list[EpisodeInfo] = self._prepare_episodes_info(raw_episodes_info)

        categories = list(categories)
        genres = list(genres)

        title = self.upsert_title(remote_id, title_info, episodes_info, categories, genres)

        if title.is_series:
            self.import_seasons_and_episodes(title, episodes_info)

    def import_procedure(self):
        """Import categories and dependent objects in mongoDB."""
        self.import_categories_and_genres()
        self.import_all_titles()

    def import_all_titles(self):
        with self.bunch_of_tasks() as submit:
            for remote_id, category_ids in self.titles_categories.items():
                submit(self.import_title, remote_id, category_ids, self.titles_genres[remote_id])

    def upsert_genre(self, resource_info: ResourceInfo, priority: int) -> PremierShowcaseGenre:
        """Import genre in mongoDB if not present.

        :param resource_info: data from CategoryInfo, which actually contains info about genre
        :param priority: priority of genre
        """
        remote_id = str(resource_info.id)
        if remote_id in self.genres_cache:
            return self.genres_cache[remote_id]
        try:
            genre = self.genre_model.objects.get(remote_id=remote_id)
        except DoesNotExist:
            genre = self.genre_model(remote_id=remote_id, created_during_import=self.uid)

        self.genres_cache[remote_id] = genre

        # This does not have much sense, because today we delete absent genres instead of 'unpublish'.
        # But I add following lines (import uid etc.) for consistency reasons.
        genre.is_published = True
        genre.updated_during_import = self.uid
        if not self.can_update(genre):
            return genre.save()

        genre_name = resource_info.name
        genre.remote_id = remote_id
        genre.slug = slugify(genre_name) + "-" + remote_id
        genre.caption = genre_name
        genre.is_published = True
        genre.default_caption = genre_name
        genre.priority = priority
        return genre.save()

    def get_or_create_category(self, category_priority: int, category_info: CategoryInfo) -> PremierShowcaseCategory:
        slug = slugify(category_info.name)
        try:
            category = self.category_model.objects.get(slug=slug)
        except DoesNotExist:
            category = self.category_model(slug=slug)

        category.is_published = True
        category.updated_during_import = self.uid
        if not self.can_update(category):
            return category.save()

        category.slug = slug
        category.caption = category_info.name
        category.created_at = datetime.utcnow()
        category.priority = category_priority
        return category.save()

    def import_categories_and_genres(self):
        categories_info = self.premier_api.get_categories_info(showcase_id=self.showcase_id)

        genre_priority = 1
        for category_priority, category_info in enumerate(categories_info, start=1):
            category = self.get_or_create_category(category_priority, category_info)

            category_genres = []
            category_title_ids = []

            for resource_info in category_info.resources:
                cardgroup_id = resource_info.object_id
                genre_title_remote_ids = list(self.collect_cardgroup_title_ids(cardgroup_id))
                genre = self.upsert_genre(resource_info, priority=genre_priority)
                category_title_ids.extend(genre_title_remote_ids)
                genre_priority += 1
                category_genres.append(genre.id)
                for title_remote_id in genre_title_remote_ids:
                    self.titles_genres[title_remote_id].add(genre.id)
            self.actual_genres_remote_ids = self.genres_cache.keys()

            if not category.do_not_autoupdate:
                category.genres = category_genres
                category.save()

            for title_remote_id in category_title_ids:
                self.titles_categories[title_remote_id].add(category.id)
