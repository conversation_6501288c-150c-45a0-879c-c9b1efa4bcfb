from __future__ import annotations

from flask import current_app
from flask_admin import Admin
from flask_security import current_user

from app.abcvod.core.views import (
    AbcVodCategoryView,
    AbcVodCollectionView,
    AbcVodEpisodeView,
    AbcVodGenreView,
    AbcvodImportTaskView,
    AbcVodSeasonView,
    AbcVodTitleView,
    AbcVodViewMixin,
    GetJsonFromApiMixin,
    ReimportMixin,
    init_vod_admin,
)
from app.abcvod.providers.premier.common.api import PremierAPI
from app.abcvod.providers.premier.common.models import PremierTitleAbstract
from app.abcvod.providers.premier.showcase.celery import (
    premier_showcase_export_to_clickhouse,
    premier_showcase_import_task,
)
from app.abcvod.providers.premier.showcase.importer import PremierShowcaseImporter
from app.abcvod.providers.premier.showcase.models import (
    PremierShowcaseCategory,
    PremierShowcaseCollection,
    PremierShowcaseEpisode,
    PremierShowcaseExportToClickhouseTracker,
    PremierShowcaseGenre,
    PremierShowcaseImportTracker,
    PremierShowcaseSeason,
    PremierShowcaseTitle,
)
from app.cms.models import TaskTrigger
from app.cms.tasks.views import CeleryTaskView


class PremierShowcaseMixin(AbcVodViewMixin):
    vod_name = "premier_showcase"
    genre_class = PremierShowcaseGenre
    category_class = PremierShowcaseCategory
    title_class = PremierShowcaseTitle
    season_class = PremierShowcaseSeason
    episode_class = PremierShowcaseEpisode
    collection_class = PremierShowcaseCollection

    @property
    def api(self) -> PremierAPI:
        return current_app.extensions["premier_api"]


class PremierShowcaseTitleView(ReimportMixin, PremierShowcaseMixin, GetJsonFromApiMixin, AbcVodTitleView):
    def reimport_procedure(self, document):
        title: PremierShowcaseTitle = document
        tracker = PremierShowcaseImporter.create_tracker(task_trigger=TaskTrigger.MANUAL, user=current_user)
        importer = PremierShowcaseImporter(app=current_app, task_tracker=tracker)
        with importer as importer:
            importer: PremierShowcaseImporter
            importer.results["Reimport title with id"] = title.remote_id
            importer.reraise_all_log_errors = True
            # We use known categories and genres, so broken categories and genres will not be fixed. This is caused by
            # Premier's API, it's too complicated to find and import/use categories and genres for certain title.
            # Also, in case there appears to be new categories and genres - we need full import anyway.
            with importer.ignore_do_not_autoupdate:
                importer.import_title(title.remote_id, title.categories, title.genres)
            importer.download_all_images()

    def get_api_response(self, document):
        title: PremierTitleAbstract = document
        return self.api.get_title_info_raw(title.remote_id)


class PremierShowcaseCategoryView(PremierShowcaseMixin, AbcVodCategoryView):
    pass


class PremierShowcaseGenreView(PremierShowcaseMixin, AbcVodGenreView):
    pass


class PremierShowcaseSeasonView(PremierShowcaseMixin, AbcVodSeasonView):
    pass


class PremierShowcaseEpisodeView(GetJsonFromApiMixin, PremierShowcaseMixin, AbcVodEpisodeView):
    def get_episode_info(self, title_remote_id: str, episode_remote_id: str):
        for episode_info in self.api.get_episodes_info_raw(title_remote_id=title_remote_id):
            if str(episode_info["id"]) == episode_remote_id:
                return episode_info

    def get_api_response(self, document):
        episode: PremierShowcaseEpisode = document
        title: PremierShowcaseTitle = episode.title
        return self.get_episode_info(title.remote_id, episode.remote_id)


class PremierShowcaseCollectionView(PremierShowcaseMixin, AbcVodCollectionView):
    model = PremierShowcaseCollection


class PremierShowcaseImportTaskView(AbcvodImportTaskView):
    model = PremierShowcaseImportTracker
    celery_task = premier_showcase_import_task


class PremierShowcaseExportTaskView(CeleryTaskView):
    model = PremierShowcaseExportToClickhouseTracker
    celery_task = premier_showcase_export_to_clickhouse
    default_action_title = "Run export to Clickhouse"


def init_premier_showcase_admin(admin: Admin, caption="Premier Showcase", parent_name=None):
    init_vod_admin(
        admin,
        caption,
        vod_mixin=PremierShowcaseMixin,
        category_view=PremierShowcaseCategoryView,
        genre_view=PremierShowcaseGenreView,
        title_view=PremierShowcaseTitleView,
        season_view=PremierShowcaseSeasonView,
        episode_view=PremierShowcaseEpisodeView,
        collection_view=PremierShowcaseCollectionView,
        parent_name=parent_name,
    )
    vod_name = PremierShowcaseMixin.vod_name
    admin.add_view(PremierShowcaseImportTaskView(name="Import tasks", category=caption, endpoint=f"{vod_name}_tasks"))
    admin.add_view(
        PremierShowcaseExportTaskView(name="Export to CH tasks", category=caption, endpoint=f"{vod_name}_export_to_ch")
    )
