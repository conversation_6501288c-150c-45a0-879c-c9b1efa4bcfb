from flask import current_app
from flask.cli import AppGroup

from app.abcvod.providers.premier.showcase.importer import PremierShowcaseImporter
from app.cms.models import TaskTrigger

premier_showcase_commands = AppGroup("premier-showcase")


@premier_showcase_commands.command("import")
def premier_import_objects():
    """Import for Premier Showcase."""
    importer = PremierShowcaseImporter(app=current_app, task_trigger=TaskTrigger.CLI)
    importer.skip_download_images = True
    importer.run()


@premier_showcase_commands.command("export-to-clickhouse")
def export_to_clickhouse():
    from app.abcvod.providers.premier.showcase.celery import PremierShowcaseExportToClickhouseProcessor

    processor = PremierShowcaseExportToClickhouseProcessor(task_trigger=TaskTrigger.CLI)
    processor.run()
