from mongoengine import (
    CASCAD<PERSON>,
    PULL,
    ListField,
    ReferenceField,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodGenre,
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.abcvod.core.models.task import AbcvodImportTracker
from app.abcvod.providers.premier.common.models import (
    PremierEpisodeAbstract,
    PremierExportToClickhouseTracker,
    PremierSeasonAbstract,
    PremierTitleAbstract,
)
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)

DB_ALIAS = "premier_showcase"


class PremierShowcaseGenre(AbcvodGenre):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_genre",
    }


class PremierShowcaseCategory(AbcvodCategory):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_category",
    }
    genres = ListField(ReferenceField(PremierShowcaseGenre, reverse_delete_rule=PULL))


class PremierShowcaseTitle(PremierTitleAbstract):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_title",
    }
    categories = ListField(ReferenceField(PremierShowcaseCategory, reverse_delete_rule=PULL))
    genres = ListField(ReferenceField(PremierShowcaseGenre, reverse_delete_rule=PULL))
    collections = ListField(ReferenceField("PremierShowcaseCollection"))


class PremierShowcaseTitleReference(AbcvodTitleReference):
    title = ReferenceField(PremierShowcaseTitle)


class PremierShowcaseCollection(AbcvodCollection):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_collection",
    }
    titles = TitlesField(PremierShowcaseTitleReference)
    titles_excluded = TitlesExcludedField(PremierShowcaseTitleReference)
    categories = ReferenceCriteriaField(PremierShowcaseTitle.categories)
    genres = ReferenceCriteriaField(PremierShowcaseTitle.genres)
    imdb_rating = RatingCriteriaField(PremierShowcaseTitle.imdb_rating)
    kp_rating = RatingCriteriaField(PremierShowcaseTitle.kp_rating)


PremierShowcaseCollection.register_delete_rule(PremierShowcaseTitle, "collections", PULL)


class PremierShowcaseSeason(PremierSeasonAbstract):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_season",
    }

    title = ReferenceField(PremierShowcaseTitle, reverse_delete_rule=CASCADE)


class PremierShowcaseEpisode(PremierEpisodeAbstract):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_episode",
    }

    title = ReferenceField(PremierShowcaseTitle, reverse_delete_rule=CASCADE)
    season = ReferenceField(PremierShowcaseSeason, reverse_delete_rule=CASCADE)


class PremierShowcaseExportToClickhouseTracker(PremierExportToClickhouseTracker):
    job_title = "Premier Showcase - export to ClickHouse"
    job_id = "premier-showcase-to-clickhouse"


class PremierShowcaseImportTracker(AbcvodImportTracker):
    job_title = "Premier Showcase VOD content import"
    job_id = "premier-showcase-import"
