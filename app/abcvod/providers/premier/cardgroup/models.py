from mongoengine import (
    CASCADE,
    PULL,
    <PERSON>Field,
    ReferenceField,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodGenre,
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.abcvod.core.models.task import AbcvodImportTracker
from app.abcvod.providers.premier.common.models import (
    PremierEpisodeAbstract,
    PremierExportToClickhouseTracker,
    PremierSeasonAbstract,
    PremierTitleAbstract,
)
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)

DB_ALIAS = "premier"


class PremierCardgroupGenre(AbcvodGenre):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_genre",
    }


class PremierCardgroupCategory(AbcvodCategory):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_category",
    }
    genres = ListField(ReferenceField(PremierCardgroupGenre, reverse_delete_rule=PULL))


class PremierCardgroupTitle(PremierTitleAbstract):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_title",
    }
    categories = ListField(ReferenceField(PremierCardgroupCategory, reverse_delete_rule=PULL))
    genres = ListField(ReferenceField(PremierCardgroupGenre, reverse_delete_rule=PULL))
    collections = ListField(ReferenceField("PremierCardgroupCollection"))


class PremierCardgroupTitleReference(AbcvodTitleReference):
    title = ReferenceField(PremierCardgroupTitle)


class PremierCardgroupCollection(AbcvodCollection):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_collection",
    }
    titles = TitlesField(PremierCardgroupTitleReference)
    titles_excluded = TitlesExcludedField(PremierCardgroupTitleReference)
    categories = ReferenceCriteriaField(PremierCardgroupTitle.categories)
    genres = ReferenceCriteriaField(PremierCardgroupTitle.genres)
    imdb_rating = RatingCriteriaField(PremierCardgroupTitle.imdb_rating)
    kp_rating = RatingCriteriaField(PremierCardgroupTitle.kp_rating)


PremierCardgroupCollection.register_delete_rule(PremierCardgroupTitle, "collections", PULL)


class PremierCardgroupSeason(PremierSeasonAbstract):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_season",
    }

    title = ReferenceField(PremierCardgroupTitle, reverse_delete_rule=CASCADE)


class PremierCardgroupEpisode(PremierEpisodeAbstract):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "premier_episode",
    }

    title = ReferenceField(PremierCardgroupTitle, reverse_delete_rule=CASCADE)
    season = ReferenceField(PremierCardgroupSeason, reverse_delete_rule=CASCADE)


class PremierCardgroupImportTracker(AbcvodImportTracker):
    job_title = "Premier VOD content import"
    job_id = "premier-cardgroup-import"


class PremierCardgroupExportToClickhouseTracker(PremierExportToClickhouseTracker):
    job_title = "Premier Cardgroup - export to ClickHouse"
    job_id = "premier-cardgroup-to-clickhouse"
