from flask import current_app
from flask.cli import AppGroup

from app.abcvod.providers.premier.cardgroup.importer import PremierCardgroupImporter
from app.cms.models import TaskTrigger

premier_cardgroup_commands = AppGroup("premier-cardgroup")


@premier_cardgroup_commands.command("import")
def premier_cardgroup_import():
    """Import for Premier Cardgroup."""
    importer = PremierCardgroupImporter(app=current_app, task_trigger=TaskTrigger.CLI)
    importer.skip_download_images = True
    importer.run()


@premier_cardgroup_commands.command("export-to-clickhouse")
def export_to_clickhouse():
    from app.abcvod.providers.premier.cardgroup.celery import PremierExportToClickhouseProcessor
    from app.cms.models import TaskTrigger

    processor = PremierExportToClickhouseProcessor(task_trigger=TaskTrigger.CLI)
    processor.run()
