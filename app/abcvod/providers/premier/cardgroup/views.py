from flask import current_app
from flask_admin import Admin
from flask_security import current_user

from app.abcvod.core.views import (
    AbcVodCategoryView,
    AbcVodCollectionView,
    AbcVodEpisodeView,
    AbcVodGenreView,
    AbcvodImportTaskView,
    AbcVodSeasonView,
    AbcVodTitleView,
    AbcVodViewMixin,
    GetJsonFromApiMixin,
    ReimportMixin,
    init_vod_admin,
)
from app.abcvod.providers.premier.cardgroup.celery import (
    premier_cardgroup_export_to_clickhouse,
    premier_cardgroup_import_task,
)
from app.abcvod.providers.premier.cardgroup.importer import PremierCardgroupImporter
from app.abcvod.providers.premier.cardgroup.models import (
    PremierCardgroupCategory,
    PremierCardgroupCollection,
    PremierCardgroupEpisode,
    PremierCardgroupExportToClickhouseTracker,
    PremierCardgroupGenre,
    PremierCardgroupImportTracker,
    PremierCardgroupSeason,
    PremierCardgroupTitle,
)
from app.abcvod.providers.premier.common.api import Premier<PERSON><PERSON>
from app.abcvod.providers.premier.common.models import Premier<PERSON>itleAbstract
from app.cms.models import TaskTrigger
from app.cms.tasks.views import CeleryTaskView


class PremierCardgroupMixin(AbcVodViewMixin):
    vod_name = "premier_cardgroup"
    genre_class = PremierCardgroupGenre
    category_class = PremierCardgroupCategory
    title_class = PremierCardgroupTitle
    season_class = PremierCardgroupSeason
    episode_class = PremierCardgroupEpisode
    collection_class = PremierCardgroupCollection

    @property
    def api(self) -> PremierAPI:
        return current_app.extensions["premier_api"]


class PremierCardgroupTitleView(PremierCardgroupMixin, ReimportMixin, GetJsonFromApiMixin, AbcVodTitleView):
    def reimport_procedure(self, document):
        title: PremierCardgroupTitle = document
        tracker = PremierCardgroupImporter.create_tracker(TaskTrigger.MANUAL, user=current_user)
        importer = PremierCardgroupImporter(app=current_app, task_tracker=tracker)
        importer.reraise_all_log_errors = True
        with importer as importer:
            importer.reraise_all_log_errors = True
            importer.setup_categories()
            with importer.ignore_do_not_autoupdate:
                importer.import_title(title.remote_id)
            importer.download_all_images()

    def get_api_response(self, document):
        title: PremierTitleAbstract = document
        return self.api.get_title_info_raw(title.remote_id)


class PremierCardgroupCategoryView(PremierCardgroupMixin, AbcVodCategoryView):
    pass


class PremierCardgroupGenreView(PremierCardgroupMixin, AbcVodGenreView):
    pass


class PremierCardgroupSeasonView(PremierCardgroupMixin, AbcVodSeasonView):
    pass


class PremierCardgroupEpisodeView(GetJsonFromApiMixin, PremierCardgroupMixin, AbcVodEpisodeView):
    def get_episode_info(self, title_remote_id: str, episode_remote_id: str):
        for episode_info in self.api.get_episodes_info_raw(title_remote_id=title_remote_id):
            if str(episode_info["id"]) == episode_remote_id:
                return episode_info

    def get_api_response(self, document):
        episode: PremierCardgroupEpisode = document
        title: PremierCardgroupTitle = episode.title
        return self.get_episode_info(title.remote_id, episode.remote_id)


class PremierCardgroupCollectionView(PremierCardgroupMixin, AbcVodCollectionView):
    model = PremierCardgroupCollection


class PremierCardgroupImportTaskView(AbcvodImportTaskView):
    model = PremierCardgroupImportTracker
    celery_task = premier_cardgroup_import_task


class PremierCardgroupExportTaskView(CeleryTaskView):
    model = PremierCardgroupExportToClickhouseTracker
    celery_task = premier_cardgroup_export_to_clickhouse
    default_action_title = "Run export to Clickhouse"


def init_premier_cardgroup_admin(admin: Admin, caption="Premier Cardgroup", parent_name=None):
    init_vod_admin(
        admin,
        caption,
        vod_mixin=PremierCardgroupMixin,
        category_view=PremierCardgroupCategoryView,
        genre_view=PremierCardgroupGenreView,
        title_view=PremierCardgroupTitleView,
        season_view=PremierCardgroupSeasonView,
        episode_view=PremierCardgroupEpisodeView,
        collection_view=PremierCardgroupCollectionView,
        parent_name=parent_name,
    )
    vod_name = PremierCardgroupMixin.vod_name
    admin.add_view(PremierCardgroupImportTaskView(name="Import tasks", category=caption, endpoint=f"{vod_name}_tasks"))
    admin.add_view(
        PremierCardgroupExportTaskView(name="Export to CH tasks", category=caption, endpoint=f"{vod_name}_export_to_ch")
    )
