from __future__ import annotations

from flask import current_app

from app.abcvod.providers.premier.cardgroup.importer import PremierCardgroupImporter
from app.abcvod.providers.premier.cardgroup.models import (
    PremierCardgroupEpisode,
    PremierCardgroupExportToClickhouseTracker,
    PremierCardgroupImportTracker,
    PremierCardgroupTitle,
)
from app.abcvod.providers.premier.common.tasks import CommonPremierExportToClickhouseProcessor
from app.cms.models import (
    CeleryTaskTracker,
    TaskTrigger,
)
from app.cms.tasks.utils import cms_task


@cms_task(task_processor_class=PremierCardgroupImporter)
def premier_cardgroup_import_task(task_trigger: str, task_tracker: PremierCardgroupImportTracker | None = None):
    importer = PremierCardgroupImporter(app=current_app, task_trigger=task_trigger, task_tracker=task_tracker)
    importer.run()
    premier_cardgroup_export_to_clickhouse.delay(task_trigger=TaskTrigger.TASK)


class PremierExportToClickhouseProcessor(CommonPremierExportToClickhouseProcessor):
    task_tracker_model = PremierCardgroupExportToClickhouseTracker
    title_model = PremierCardgroupTitle
    episode_model = PremierCardgroupEpisode


@cms_task(task_processor_class=PremierExportToClickhouseProcessor)
def premier_cardgroup_export_to_clickhouse(task_trigger: str, task_tracker: CeleryTaskTracker = None):
    processor = PremierExportToClickhouseProcessor(task_trigger=task_trigger, task_tracker=task_tracker)
    processor.run()
