from __future__ import annotations

import logging
from collections import defaultdict
from functools import lru_cache

from flask import Flask
from mongoengine import DoesNotExist

from app.abcvod.core.models.task import AbcvodImportTracker
from app.abcvod.providers.premier.cardgroup.models import (
    PremierCardgroupCategory,
    PremierCardgroupCollection,
    PremierCardgroupEpisode,
    PremierCardgroupGenre,
    PremierCardgroupImportTracker,
    PremierCardgroupSeason,
    PremierCardgroupTitle,
)
from app.abcvod.providers.premier.common.api import (
    EpisodeInfo,
    ErrorInfo,
    TitleInfo,
    TitleType,
)
from app.abcvod.providers.premier.common.importer import BasePremierImporter
from cmf.core.fields import MultilangString

logger = logging.getLogger(__name__)


class PremierCardgroupImporter(BasePremierImporter):
    """Class for importing Premier content with cardgroup id."""

    category_model = PremierCardgroupCategory
    genre_model = PremierCardgroupGenre
    title_model = PremierCardgroupTitle
    season_model = PremierCardgroupSeason
    episode_model = PremierCardgroupEpisode
    collection_model = PremierCardgroupCollection
    task_tracker_model = PremierCardgroupImportTracker

    def __init__(self, *, app: Flask, task_tracker: AbcvodImportTracker | None = None, task_trigger: str | None = None):
        super().__init__(
            app=app,
            images_path=app.config["PREMIER_STORAGE_FOLDER"],
            task_tracker=task_tracker,
            task_trigger=task_trigger,
        )
        # Options, exclusive to Cardgroup importer options
        self.cardgroup_id = app.config["PREMIER_CARDGROUP_ID"]
        # Some variables, which will be set during import.
        self.series_category = None
        self.movies_category = None
        self.show_category = None
        # Bonds between categories and genres. Filled during import, saved in the end.
        self.category_genres: dict[PremierCardgroupCategory, set[PremierCardgroupGenre]] = defaultdict(set)

    def import_procedure(self):
        """Import all films and series and dependent objects."""
        self.setup_categories()
        self.import_all_titles()
        self.save_genres_in_categories()

    def import_title(self, remote_id: str):
        with self.log_import_error(custom_message=f"Error during import title with remote_id {remote_id}"):
            logger.info(f"Importing title {remote_id}...")
            self._import_title(remote_id)
            logger.info(f"Title {remote_id} imported.")

    def _import_title(self, remote_id: str):
        """Import whole title, with seasons and episodes, to DB.

        :param remote_id: id of a title in Premier API.
        """
        remote_id = str(remote_id)  # just in case
        title_info: TitleInfo = self.premier_api.get_title_info(remote_id=remote_id)
        episodes_info: list[EpisodeInfo] = []
        for info in self.premier_api.get_episodes_info(title_remote_id=remote_id):
            if isinstance(info, ErrorInfo):
                self.add_import_error("Bad episode info", info.get_error_message())
            else:
                episodes_info.append(info)
        genres: list[PremierCardgroupGenre] = self.get_title_genres(title_info)
        categories = self.get_title_categories(title_info.type.name)

        title = self.upsert_title(remote_id, title_info, episodes_info, categories, genres)
        self.add_genres_to_categories(categories, genres)

        if title.is_series:
            self.import_seasons_and_episodes(title, episodes_info)

    def upsert_category(self, slug: str, name: str) -> PremierCardgroupCategory:
        try:
            category = self.category_model.objects.get(slug=slug)
        except DoesNotExist:
            category = self.category_model(slug=slug)

        category.is_published = True
        category.updated_during_import = self.uid
        if not self.can_update(category):
            return category.save()

        category.slug = slug
        category.caption = name
        return category.save()

    def setup_categories(self):
        """Import categories in mongoDB if not present."""
        # Category slug is explicitly hardcoded and may be slightly different from "TitleType".
        self.movies_category = self.upsert_category("movies", "Фильмы")
        self.series_category = self.upsert_category("series", "Сериалы")
        self.show_category = self.upsert_category("show", "Шоу")

    def import_all_titles(self):
        all_title_ids = self.collect_cardgroup_title_ids(self.cardgroup_id)
        with self.bunch_of_tasks() as submit:
            for _id in all_title_ids:
                submit(self.import_title, str(_id))

    def get_title_categories(self, title_type) -> list[PremierCardgroupCategory]:
        if title_type == TitleType.SERIES:
            return [self.series_category]
        elif title_type == TitleType.MOVIES:
            return [self.movies_category]
        elif title_type == TitleType.SHOW:
            return [self.show_category]
        else:
            raise Exception(f"Title type {title_type} is unknown")

    @lru_cache
    def upsert_genre(self, genre_name: str, genre_remote_id: str) -> PremierCardgroupGenre:
        self.actual_genres_remote_ids.add(genre_remote_id)
        try:
            genre = self.genre_model.objects.get(remote_id=genre_remote_id)
        except DoesNotExist:
            genre = self.genre_model(remote_id=genre_remote_id, created_during_import=self.uid)

        # This does not have much sense, because today we delete absent genres instead of 'unpublish'.
        # But I add following lines (import uid etc.) for consistency reasons.
        genre.is_published = True
        genre.updated_during_import = self.uid
        if not self.can_update(genre):
            return genre.save()

        genre.caption = MultilangString(genre_name)
        genre.slug = genre_name
        genre.default_caption = genre_name
        return genre.save()

    def get_title_genres(self, title_info: TitleInfo) -> list[PremierCardgroupGenre]:
        result = []
        for genre_info in title_info.genres:
            genre_name = genre_info.name
            genre_remote_id = genre_info.id
            with self.lock:
                genre = self.upsert_genre(genre_name, genre_remote_id)
            result.append(genre)
        return result

    def add_genres_to_categories(self, categories: list[PremierCardgroupCategory], genres: list[PremierCardgroupGenre]):
        for category in categories:
            self.category_genres[category].update(genres)

    def save_genres_in_categories(self):
        for category, genres in self.category_genres.items():
            category.reload()
            if self.can_update(category):
                category.genres = list(genres)
                category.save()
