from mongoengine import StringField

from app.abcvod.core.models.abcvod import (
    AbcvodEpisode,
    AbcvodSeason,
    AbcvodTitle,
)
from app.cms.models import CeleryTaskTracker


class PremierTitleAbstract(AbcvodTitle):
    meta = {
        "abstract": True,
        "indexes": [
            "categories",
            "genres",
        ],
    }
    original_title = StringField()


class PremierSeasonAbstract(AbcvodSeason):
    meta = {
        "abstract": True,
        "indexes": ["title"],
    }


class PremierEpisodeAbstract(AbcvodEpisode):
    meta = {
        "abstract": True,
        "indexes": [
            "title",
            "season",
            "default_caption",
        ],
    }


class PremierExportToClickhouseTracker(CeleryTaskTracker):
    job_group_id = "vod-export-analytics"
