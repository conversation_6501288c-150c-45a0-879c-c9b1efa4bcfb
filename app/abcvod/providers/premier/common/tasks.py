from typing import (
    Dict,
    List,
    Union,
)

import requests
from flask import current_app

from app.abcvod.providers.premier.common.models import (
    PremierEpisodeAbstract,
    PremierTitleAbstract,
)
from app.cms.tasks.utils import BaseExportToClickhouseProcessor


class CommonPremierExportToClickhouseProcessor(BaseExportToClickhouseProcessor):
    title_model: PremierTitleAbstract
    episode_model: PremierEpisodeAbstract
    optimize = True
    CH_TABLE_NAME = "vod_premier_titles_v3"
    CH_CREATE_TABLE = f"""
        CREATE TABLE IF NOT EXISTS {CH_TABLE_NAME}
        (
            title_id String,
            episode_id String,
            title_name String,
            video_id String,
            slug String
        ) ENGINE = ReplacingMergeTree()
        ORDER BY video_id
    """

    def get_video_id_from_api(self, title) -> str:
        resp = requests.get(f"{self.premier_api_url}/metainfo/tv/{title['remote_id']}/video")
        resp_data = resp.json()
        videos = resp_data.get("results", [])
        for video in videos:
            if video.get("type", {}).get("name") == "episode":
                return video.get("id", "")

        return ""

    def setup(self):
        self.CH_URL = current_app.config["CLICKHOUSE_EVENTS_URL"]
        self.CH_DB = current_app.config["CLICKHOUSE_EVENTS_DB"]
        self.premier_api_url = current_app.config["PREMIER_API_URL"]

    def get_data(self) -> List[Dict[str, Union[str, int]]]:
        premier_title_collection = self.title_model._get_collection()
        premier_episode_collection = self.episode_model._get_collection()

        titles = []
        for title in premier_title_collection.find():
            title_data = {
                "title_id": str(title["_id"]),
                "title_name": title["default_caption"],
                "slug": title["slug"],
            }

            if not title.get("is_series"):
                title_data["video_id"] = self.get_video_id_from_api(title)
            else:
                episodes = premier_episode_collection.find({"title": title["_id"]})
                for episode in episodes:
                    episode_data = {
                        **title_data,
                        "video_id": str(episode["remote_id"]),
                        "episode_id": str(episode["_id"]),
                    }
                    titles.append(episode_data)

            titles.append(title_data)
        return titles
