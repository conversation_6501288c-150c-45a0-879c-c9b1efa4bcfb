from __future__ import annotations

import datetime
from enum import Enum
from typing import (
    Generator,
    Optional,
)

from flask import Flask
from pydantic import (
    BaseModel,
    ConfigDict,
    ValidationError,
    field_validator,
)

from app.abcvod.core.api import (
    BaseAPI,
    NotImportant,
)


class TitleType(str, Enum):
    SERIES = "series"
    MOVIES = "movie"
    SHOW = "show"


class ModelWithId(BaseModel):
    model_config = ConfigDict(coerce_numbers_to_str=True)
    id: str  # actually int, but we need str


class GenreInfo(ModelWithId):
    name: str  # 'Драма'
    transliterated_name: str | NotImportant = None  # 'dramy'
    main: bool | NotImportant = None  # True
    url: Optional[str] = None  # None


class RatingInfo(BaseModel):
    rating: float | NotImportant = None  # 0.0
    likes: int | NotImportant = None  # 0
    dislikes: int | NotImportant = None  # 0
    kinopoisk: float | NotImportant = None  # 0
    imdb: float | NotImportant = None  # 0


class BriefTypeInfo(ModelWithId):
    name: str  # 'series'
    title: str  # 'Сериал'


class TypeInfo(ModelWithId):
    name: TitleType  # 'series'
    name_en: str  # 'Series'
    name_plural: str  # 'Сериалы'
    title: str  # 'Сериал'
    serial_content: bool  # True


class CountryInfo(BaseModel):
    two_letter: str  # 'RU'
    name: str  # 'Россия'


class StudioInfo(ModelWithId):
    tv_list: str  # 'https://uma.media/api/metainfo/studio/95/tv'
    name: str  # '123 Продакшн'


class FranchiseInfo(BaseModel):
    pass


class ContentTvTypeInfo(ModelWithId):
    name: str  # 'episode'
    title: str  # 'Полная серия/выпуск'


class PicturesInfo(BaseModel):
    # fmt: off
    banner: str  # "https://pic.uma.media/pic/cardimage/8f/9a/8f9afd3a930d9576e17108121563c805.jpg"  # noqa
    card_group: str  # "https://pic.uma.media/pic/cardimage/93/65/9365e507a70b0213bb00efc51584bba3.jpg"  # noqa
    banner_landscape: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/6f/58/6f58b61ae75cdae6c642a0ece5a949f3.jpg"  # noqa
    g_hasLogo_600x600: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/2f/ee/2feee1bafa57f1d57dc135ccdac292ef.png"  # noqa
    g_hasTitle_logo_1800x1000: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/46/36/46363f052b6ce1f90e0b026287f93c4e.png"  # noqa
    g_iconic_background_1000x1500: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/6c/2c/6c2c3c5c1cb73f5a3755467ebb198e17.jpg"  # noqa
    g_iconic_background_1920x1080: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/88/76/8876fbe2601d016d1c4fb2e4f61b891c.jpg"  # noqa
    g_iconic_background_3840x2160: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/1b/7f/1b7fb859c60e33ee2dea59b63bbec56e.jpg"  # noqa
    g_iconic_poster_1000x1500: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/ce/c5/cec5320895158220e7d0d45940f4f249.jpg"  # noqa
    g_iconic_poster_3840x2160: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/3e/95/3e95151a3c7425a4d914828720520ed8.jpg"  # noqa
    g_iconic_poster_600x600: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/b8/20/b820f7b60c052d71db24f40f0afaa76d.jpg"  # noqa
    g_iconic_poster_600x800: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/93/ae/93aef13d94ab58913a3e09de7c07ea4a.jpg"  # noqa
    g_iconic_poster_800x600: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/d9/e4/d9e454c167badbb7d453320ab37d7509.jpg"  # noqa
    logo_new: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/00/ee/00ee00fd180735736ffc086491116074.png"  # noqa
    tnt_p_square: Optional[str] = None  # "https://pic.uma.media/pic/cardimage/0c/ef/0cefd521a84ae4e3af7494c1dae627fb.jpg  # noqa
    # fmt: on


class ProviderInfo(ModelWithId):
    name: str  # 'Партнеры PREMIER'
    slug: str  # 'premierpartners'


class AuthorInfo(ModelWithId):
    name: str  # 'PREMIER Partners'
    avatar_url: str  # 'https://uma.media/static/img/_thumb_default_00.png'
    site_url: str  # 'https://uma.media/'


class BriefTitleInfo(ModelWithId):
    name: str | NotImportant = None  # 'Полёт'
    absolute_url: str | NotImportant = None  # 'https://uma.media/metainfo/tv/15147/'
    uniform_url: str | NotImportant = None  # 'https://uma.media/metainfo/tv/15147/'
    slug: str | NotImportant = None  # 'polet_'
    description: str | NotImportant = None  # 'Психологическая драма с закрученным сюжетом о том, как один...'
    can_subscribe: bool | NotImportant = None  # False
    subscribers_count: int | NotImportant = None  # 0
    type: BriefTypeInfo | NotImportant = None
    last_updated_ts: str | NotImportant = None  # '2021-04-01T16:00:01.315830'
    video_count: int | NotImportant = None  # 0
    icon: str | NotImportant = None  # ''
    picture: str | NotImportant = None  # 'https://pic.uma.media/pic/cardimage/6a/9d/6a9deba631d605ddb3331...'
    age_restriction: str | NotImportant = None  # '18+'
    rating: RatingInfo | NotImportant = None
    genres: list[GenreInfo] | NotImportant = None
    year_start: str | NotImportant = None  # '2021'
    year_end: str | NotImportant = None  # ''
    countries: list[CountryInfo] | NotImportant = None
    duration: Optional[int] = None  # None
    seasons_count: int | NotImportant = None  # 1
    pictures: PicturesInfo | NotImportant = None
    trailer_id: Optional[str] = None  # '98d8dc530959eb4946e065eb1e8fcd40'
    trailer_url: Optional[str] = None  # 'https://uma.media/video/98d8dc530959eb4946e065eb1e8fcd40/'
    tv_description: str | NotImportant = None  # 'Психологическая драма с закрученным сюжетом о том, как один...'


class ExternalIdInfo(BaseModel):
    code: str  # "kinopoisk"
    external_id: str  # "5325618"


class PersonsInfo(BaseModel):
    actor: list[str] = []  # "Михаил Евланов, Татьяна Казючиц, Владимир Капустин, Анатолий Рыжиков,... Павел Крайнов"
    cinematographer: list[str] = []  # "Дмитрий Просянников"
    composer: list[str] = []  # "Сергей Кондрашов, Дмитрий Полухтин"
    director: list[str] = []  # "Балахонов Александр"
    producer: list[str] = []  # "Роман Елистратов, Олег Бланк"
    screenwriter: list[str] = []  # "Сергей Суржиков"

    # noinspection PyNestedDecorators
    @field_validator("actor", "cinematographer", "composer", "director", "producer", "screenwriter", mode="before")
    @classmethod
    def convert_to_string_list(cls, v: Optional[str]):
        if not v:
            return []
        return v.split(", ")


class TitleInfo(ModelWithId):
    slug: str  # 'polet_'
    content: str  # 'https://uma.media/api/metainfo/tv/15147/video?format=json'
    absolute_url: str | NotImportant = None  # 'https://uma.media/metainfo/tv/15147/'
    uniform_url: str | NotImportant = None  # 'https://uma.media/metainfo/tv/15147/'
    type: TypeInfo
    original_title: str  # 'Полёт'
    countries: list[CountryInfo]
    genres: list[GenreInfo]
    year: str | NotImportant = None  # '2021', may also be ''
    year_start: str  # '2021',  may also be ''
    year_end: str  # ''
    is_active: bool | NotImportant = None  # True
    related_showcase: Optional[str] = None
    age_restriction: str  # '18+'
    smoking_restriction: Optional[str] = None
    slogan: str | NotImportant = None
    poster_url: Optional[str] = None  # None
    studios: list[StudioInfo] | NotImportant = None
    keywords: str | NotImportant = None  # 'палет полет gfktn gjktn федорович никишов ефремов табаков акиньшина ...'
    external_ids: Optional[list[ExternalIdInfo]] = None  # []
    provider: ProviderInfo | NotImportant = None
    has_allow_download: bool | NotImportant = None  # False
    rating: Optional[RatingInfo] = None
    contenttvstype: list[ContentTvTypeInfo] | NotImportant = None
    last_updated_ts: str | NotImportant = None  # '2021-04-01T16:00:01.315830'
    labels: list | NotImportant = None  # []
    local_release: Optional[str] = None  # None
    global_release: Optional[str] = None  # None
    accessibility: str | NotImportant = None  # 'free'
    last_video_add_ts: str | NotImportant = None  # '2021-04-01T16:00:01.903879'
    sensitive_content: bool | NotImportant = None  # False
    restriction_notices: list | NotImportant = None  # 0} []
    seasons_count: int | NotImportant = None  # 1
    franchises: list[FranchiseInfo] | NotImportant = None
    pictures: PicturesInfo
    name: str  # 'Полёт'
    can_subscribe: bool | NotImportant = None  # False
    description: str  # 'Психологическая драма с закрученным сюжетом о том, как один несостоявшийся поле...'
    picture: str | NotImportant = None  # 'https://pic.uma.media/pic/cardimage/6a/9d/6a9deba631d605ddb33312b...'
    persons: Optional[PersonsInfo] = None


class ContentTypeInfo(ModelWithId):
    app_label: str  # 'metainfo'
    model: str  # 'tv'


class TitleVideoInfo(ModelWithId):
    content_type: ContentTypeInfo | NotImportant = None
    object_id: str  # actually int, but we need str 15147
    object: BriefTitleInfo | NotImportant = None
    last_updated_ts: str | NotImportant = None  # '2021-04-01T16:00:01.315830'
    external_ids: list | NotImportant = None  # []
    labels: list | NotImportant = None  # []
    name: str | NotImportant = None  # 'ТЕСТ_Партнеры'
    additional_name: Optional[str] = None  # 'тест без ДРМ'
    is_adult: bool | NotImportant = None  # True
    genres: list[GenreInfo] | NotImportant = None
    rating: RatingInfo | NotImportant = None


class ResourceInfo(ModelWithId):
    created_ts: str | NotImportant = None  # '2021-07-26T12:15:40.061734'
    content_type: ContentTypeInfo | NotImportant = None
    object_id: str  # '628'
    order_number: int | NotImportant = None  # 1
    url: str | NotImportant = None  # 'https://uma.media/api/feeds/cardgroup/628'
    extra_params: dict | NotImportant = None  # {}
    inline_widget: bool | NotImportant = None  # True
    name: str  # 'Новинки'
    site_url: str | NotImportant = None  # 'https://uma.media/api/feeds/cardgroup/628'
    playlist_name: str | NotImportant = None  # 'Новинки'
    additional_name: Optional[str] = None  # None
    slug: str | NotImportant = None  # 'novinki'
    external_name: bool | NotImportant = None  # False
    auth_visibility: int | NotImportant = None  # 0
    subscription_visibility: int | NotImportant = None  # 0


class CategoryInfo(BaseModel):
    id: int  # 1906
    name: str  # 'Новинки'
    sort: str | NotImportant = None  # 'original'
    order_number: int | NotImportant = None  # 1
    resources: list[ResourceInfo]
    link: Optional[str] = None  # None
    slug: Optional[str] = None  # None
    hide_tags: bool | NotImportant = None  # False
    hide_authors: bool | NotImportant = None  # False
    extra_title: Optional[str] = None  # None
    extra_description: str | NotImportant = None  # ''


class TagInfo(BaseModel):
    comment: str | NotImportant  # ''
    id: int | NotImportant  # 87
    label: str | None | NotImportant  # None
    name: str | NotImportant  # 'test'
    url: str | NotImportant  # 'https://uma.media/tags/video/87/'


class EpisodeInfo(BaseModel):
    id: str  # '63a3ffd3eabc4235268bc439ad4c4fcc'
    title: str  # '1 серия'
    description: str  # 'Сотрудники юридического отдела строительной компании «БИГ» собираются в...'
    description_editors: str | NotImportant = None  # ''
    thumbnail_url: str  # 'https://pic.uma.media/pic/video/0f/a6/0fa6574775a552a1028786e697099fb7.jpg'
    created_ts: str | NotImportant = None  # '2021-01-29T15:10:29'
    video_url: str | NotImportant = None  # 'https://uma.media/video/63a3ffd3eabc4235268bc439ad4c4fcc/'
    track_id: int | NotImportant = None  # 308509
    hits: int | NotImportant = None  # 0
    duration: int | NotImportant = None  # 2992
    picture_url: str | NotImportant = None  # ''
    author: AuthorInfo | NotImportant = None
    is_adult: bool | NotImportant = None  # False
    publication_ts: str | NotImportant = None  # '2021-01-29T15:10:29.548673'
    hashtags: list[str] | NotImportant = None  # []
    is_livestream: bool | NotImportant = None  # False
    comments_count: int | NotImportant = None  # 0
    season: int  # 1
    episode: int  # 1
    fragment: int | NotImportant = None  # 0
    release: datetime.datetime  # '2021-01-25T21:00:01'
    announcement: Optional[str] = None  # None
    type: BriefTypeInfo
    allow_download: bool | NotImportant = None  # False
    all_tags: list[TagInfo] | NotImportant = None  # []
    allow_comment: bool | NotImportant = None  # False
    is_clickable: bool | NotImportant = None  # True
    title_for_card: str | NotImportant = None  # ''
    title_for_player: str | NotImportant = None  # ''


class BalancerInfo(BaseModel):
    default: str  # 'https://vb4.uma.media/1699471902/q4iN6i06WAXz9CX0OMaZ-A/vod/vod:premierpartners/54512...'


class PlaybackInfo(BaseModel):
    video_balancer: BalancerInfo | NotImportant = None
    extended_balancer: dict | NotImportant = None  # {}
    live_streams: dict | NotImportant = None  # {}
    audio_lang: list | NotImportant = None  # []


class ErrorInfo(BaseModel):
    """For internal usage in importers, does not describe API."""

    model_config = ConfigDict(coerce_numbers_to_str=True)

    title_remote_id: str
    error_text: str

    def get_error_message(self):
        return f"title_remote_id = {self.title_remote_id} -> {self.error_text}"


class PremierAPI(BaseAPI):
    """Class for connection to Premier API."""

    default_request_params = {"format": "json"}

    def __init__(self, platform, api_url, http_retries, referer):
        """Init.

        :param api_url: Base URL for API
        :param platform: platform parameter for access API
        :param http_retries: Retries count for HTTP requests
        :param referer: referer, that helps filter unavailable content. See #63920
        """
        super().__init__(http_retries=http_retries)
        # Extra get parameters, which may be required for SOME requests.
        self.referer_request_params = {"referer": referer}
        self.platform_request_params = {"platform": platform}
        self.api_url = api_url

    def get_categories_info(self, showcase_id) -> list[CategoryInfo]:
        """Get all categories from Premier API."""
        url = f"{self.api_url}/feeds/{showcase_id}/"
        response = self.get_json_response(url=url)
        return [CategoryInfo(**info) for info in response["tabs"]]

    def _get_all_info_from_all_pages(self, url, extra_get_params: Optional[dict] = None) -> Generator[dict, None, None]:
        page = 1
        has_next = True
        while has_next:
            get_params = {"page": page}
            if extra_get_params:
                get_params.update(extra_get_params)
            response = self.get_json_response(url=url, get_params=get_params)
            info_list, has_next = response["results"], response["next"]
            for info_item in info_list:
                yield info_item
            page += 1

    def get_cardgroup_titles_info(self, cardgroup_id) -> Generator[TitleVideoInfo | ErrorInfo, None, None]:
        """Get brief info about all titles from Premier API."""
        url = f"{self.api_url}/feeds/cardgroup/{cardgroup_id}/"
        for info in self._get_all_info_from_all_pages(url, extra_get_params=self.referer_request_params):
            try:
                yield TitleVideoInfo(**info)
            except ValidationError as e:
                yield ErrorInfo(title_remote_id=info["id"], error_text=str(e))

    def get_title_info(self, remote_id) -> TitleInfo:
        """Get full info about certain title."""
        return TitleInfo(**self.get_title_info_raw(remote_id))

    def get_title_info_raw(self, remote_id) -> dict:
        url = f"{self.api_url}/metainfo/tv/{remote_id}/"
        return self.get_json_response(url=url)

    def get_episodes_info(self, title_remote_id: str) -> Generator[EpisodeInfo | ErrorInfo, None, None]:
        for data in self.get_episodes_info_raw(title_remote_id):
            try:
                yield EpisodeInfo(**data)
            except ValidationError as e:
                yield ErrorInfo(title_remote_id=title_remote_id, error_text=f"Bad episode {data['id']} data: {e}")

    def get_episodes_info_raw(self, title_remote_id: str) -> Generator[dict, None, None]:
        url = f"{self.api_url}/metainfo/tv/{title_remote_id}/video/"
        return self._get_all_info_from_all_pages(url, extra_get_params=self.referer_request_params)

    def get_playback_url(self, video_id: str) -> str:
        """Return video url from Premier API.

        :param video_id: video id in Premier
        """
        url = f"{self.api_url}/play/stream/{video_id}/"
        playback_info = PlaybackInfo(**self.get_json_response(url=url, get_params=self.platform_request_params))
        return playback_info.video_balancer.default


def init_premier_api(app: Flask):
    """Create PremierAPI client using app's config and adds it to app's extensions under `premier` key.

    :param app: Flask application.
    """
    if not app.config["PREMIER_API_REFERER"]:
        raise ValueError("PREMIER_API_REFERER config parameter is required")
    premier_api = PremierAPI(
        platform=app.config["PREMIER_PLATFORM"],
        api_url=app.config["PREMIER_API_URL"],
        http_retries=app.config["PREMIER_MAX_RETRIES"],
        referer=app.config["PREMIER_API_REFERER"],
    )

    app.extensions = getattr(app, "extensions", {})
    app.extensions["premier_api"] = premier_api
