from __future__ import annotations

from abc import ABC
from datetime import datetime
from functools import lru_cache
from typing import (
    Generator,
    Optional,
    Type,
)

from flask import Flask
from mongoengine import (
    DoesNotExist,
    MultipleObjectsReturned,
)
from requests import HTTPError
from slugify import slugify

from app.abcvod.core.importer import (
    DoNotAutoupdateMixin,
    ImporterWithImages,
)
from app.abcvod.core.models.abcvod import AbcvodGenre
from app.abcvod.core.models.task import AbcvodImportTracker
from app.abcvod.models import (
    AbcvodCountry,
    AbcvodPerson,
)
from app.abcvod.providers.premier.common.api import (
    EpisodeInfo,
    ErrorInfo,
    PremierAPI,
    TitleInfo,
    TitleType,
)
from app.abcvod.providers.premier.common.models import (
    PremierEpisodeAbstract,
    PremierSeasonAbstract,
    PremierTitleAbstract,
)
from app.common.utils import require_attribute
from cmf.core.fields import MultilangString

EXTERNAL_ID_CODE_KINOPOISK = "kinopoisk"
EXTERNAL_ID_CODE_IMDB = "imdb"


class BasePremierImporter(DoNotAutoupdateMixin, ImporterWithImages, ABC):
    """Class for importing Premier content."""

    # These attributes are required for automatic unpublish in the end.
    title_model: Type[PremierTitleAbstract]
    episode_model: Type[PremierEpisodeAbstract]
    season_model: Type[PremierSeasonAbstract]
    genre_model: Type[AbcvodGenre]

    def __init__(
        self,
        *,
        app: Flask,
        images_path: str,
        task_tracker: AbcvodImportTracker | None = None,
        task_trigger: str | None = None,
    ):
        super().__init__(
            task_tracker=task_tracker,
            task_trigger=task_trigger,
            storage=app.extensions["storage"],
            max_workers=app.config["PREMIER_MAX_WORKERS"],
            http_retries=app.config["PREMIER_MAX_RETRIES"],
        )
        require_attribute(self, "title_model", check_type=False)
        require_attribute(self, "episode_model", check_type=False)
        require_attribute(self, "season_model", check_type=False)

        self.premier_api: PremierAPI = app.extensions["premier_api"]
        self.images_path = images_path
        self.actual_genres_remote_ids = set()  # genres, that were met in PremierAPI during current import
        self.countries_cache = {country.iso2: country for country in AbcvodCountry.objects.all()}
        self.persons_cache: dict[str, AbcvodPerson] = {
            person.id: person for person in AbcvodPerson.objects.filter(premier_id__exists=True)
        }

    def collect_cardgroup_title_ids(self, cardgroup_id) -> Generator[str, None, None]:
        # FIXME: This is an artifact of the past, the idea is lost, but algorithm still need this.
        all_info = self.premier_api.get_cardgroup_titles_info(cardgroup_id=cardgroup_id)
        for title_video_info in all_info:
            if isinstance(title_video_info, ErrorInfo):
                self.add_import_error("Horizontal poster error", error_message=title_video_info.get_error_message())
                continue
            yield title_video_info.object_id

    def before_import(self):
        super(BasePremierImporter, self).before_import()
        # Setup countries cache
        self.countries_cache = {country.iso2: country for country in AbcvodCountry.objects.all()}

    def get_country(self, title_remote_id, country_two_letter_code) -> Optional[AbcvodCountry]:
        try:
            return self.countries_cache[country_two_letter_code]
        except KeyError:
            self.add_import_error(
                "Unknown country", f"Unexpected country code - {country_two_letter_code}, found at {title_remote_id=}"
            )

    def upsert_episode(self, episode_info: EpisodeInfo, season: PremierSeasonAbstract) -> PremierEpisodeAbstract:
        """Import episode.

        :param episode_info: Dict with episode info from premier api
        :param season: Season, which this episode belongs to
        """
        remote_id = episode_info.id
        try:
            episode = self.episode_model.objects.get(remote_id=remote_id)
        except DoesNotExist:
            episode = self.episode_model(remote_id=remote_id, created_during_import=self.uid)

        episode.is_published = True
        episode.updated_during_import = self.uid
        if not self.can_update(episode):
            return episode.save()

        title: PremierTitleAbstract = season.title
        title_name = title.default_caption

        storage_dir = f"{self.images_path}/{title.remote_id}/{season.number}"

        poster_path = self.delay_download_image_auto_name(storage_dir, episode_info.thumbnail_url)
        slug = f"{slugify(title_name)}_{episode_info.season}_{episode_info.episode}_{episode_info.id}"

        episode.slug = slug
        episode.poster = poster_path
        episode.number = episode_info.episode
        episode.caption = MultilangString(episode_info.title)
        episode.default_caption = episode_info.title
        episode.description = MultilangString(episode_info.description)
        episode.release_date = episode_info.release
        episode.title = season.title
        episode.season = season
        episode.playback_url = self.get_playback_url(remote_id)
        episode.save()

    @lru_cache
    def upsert_season(self, season_number: int, title: PremierTitleAbstract) -> PremierSeasonAbstract:
        """Import season.

        :param season_number: number of season
        :param title: title of the season
        """
        try:
            season = self.season_model.objects.get(title=title, number=season_number)
        except DoesNotExist:
            season = self.season_model(title=title, number=season_number, created_during_import=self.uid)
        except MultipleObjectsReturned:
            # Happens when there is nonsense in database.
            self.add_warning(f"Title '{title}' had multiple seasons #{season_number} in database.")
            season = self.season_model.objects(title=title, number=season_number).first()
            for candidate_season in self.season_model.objects(title=title, number=season_number):
                if candidate_season.id == season.id:
                    self.add_log(f"Delete duplicate season #{season_number} for title '{title}'")
                    candidate_season.delete()

        season.is_published = True
        season.updated_during_import = self.uid
        if not self.can_update(season):
            return season.save()

        season.slug = f"{slugify(title.default_caption)}_{season_number}"
        season.caption = f"Сезон {season_number}"
        season.title = title
        return season.save()

    def upsert_person(self, name: str):
        if name in self.persons_cache:
            return self.persons_cache[name]
        with self.lock:
            person = AbcvodPerson.get_or_create_person(name=name, premier_id=name)
            self.persons_cache[name] = person
            return person

    def get_persons(self, names: list[str]) -> list[AbcvodPerson]:
        return [self.upsert_person(name) for name in names]

    def get_playback_url(self, video_id: str) -> str:
        # This API method may return 404, that's OK.
        try:
            return self.premier_api.get_playback_url(video_id)
        except HTTPError:
            return ""

    def import_seasons_and_episodes(self, title, title_videos_info: list[EpisodeInfo]):
        release_date = None

        for episode_info in title_videos_info:
            season_num = episode_info.season
            with self.lock:
                season = self.upsert_season(season_number=season_num, title=title)

            self.upsert_episode(episode_info=episode_info, season=season)

            # Get release date of the first episode
            if not release_date or episode_info.release < release_date:
                release_date = episode_info.release

        if release_date and self.can_update(title):
            title.release_date = release_date
            title.save()

    def get_movie_release_date(self, title_videos_info: list[EpisodeInfo]):
        release_dates = [video_info.release for video_info in title_videos_info if video_info.type.name == "episode"]
        if not release_dates:
            release_dates = [video_info.release for video_info in title_videos_info]
        return max(release_dates) if release_dates else None

    def upsert_title(
        self,
        remote_id: str,
        title_info: TitleInfo,
        episodes_info: list[EpisodeInfo],
        categories: list,
        genres: list,
    ) -> PremierTitleAbstract:
        try:
            title = self.title_model.objects.get(remote_id=remote_id)
        except DoesNotExist:
            title = self.title_model(remote_id=remote_id, created_during_import=self.uid)

        title.is_published = True
        title.updated_during_import = self.uid
        if not self.can_update(title):
            return title.save()

        # Get all images for title
        pictures = title_info.pictures
        storage_dir = f"{self.images_path}/{remote_id}"
        vertical_poster_path = self.delay_download_image_auto_name(storage_dir, pictures.card_group)
        horizontal_poster_path = self.delay_download_image_auto_name(storage_dir, pictures.banner)

        background_poster_path = None
        background_poster_url = pictures.g_iconic_background_3840x2160 or pictures.g_iconic_background_1920x1080
        if background_poster_url:
            background_poster_path = self.delay_download_image_auto_name(storage_dir, background_poster_url)

        title_logo_path = None
        if title_logo_url := pictures.g_hasTitle_logo_1800x1000:
            title_logo_path = self.delay_download_image_auto_name(storage_dir, title_logo_url)

        # Get years for title
        years = []
        year_start = title_info.year_start or None
        year_end = title_info.year_end or None
        if year_start and year_end:
            years = list(range(int(year_start), int(year_end) + 1))
        elif year_start:
            years = [year_start]

        # Find out if title is series
        title_type = title_info.type.name
        is_series = title_type in (TitleType.SERIES, TitleType.SHOW)

        title.remote_id = remote_id
        title.slug = title_info.slug
        title.original_title = title_info.original_title
        title.caption = MultilangString(title_info.name)
        title.default_caption = title_info.name
        title.description = MultilangString(title_info.description)
        title.poster = vertical_poster_path
        title.poster_horizontal = horizontal_poster_path
        title.poster_background = background_poster_path
        title.title_logo = title_logo_path
        title.age_rating = get_digits(title_info.age_restriction)
        title.years = years
        title.categories = categories
        title.is_series = is_series
        title.updated_at = datetime.utcnow()
        title.countries = [
            self.get_country(remote_id, country_info.two_letter) for country_info in title_info.countries
        ]

        title.genres = genres

        self.set_title_external_ratings(title, title_info)

        if persons := title_info.persons:
            title.actors = self.get_persons(persons.actor)
            title.directors = self.get_persons(persons.director)

        if not is_series and episodes_info:
            video_id = episodes_info[0].id
            title.playback_url = self.get_playback_url(video_id)
            title.release_date = self.get_movie_release_date(episodes_info)
        if not is_series and not episodes_info:
            # This should not be happening and, probably, must be reported.
            self.add_import_error(
                "No playback info",
                f"No title_videos_info found for title {remote_id} -> can't get 'playback_url' and 'release_date'",
            )
        return title.save()

    def set_title_external_ratings(self, title: PremierTitleAbstract, title_info: TitleInfo):
        """Set external IDs and ratings from Kinopoisk and IMDb.

        :param title: Title object to update
        :param title_info: Title info from Premier API
        """
        if title_info.external_ids:
            for ext in title_info.external_ids:
                external_id = ext.external_id
                code = ext.code

                if not code or not external_id:
                    continue

                code_lc = code.lower()
                if code_lc == EXTERNAL_ID_CODE_KINOPOISK:
                    if kp_digits := get_digits(str(external_id)):
                        title.kp_id = int(kp_digits)
                elif code_lc == EXTERNAL_ID_CODE_IMDB:
                    title.imdb_id = str(external_id)

        if title_info.rating:
            if title_info.rating.imdb is not None:
                title.imdb_rating = float(title_info.rating.imdb)

            if title_info.rating.kinopoisk is not None:
                title.kp_rating = float(title_info.rating.kinopoisk)

    def delete_absent_genres(self):
        """Delete genres if they were deleted from premier database."""
        for genre in self.genre_model.objects():
            if genre.remote_id and genre.remote_id not in self.actual_genres_remote_ids:
                genre.delete()

    def after_import(self):
        super(BasePremierImporter, self).after_import()
        self.delete_absent_genres()
        self.mark_unpublished(
            {
                "Titles": self.title_model,
                "Episodes": self.episode_model,
                "Seasons": self.season_model,
            },
        )


def get_digits(s: str):
    number = ""
    for letter in s:
        if letter.isdigit():
            number += letter
    return number
