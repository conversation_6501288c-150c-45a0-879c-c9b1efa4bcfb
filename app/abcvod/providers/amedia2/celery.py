from flask import current_app

from app.abcvod.providers.amedia2.importer import Amedia2Importer
from app.cms.models import CeleryTaskTracker
from app.cms.tasks.utils import cms_task


@cms_task(task_processor_class=Amedia2Importer)
def amedia2_import_task(task_trigger: str, task_tracker: CeleryTaskTracker = None):
    importer = Amedia2Importer(app=current_app, task_trigger=task_trigger, task_tracker=task_tracker)
    importer.run()
