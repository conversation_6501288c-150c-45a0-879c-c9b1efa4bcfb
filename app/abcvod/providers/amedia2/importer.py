from __future__ import annotations

import hashlib
import logging
from datetime import (
    datetime,
    timezone,
)
from typing import TypedDict

from flask import Flask
from fs.base import FS
from mongoengine import DoesNotExist
from slugify import slugify

from app.abcvod.core.importer import (
    DoNotAutoupdateMixin,
    ImporterWithImages,
)
from app.abcvod.core.models.abcvod import (
    AbcvodCountry,
    AbcvodPerson,
)
from app.abcvod.models import AbcvodCopyrightHolder
from app.common.utils import BulkUpdate

from .api import (
    Amedia2API,
    Amedia2CatalogueType,
    AssetsInfo,
    EpisodeInfo,
    GenreInfo,
    PersonInfo,
    PersonType,
    RecommendationInfo,
    SeasonInfo,
    TitleInfo,
    TitleType,
)
from .models import (
    Amedia2Category,
    Amedia2Collection,
    Amedia2Episode,
    Amedia2Genre,
    Amedia2ImportTracker,
    Amedia2Season,
    Amedia2SimilarTitleReference,
    Amedia2Title,
    AmediaImage,
)

logger = logging.getLogger(__name__)


class DelayedEpisode(TypedDict):
    title: Amedia2Title
    season: Amedia2Season | None
    episode_info: EpisodeInfo


class Amedia2Importer(DoNotAutoupdateMixin, ImporterWithImages):
    """Class for importing Amedia content."""

    task_tracker_model = Amedia2ImportTracker
    title_model = Amedia2Title
    collection_model = Amedia2Collection

    def __init__(
        self, *, app: Flask, task_tracker: Amedia2ImportTracker | None = None, task_trigger: str | None = None
    ):
        storage: FS = app.extensions["storage"]
        max_workers = app.config["AMEDIA_MAX_WORKERS"]
        http_retries = app.config["AMEDIA_MAX_RETRIES"]
        super().__init__(
            storage=storage,
            max_workers=max_workers,
            http_retries=http_retries,
            task_tracker=task_tracker,
            task_trigger=task_trigger,
        )

        self.amedia_api: Amedia2API = app.extensions["amedia2_api"]
        self.images_path = app.config["AMEDIA_STORAGE_FOLDER"]
        self.amedia_grant = app.config["AMEDIA_GRANT"]
        self.import_datetime = datetime.now(tz=timezone.utc)

        # Make cache for persons and countries
        persons = AbcvodPerson.objects.filter(amedia_id__exists=True)
        self.persons_cache = {person.id: person for person in persons}
        self.countries_cache = {country.name: country for country in AbcvodCountry.objects.all()}
        self.genres_cache = {genre.remote_id: genre for genre in Amedia2Genre.objects.all()}
        self.copyright_holders_cache = {holder.slug: holder for holder in AbcvodCopyrightHolder.objects.only("slug")}
        self.categories_cache: dict[Amedia2CatalogueType:Amedia2Category] = {}  # {category_name: category}
        self.titles_cache: dict[str, Amedia2Title] = {}  # {title_remote_id: title}
        self.similar_titles_cache: dict[str, list[RecommendationInfo]] = {}
        self.delayed_title_ids: list[str] = []
        self.delayed_episodes: list[DelayedEpisode] = []
        self.delayed_seasons: list[TitleInfo] = []  # TitleInfo contains everything we need for import all seasons.
        self.delayed_title_updates: list[dict] = []

        self.not_enough_grants_ids: set[str] = set()  # #113227 - titles, that were not imported due to lack of grants.

    def _upsert_category(self, catalogue_type: Amedia2CatalogueType) -> Amedia2Category:
        try:
            category = Amedia2Category.objects.get(slug=catalogue_type)
        except DoesNotExist:
            category = Amedia2Category(slug=catalogue_type, created_during_import=self.uid)

        category.is_published = True
        category.updated_during_import = self.uid
        if not self.can_update(category):
            return category.save()

        category.caption = catalogue_type
        return category.save()

    def setup_categories(self):
        """Create categories in mongoDB if not present."""
        for catalogue_type in Amedia2CatalogueType:
            catalogue_type: Amedia2CatalogueType  # for some reason my linter does not get this
            self.categories_cache[catalogue_type] = self._upsert_category(catalogue_type)

    def _upsert_genre(self, genre_info: GenreInfo):
        try:
            if genre_info.id in self.genres_cache:
                genre = self.genres_cache[genre_info.id]
            else:
                genre = Amedia2Genre.objects.get(remote_id=genre_info.id)
        except DoesNotExist:
            genre = Amedia2Genre(remote_id=genre_info.id, created_during_import=self.uid)

        genre.is_published = True
        genre.updated_during_import = self.uid
        if not self.can_update(genre):
            return genre.save()

        genre.slug = slugify(genre_info.name)
        genre.caption = genre_info.name
        return genre.save()

    def import_genres(self):
        """Imports all genres from Amedia API.

        Genres which are already in mongoDB are not replaced; new genres are added; absent genres are deleted.
        """
        for category in self.categories_cache.values():
            genres_info_list = self.amedia_api.get_genres_info_list(category.slug)
            category_genres = []
            for genre_info in genres_info_list:
                genre = self._upsert_genre(genre_info)
                category_genres.append(genre)
                self.genres_cache[genre.remote_id] = genre
            if not self.can_update(category):
                continue
            category.genres = category_genres
            category.save()

    def get_images(self, assets_info: AssetsInfo, storage_dir: str) -> list[AmediaImage]:
        """Collect object images and queue them for further download.

        :param assets_info: info about all images
        :param storage_dir: directory in storage to save assets
        """
        asset_paths = []
        for image_name, image_url in assets_info.model_dump().items():
            if not image_url:
                continue
            url_md5 = hashlib.md5(image_url.encode("utf-8")).hexdigest()
            file_name = f"{url_md5}.jpg"
            storage_path = self.delay_download_image(storage_dir=storage_dir, file_name=file_name, image_url=image_url)
            asset_paths.append(AmediaImage(name=image_name, path=storage_path))
        return asset_paths

    def delay_episode_import(
        self, *, title: Amedia2Title, season: Amedia2Season | None = None, episode_info: EpisodeInfo
    ):
        self.delayed_episodes.append(DelayedEpisode(title=title, season=season, episode_info=episode_info))

    def import_episode(self, title: Amedia2Title, season: Amedia2Season | None, episode_info: EpisodeInfo):
        """Uniformity method."""
        self._upsert_episode(title, season, episode_info)

    def _upsert_episode(
        self,
        title: Amedia2Title,
        season: Amedia2Season | None,
        episode_info: EpisodeInfo,
    ) -> Amedia2Episode:
        """Imports episode."""
        try:
            episode = Amedia2Episode.objects.get(remote_id=episode_info.id)
        except DoesNotExist:
            episode = Amedia2Episode(remote_id=episode_info.id, created_during_import=self.uid)

        episode.is_published = episode_info.videoPublished
        episode.updated_during_import = self.uid
        if not self.can_update(episode):
            return episode.save()

        # upload images
        images_dir = f"{self.images_path}/{title.remote_id}/{season.remote_id if season else 0}/{episode_info.id}"
        asset_paths = self.get_images(assets_info=episode_info.assets, storage_dir=images_dir)

        poster = ""
        for asset in asset_paths:
            if asset["name"] == "stopKadr":
                poster = asset["path"]

        episode.remote_id = episode_info.id
        episode.title = title
        episode.season = season
        episode.original_title = episode_info.originalTitle
        episode.slug = slugify(f"{episode_info.originalTitle}_{episode_info.id}")
        episode.caption = episode_info.title
        episode.poster = poster
        episode.images = asset_paths
        episode.release_date = episode_info.publishDate
        episode.number = episode_info.number
        episode.description = episode_info.shortDescription
        episode.duration = int(episode_info.duration)
        return episode.save()

    def import_season(self, remote_id: str, title: Amedia2Title) -> Amedia2Season:
        with self.log_import_error(custom_message=f"Failed to import season with remote_id {remote_id}"):
            return self._import_season(remote_id=remote_id, title=title)

    def _upsert_season(self, title: Amedia2Title, season_info: SeasonInfo) -> Amedia2Season:
        try:
            season = Amedia2Season.objects.get(remote_id=season_info.id)
        except DoesNotExist:
            season = Amedia2Season(remote_id=season_info.id, created_during_import=self.uid)

        season.is_published = False
        for episode_info in season_info.episodes:
            if episode_info.videoPublished:
                season.is_published = True
                break
        season.updated_during_import = self.uid
        if not self.can_update(season):
            return season.save()

        # upload images
        images_dir = f"{self.images_path}/{title.remote_id}/{season_info.id}"
        asset_paths = self.get_images(assets_info=season_info.assets, storage_dir=images_dir)

        season.caption = season_info.title
        season.original_title = season_info.originalTitle
        season.slug = slugify(f"{season_info.originalTitle}_{season_info.id}")
        season.title = title
        season.images = asset_paths
        season.number = season_info.seasonNumber
        season.description = season_info.shortDescription
        season.premier_year = season_info.premierYear
        return season.save()

    def _import_season(self, remote_id: str, title: Amedia2Title) -> Amedia2Season:
        season_info = self.amedia_api.get_season_info(remote_id)
        season: Amedia2Season = self._upsert_season(title, season_info)
        for episode_info in season_info.episodes:
            self.delay_episode_import(title=title, season=season, episode_info=episode_info)
        return season

    def get_genres(self, title_info: TitleInfo) -> list[Amedia2Genre]:
        return [self.genres_cache[genre_info.id] for genre_info in title_info.genres]

    def upsert_title(self, title_info: TitleInfo, catalogue_type: Amedia2CatalogueType) -> Amedia2Title:
        with self.log_import_error(custom_message=f"Failed to update or create title with remote_id '{title_info.id}'"):
            return self._upsert_title(title_info, catalogue_type)

    def get_title_is_published(self, title_info: TitleInfo) -> bool:
        """Get preliminary information about title is published or not.

        This is subject of change during import of episodes. If no episodes are published - title will be unpublished.
        """
        if title_info.type == TitleType.tvmovie:
            return title_info.episodes[0].videoPublished
        elif title_info.type == TitleType.movie and title_info.episodes:
            return title_info.episodes[0].videoPublished
        elif title_info.type == TitleType.series:
            for season_info in title_info.seasons:
                if season_info.published:
                    return True
            return False
        return True

    def get_title_duration(self, title_info: TitleInfo) -> int | None:
        if title_info.type == TitleType.movie:
            return int(title_info.episodes[0].duration)
        elif title_info.type == TitleType.tvmovie:
            return int(sum(episode.duration for episode in title_info.episodes))

    def _upsert_title(self, title_info: TitleInfo, catalogue_type: Amedia2CatalogueType) -> Amedia2Title:
        try:
            title = Amedia2Title.objects.get(remote_id=title_info.id)
        except DoesNotExist:
            title = Amedia2Title(remote_id=title_info.id, created_during_import=self.uid)

        title.is_published = self.get_title_is_published(title_info)
        title.updated_during_import = self.uid
        if not self.can_update(title):
            return title.save()

        # upload images
        images_dir = f"{self.images_path}/{title_info.id}"
        asset_paths = self.get_images(assets_info=title_info.assets, storage_dir=images_dir)

        self._set_title_posters_info(title, asset_paths)

        title.is_hbo = False
        for studio_info in title_info.studios:
            if studio_info.name == "HBO":
                title.is_hbo = True
                break

        title.categories = [self.categories_cache[catalogue_type]]
        title.remote_id = title_info.id
        title.caption = title_info.title
        title.original_title = title_info.originalTitle
        title.slug = slugify(f"{title_info.originalTitle}_{title_info.id}")
        title.genres = self.get_genres(title_info)
        title.images = asset_paths
        title.description = title_info.shortDescription
        title.years = [title_info.premierYear]
        title.is_series = title_info.type == TitleType.series or title_info.type == TitleType.tvmovie
        title.age_rating = title_info.ageRestrictions
        title.imdb_rating = title_info.ratingImdb
        title.kp_rating = title_info.ratingKinopoisk
        title.countries = self.get_title_countries(title_info)
        title.directors = self.get_persons(title_info, PersonType.director)
        title.actors = self.get_persons(title_info, PersonType.actor)
        title.copyright_holders = self.get_copyright_holders(title_info)
        title.release_date = title_info.publicationDate
        title.duration = self.get_title_duration(title_info)
        title.title_type = title_info.type
        return title.save()

    def import_seasons_for_title(self, title_info: TitleInfo):
        years = set()
        title_is_published = False
        title = self.titles_cache[title_info.id]
        if title_info.seasons:
            for season_info in title_info.seasons:
                season = self.import_season(season_info.id, title)
                if season.premier_year:
                    years.add(season.premier_year)
                if season.is_published:
                    title_is_published = True

        delayed_title_update = {}
        if title.is_published != title_is_published:
            delayed_title_update["is_published"] = title_is_published
        if set(title.years) != set(years):
            delayed_title_update["years"] = sorted(years)

        if delayed_title_update:
            delayed_title_update["id"] = title.id
            self.delayed_title_updates.append(delayed_title_update)

    def get_or_create_person(self, person_info: PersonInfo) -> AbcvodPerson:
        """Get person from existing cache or create new AbcvodPerson."""
        if person_info.id in self.persons_cache:
            return self.persons_cache[person_info.id]
        with self.lock:
            person = AbcvodPerson.get_or_create_person(name=person_info.name, amedia_id=person_info.id)
            self.persons_cache[person_info.id] = person
            return person

    def get_copyright_holders(self, title_info: TitleInfo) -> list[AbcvodCopyrightHolder]:
        """Get or create copyright holders from studio names."""
        result = []

        for studio_info in title_info.studios:
            slug = slugify(studio_info.name)

            with self.lock:
                if slug in self.copyright_holders_cache:
                    copyright_holder = self.copyright_holders_cache[slug]
                else:
                    copyright_holder = AbcvodCopyrightHolder(name=studio_info.name)
                    copyright_holder.save()

                    self.copyright_holders_cache[slug] = copyright_holder

            result.append(copyright_holder)

        return result

    def import_titles(self):
        """Imports all films and series and dependent objects."""
        for catalogue_type in Amedia2CatalogueType:
            catalogue_type: Amedia2CatalogueType  # my linter does not recognize this.
            with self.bunch_of_tasks() as submit:
                for brief_title_info in self.amedia_api.get_all_titles_info(catalogue_type):
                    submit(self.import_title, brief_title_info.id, catalogue_type)

    def import_seasons(self):
        with self.bunch_of_tasks() as submit:
            for title_info in self.delayed_seasons:
                submit(self.import_seasons_for_title, title_info)

    def import_episodes(self):
        with self.bunch_of_tasks() as submit:
            for delayed_episode in self.delayed_episodes:
                delayed_episode: DelayedEpisode
                submit(self.import_episode, **delayed_episode)

    def import_procedure(self):
        # base import
        self.setup_categories()
        self.import_genres()
        self.import_titles()
        self.import_seasons()
        self.import_episodes()

    def after_import(self):
        super(Amedia2Importer, self).after_import()
        self.run_delayed_title_updates()
        remote_ids = ", ".join(sorted(self.not_enough_grants_ids))
        self.add_log(f"Not enough grants for following {len(self.not_enough_grants_ids)} titles: {remote_ids}")
        self.mark_unpublished(
            {
                "Genres": Amedia2Genre,
                "Titles": Amedia2Title,
                "Episodes": Amedia2Episode,
                "Seasons": Amedia2Season,
            },
        )
        self.set_similar_titles()

    def set_similar_titles(self):
        self.add_log("Setting similar titles.")
        not_found_title_ids = set()

        for title_id, recommendations in self.similar_titles_cache.items():
            title = self.titles_cache[title_id]
            if not self.can_update(title):
                continue

            similar_titles: list[Amedia2SimilarTitleReference] = []
            for recommendation_info in recommendations:
                similar_title = self.titles_cache.get(recommendation_info.id)
                if not similar_title:
                    not_found_title_ids.add(recommendation_info.id)
                    continue

                similar_titles.append(
                    Amedia2SimilarTitleReference(
                        title=similar_title.pk,
                    )
                )

            title.similar_titles = similar_titles
            title.save()

        if not_found_title_ids:
            remote_ids = ", ".join(sorted(not_found_title_ids))
            self.add_log(
                f"During 'set_similar_titles' {len(not_found_title_ids)} titles were not found in database. "
                f"Remote ids: {remote_ids}"
            )

    def import_title(self, remote_id: str, catalogue_type: Amedia2CatalogueType):
        title_info: TitleInfo = self.amedia_api.get_title_info(remote_id=remote_id, catalogue_type=catalogue_type)

        # #113227 do not import titles with mismatch in grants
        title_grants = [grant for grant in title_info.grants if grant.is_valid(valid_for_date=self.import_datetime)]
        granted_countries = set().union(*(grant.countries for grant in title_grants))

        if title_grants and self.amedia_grant and self.amedia_grant not in granted_countries:
            self.not_enough_grants_ids.add(remote_id)
            return

        title: Amedia2Title = self._upsert_title(title_info, catalogue_type)
        self.titles_cache[title.remote_id] = title
        self.similar_titles_cache[title.remote_id] = title_info.recommendations
        if catalogue_type == Amedia2CatalogueType.series:
            self.delayed_seasons.append(title_info)
        if title_info.type == TitleType.tvmovie:
            for episode_info in title_info.episodes:
                self.delay_episode_import(title=title, episode_info=episode_info)

    def get_title_countries(self, title_info: TitleInfo) -> list[AbcvodCountry]:
        result: list[AbcvodCountry] = []
        for country_info in title_info.countries:
            try:
                result.append(self.countries_cache[country_info.name])
            except KeyError:
                self.add_import_error("Unknown country", f"Unexpected country name: {country_info.name}")
        return result

    def get_persons(self, title_info: TitleInfo, person_type: PersonType) -> list[AbcvodPerson]:
        """Extract persons of certain type from title info."""
        result = []
        for person_info in title_info.persons:
            if person_info.type != person_type:
                continue
            result.append(self.get_or_create_person(person_info))
        return result

    def _set_title_posters_info(self, title: Amedia2Title, asset_paths):
        for asset in asset_paths:
            name = asset["name"]
            path = asset["path"]
            if name == "partnersPoster":
                title.poster = path
            # Backup main poster, if "partnersPoster" not provided.
            if name == "v_poster" and not title.poster:
                title.poster = path
            if name == "partnersPoster_16_9":
                title.poster_horizontal = path
            if name == "HproductBackground":
                title.poster_background = path
            if name == "productLogo":
                title.title_logo = path

    def run_delayed_title_updates(self):
        self.task_tracker.add_log_message(
            f"There is {len(self.delayed_title_updates)} 'delayed' updates for titles after import."
        )
        if not self.delayed_title_updates:
            return
        bulk = BulkUpdate(Amedia2Title, save=True)
        for delayed_update in self.delayed_title_updates:
            title_id = delayed_update.pop("id")
            bulk.update_one(title_id, update={"$set": delayed_update})
        bulk.flush()
