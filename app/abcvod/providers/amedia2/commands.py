import click
from flask import current_app
from flask.cli import AppGroup

from app.abcvod.providers.amedia2.importer import Amedia2Importer
from app.cms.models import TaskTrigger

amedia2_commands = AppGroup("amedia2")


@amedia2_commands.command("import")
@click.option("--download-images", "download_images", is_flag=True)
def amedia2_import(download_images):

    importer = Amedia2Importer(app=current_app, task_trigger=TaskTrigger.CLI)
    if not download_images:
        importer.skip_download_images = True
    importer.run()
