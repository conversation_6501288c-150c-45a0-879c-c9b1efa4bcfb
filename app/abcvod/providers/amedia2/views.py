from flask import current_app
from flask_admin import Admin

from app.abcvod.core.views import (
    AbcVodCategoryView,
    AbcVodCollectionView,
    AbcVodEpisodeView,
    AbcVodGenreView,
    AbcvodImportTaskView,
    AbcVodSeasonView,
    AbcVodTitleView,
    AbcVodViewMixin,
    FieldsOrderMixin,
    GetJsonFromApiMixin,
    ReimportMixin,
    init_vod_admin,
)
from app.abcvod.providers.amedia2.api import (
    Amedia2API,
    Amedia2CatalogueType,
)
from app.abcvod.providers.amedia2.celery import amedia2_import_task
from app.abcvod.providers.amedia2.importer import Amedia2Importer
from app.abcvod.providers.amedia2.models import (
    Amedia2Category,
    Amedia2Collection,
    Amedia2Episode,
    Amedia2Genre,
    Amedia2ImportTracker,
    Amedia2Season,
    Amedia2Title,
)
from app.cms.models import TaskTrigger
from app.common.widgets import ImagesFormatter


class Amedia2Mixin(AbcVodViewMixin):
    vod_name = "amedia2"
    genre_class = Amedia2Genre
    category_class = Amedia2Category
    title_class = Amedia2Title
    season_class = Amedia2Season
    episode_class = Amedia2Episode
    collection_class = Amedia2Collection

    @property
    def api(self) -> Amedia2API:
        return current_app.extensions["amedia2_api"]


class ImagesFieldMixin(FieldsOrderMixin):
    """Mixin adds 'images' field.

    Images - is the special field for Amedia, it contains all possible images for current entity.
    """

    @property
    def _additional_fields(self):
        return ("images",) + super(ImagesFieldMixin, self)._additional_fields

    @property
    def form_args(self):
        original_args = super(ImagesFieldMixin, self).form_args.copy()
        original_args.update(images={"table_layout": True, "widget": ImagesFormatter()})
        return original_args


class Amedia2TitleView(Amedia2Mixin, ImagesFieldMixin, ReimportMixin, GetJsonFromApiMixin, AbcVodTitleView):
    def _get_catalogue_type(self, title: Amedia2Title) -> Amedia2CatalogueType:
        if title.title_type == Amedia2CatalogueType.series:
            return Amedia2CatalogueType.series
        return Amedia2CatalogueType.movies

    def get_api_response(self, document):
        title: Amedia2Title = document
        catalogue_type = self._get_catalogue_type(title)
        return self.api.get_title_info_json(title.remote_id, catalogue_type)

    def reimport_procedure(self, document):
        title: Amedia2Title = document
        catalogue_type = self._get_catalogue_type(title)
        importer = Amedia2Importer(app=current_app, task_trigger=TaskTrigger.MANUAL)
        with importer, importer.can_update_only(models=[Amedia2Title, Amedia2Season, Amedia2Episode]):
            importer.results["Reimport title with remote id"] = title.remote_id
            importer.reraise_all_log_errors = True
            importer.setup_categories()
            importer.import_genres()
            importer.import_title(title.remote_id, catalogue_type)
            importer.import_seasons()
            importer.import_episodes()
            importer.download_all_images()
            importer.run_delayed_title_updates()  # unpublish empty series.


class Amedia2CategoryView(Amedia2Mixin, AbcVodCategoryView):
    pass


class Amedia2GenreView(Amedia2Mixin, AbcVodGenreView):
    pass


class Amedia2SeasonView(Amedia2Mixin, GetJsonFromApiMixin, ReimportMixin, ImagesFieldMixin, AbcVodSeasonView):
    def reimport_procedure(self, document):
        season: Amedia2Season = document
        importer = Amedia2Importer(app=current_app, task_trigger=TaskTrigger.MANUAL)
        with importer, importer.can_update_only(models=[Amedia2Season, Amedia2Episode]):
            importer.results["Reimport season with remote id"] = season.remote_id
            importer.import_season(season.remote_id, season.title)
            importer.import_episodes()
            importer.download_all_images()

    def get_api_response(self, document):
        season: Amedia2Season = document
        return self.api.get_season_info_json(remote_id=season.remote_id)


class Amedia2EpisodeView(Amedia2Mixin, ImagesFieldMixin, ReimportMixin, GetJsonFromApiMixin, AbcVodEpisodeView):
    def _reimport_episode_for_series(self, importer: Amedia2Importer, episode: Amedia2Episode):
        season: Amedia2Season = episode.season
        title: Amedia2Title = episode.title
        if not season:
            raise Exception("Season is missing, reimport is impossible. Please run reimport for title.")
        season_info = self.api.get_season_info(season.remote_id)
        for episode_info in season_info.episodes:
            if episode.remote_id == episode_info.id:
                return importer.import_episode(title, season, episode_info)
        raise Exception(
            f"Episode with remote_id {episode.remote_id} not found for 'series' title "
            f"'{title} ({title.remote_id})' in season  {season.number} ({season.remote_id})"
        )

    def _reimport_episode_for_tvmovie(self, importer: Amedia2Importer, episode: Amedia2Episode):
        title: Amedia2Title = episode.title
        title_info = self.api.get_title_info(title.remote_id, Amedia2CatalogueType.movies)
        for episode_info in title_info.episodes:
            if episode.remote_id == episode_info.id:
                return importer.import_episode(title, None, episode_info)
        raise Exception(
            f"Episode with remote_id {episode.remote_id} not found for 'tvmovie' title "
            f"'{title} ({title.remote_id})'"
        )

    def reimport_procedure(self, document):
        episode: Amedia2Episode = document
        title: Amedia2Title = episode.title
        importer = Amedia2Importer(app=current_app, task_trigger=TaskTrigger.MANUAL)
        with importer, importer.can_update_only(models=[Amedia2Episode]):
            importer.results["Reimport episode with remote id"] = episode.remote_id
            if title.title_type == Amedia2CatalogueType.series:
                self._reimport_episode_for_series(importer, episode)
            else:
                self._reimport_episode_for_tvmovie(importer, episode)
            importer.download_all_images()

    def get_api_response(self, document):
        episode: Amedia2Episode = document
        season: Amedia2Season = episode.season
        season_json = self.api.get_season_info_json(remote_id=season.remote_id)
        episodes_info_list = season_json.get("episodes", [])
        for episode_info in episodes_info_list:
            if str(episode_info["id"]) == episode.remote_id:
                return episode_info
        return {"episode": f"not found episode with remote_id {episode.remote_id}:("}


class Amedia2CollectionView(Amedia2Mixin, AbcVodCollectionView):
    model = Amedia2Collection


class Amedia2ImportTaskView(AbcvodImportTaskView):
    model = Amedia2ImportTracker
    celery_task = amedia2_import_task


def init_amedia2_admin(admin: Admin, caption="Amedia2 v2", parent_name=None):
    init_vod_admin(
        admin,
        caption,
        vod_mixin=Amedia2Mixin,
        category_view=Amedia2CategoryView,
        genre_view=Amedia2GenreView,
        title_view=Amedia2TitleView,
        season_view=Amedia2SeasonView,
        episode_view=Amedia2EpisodeView,
        collection_view=Amedia2CollectionView,
        parent_name=parent_name,
    )
    vod_name = Amedia2Mixin.vod_name
    admin.add_view(Amedia2ImportTaskView(name="Import tasks", category=caption, endpoint=f"{vod_name}_tasks"))
