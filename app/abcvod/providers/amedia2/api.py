from __future__ import annotations

import datetime
from enum import Enum
from typing import (
    Annotated,
    Generator,
    Optional,
)

from flask import Flask
from pydantic import (
    BaseModel,
    ConfigDict,
    field_validator,
)

from app.abcvod.core.api import (
    BaseAPI,
    NotImportant,
)

TimezoneAwareDatetime = Annotated[datetime.datetime, "must have tzinfo"]


class BaseAmediaModel(BaseModel):
    model_config = ConfigDict(coerce_numbers_to_str=True)

    id: str  # Almost all Amedia entities has integer id. On our side we need it as "str", because "remote_id" is str.


class GenreInfo(BaseAmediaModel):
    name: str  # "Документальные"
    slug: str  # "dokumentalnyie"


class AssetsInfo(BaseModel):
    """Various images & Posters."""

    # fmt: off
    Reg_asset: Optional[str] = None  # https://s80658.cdn.ngenix.net/resize/{SIZE}/_stor_/cms/content-contentasset/7/d0/2c67862a484836556da9bcb4fc3617d0-40363-4a5593ce267b406b946692a30443a7c5.jpg"  # noqa
    VproductBackground: Optional[str] = None  # "https://s80658.cdn.ngenix.net/resize/{SIZE}/_stor_/cms/content-contentasset/a/10/368ad0c3f799f1711a9bf6cd14af9a10-40362-966d7e080b19457e84db52c5647ba9f5.jpg"  # noqa
    HproductBackground: Optional[str] = None  # "https://s80658.cdn.ngenix.net/resize/{SIZE}/_stor_/cms/content-contentasset/4/d0/adc67868cb1dd1df6d0309e0daade4d0-40361-3fea6c4d57184e0caa80fcbfc2e99f1d.jpg"  # noqa
    productLogo: Optional[str] = None  # "https://s80658.cdn.ngenix.net/trim/{SIZE}/_stor_/cms/content-contentasset/0/b8/a0c0e0fb17cbb73d92b58ded759dd0b8-40360-9bed58837bee446ab63f32ebcae83b51.png"  # noqa
    productPoster: Optional[str] = None  # "https://s80658.cdn.ngenix.net/overlay/{SIZE}/_stor_/cms/content-badge/0/05/a63c90cc3684ad8b0a2176a6a8fe9005-10-38914e16197d4eeb8f9f1a31b0845452.png/_stor_/cms/content-contentasset/0/22/d892edd9c204e4051d7eb839cd01f022-40359-c517029dcf634e3fb80bfa2fb68ae1d3.jpg  # noqa
    partnersPoster: Optional[str] = None  # "https://i.amediateka.tech/resize/{SIZE}/_stor_/cms/content-contentasset/4/8f/937c07ed9ceab1f0b11ff16aaccf648f-170086-dbe345d22603405f8ccb508b1ef8a889.jpg",  # noqa
    partnersPoster_16_9: Optional[str] = None  # "https://i.amediateka.tech/resize/{SIZE}/_stor_/cms/content-contentasset/9/96/7cfc316e80390245688ee18cfecb5996-170087-dd523250cf7d4836aa4fca7ec3d36505.jpg",  # noqa
    # Only in episodes?
    stopKadr: Optional[str] = None  #  "https://s80658.cdn.ngenix.net/resize/{SIZE}/_stor_/cms/content-contentasset/b/49/9d9db758e204dfd8c700ca4a6a54bb49-35764-dc87f95c6ab743458df83ea4ef3dbb32.jpg"  # noqa
    # No idea, but API response for us may contain this fields.
    logo_2x: Optional[str] = None  # "https://i.amediateka.tech/trim/{SIZE}/_stor_/cms/content-contentasset/5/36/c207ee27af30a436c0baba8bf6f93536-138799-ba1d8a7e19d846c29bbf7adb8bd7a143.png"  # noqa
    productItemCover: Optional[str] = None  # "https://i.amediateka.tech/resize/{SIZE}/_stor_/cms/content-contentasset/f/f5/4346013ae428384804d5098e5775eff5-177746-c2ae652330194ac2a56f4a84808e4b43.jpg",  # noqa
    poster_back: Optional[str] = None  # "https://i.amediateka.tech/resize/{SIZE}/_stor_/cms/content-contentasset/b/db/43f6463adbb91796500ec9c370681bdb-138805-b69aa96521fe4695b87956344460b91e.jpg"  # noqa
    poster_logo: Optional[str] = None  # "https://i.amediateka.tech/trim/{SIZE}/_stor_/cms/content-contentasset/5/53/3ede4c84c4d39a15ca9e70f2c6097553-138806-848c62a616c1453f9b638508680803d6.png"  # noqa
    productPosterV2: Optional[str] = None  # "https://i.amediateka.tech/resize/{SIZE}/_stor_/cms/content-contentasset/d/fd/68c736a0c95dc8244e3aca06295d2dfd-138800-e156d267f0924c1d95fb9f3dfc8bad72.jpg"  # noqa
    v_poster: Optional[str] = None  # "https://i.amediateka.tech/resize/{SIZE}/_stor_/cms/content-contentasset/2/78/21dda190ce4f7c9a6fee629938475278-138798-464e62e24a9c4961800c9b005e484ecb.jpg"  # noqa
    v_poster_back: Optional[str] = None  # "https://i.amediateka.tech/resize/{SIZE}/_stor_/cms/content-contentasset/4/76/5cd7cd3ee301c0e4a516cbefbcddb476-138803-da819c2261e24b319f79d271542d9dae.jpg"  # noqa
    v_poster_logo: Optional[str] = None  # "https://i.amediateka.tech/trim/{SIZE}/_stor_/cms/content-contentasset/2/9e/7912bb3a6576280596587f1e3721429e-138804-3cf182012fec473395f45173191e8e7d.png"  # noqa
    # fmt: on


class BadgeInfo(BaseModel):
    text: str  # "Скоро"
    textColor: str  # "#000000"
    backgroundColor: str  # "#feb52b


class TitleType(str, Enum):
    movie = "movie"
    tvmovie = "tvmovie"
    series = "series"


class TagInfo(BaseModel):
    """Found no info about tags yet, and we also don't need them at the moment."""


class CountryInfo(BaseAmediaModel):
    name: str


class PersonType(str, Enum):
    actor = "actor"
    director = "director"
    producer = "producer"


class PersonInfo(BaseAmediaModel):
    type: str  # "actor", also known types: "director"
    name: str  # "Ивонн Орджи"
    role: Optional[str] = None  # null


class GrantInfo(BaseModel):
    start_time: TimezoneAwareDatetime | None  # "2020-06-04T20:00:00+03:00" or ""
    end_time: TimezoneAwareDatetime | None  # "2023-01-01T00:00:00+03:00" or ""
    countries: list[str]  # ["RU", "GE", "XC", "BY", "AM", "AZ", "KZ", "KG", "TJ", "TM", "UZ", "UA"]
    platforms: list[str]  # []
    device_types: list[str]  # []
    device_vendors: list[str]  # []
    device_models: list[str]  # []

    @field_validator("start_time", "end_time", mode="before")
    def convert_time(cls, data):
        if not data:
            return None
        return data

    def is_valid(self, valid_for_date: TimezoneAwareDatetime | None = None) -> bool:
        valid_for_date = valid_for_date or datetime.datetime.utcnow()
        if self.start_time and valid_for_date < self.start_time:
            return False
        if self.end_time and valid_for_date > self.end_time:
            return False
        return True


class CountryInfoPage(BaseModel):
    items: list[CountryInfo]


class StudioInfo(BaseAmediaModel):
    name: str  # "HBO"
    logo: Optional[
        str
    ] = None  # "https://s80658.cdn.ngenix.net/trim/{SIZE}/_stor_/cms/metadata-studio/a/66/c4103f122d27677c9db144cae1394a66-2-5ae2d50c1eec4dc78ea2a707123ef60a.png  # noqa


class BriefSeasonInfo(BaseAmediaModel):
    title: str  # "Сезон 1"
    originalTitle: str  # "Сезон 1"
    seasonNumber: int  # 1
    assets: AssetsInfo
    url: str | NotImportant = None  # "http://papi.amediateka.tech/cms/partner/content/seasons/24872/"
    webUrl: str | NotImportant = None  # "/watch/series_24871_dolina-soblazna/season_1_24872"
    playbackUrl: str | NotImportant = None  # "http://papi.amediateka.tech/playback-options/season/24872/"
    episodeCount: int | NotImportant = None  # 8
    published: bool  # No idea, official doc has no info about this field, but we get it.#


class ShortTitleInfo(BaseAmediaModel):
    title: str  # "Инстинкт"
    slug: str  # "instinkt"


class EpisodeInfo(BaseAmediaModel):
    number: int  # 1
    title: str  # "Эпизод 1"
    originalTitle: str  # "Эпизод 1"
    description: str | NotImportant = None  # "Первый спешл комедийной актрисы Ивонн Орджи, звезды сериала...
    shortDescription: str  # "Первый спешл комедийной актрисы Ивонн Орджи, звезды сериала «Белая ворона». В своем...
    publishDate: datetime.datetime  # "2020-06-04 17:59:16+00:00"
    assets: AssetsInfo
    playbackUrl: str | NotImportant = None  # "http://papi.amediateka.tech/playback-options/episode/24211/"
    videoPublished: bool  # true
    duration: Optional[float] = None  # "3748.0" or "null" in some cases.
    free: bool | NotImportant = None  # false

    @field_validator("duration")
    @classmethod
    def set_duration(cls, duration):
        return duration or 0


class SeasonInfo(BriefSeasonInfo):
    type: str  # "season"
    slug: str  # "sezon-2-42"
    premierYear: int  # 2019
    shootEndYear: Optional[int] = None  # null
    shortDescription: str  # Алан Камминг блистает в детективном сериале на основе романа «Убийственные игры»...
    description: str | NotImportant = None  # Алан Камминг блистает в детективном сериале на основе романа...
    badge: Optional[BadgeInfo] = None  # null
    labelText: Optional[str] = None  # null
    textColor: Optional[str] = None  # null
    labelColor: Optional[str] = None  # null
    mainGenre: GenreInfo
    publicationDate: datetime.datetime | NotImportant = None  # "2020-06-13T19:16:14+03:00"
    seoTitle: str | NotImportant = None  # "Инстинкт 2 сезон смотреть онлайн"
    seoDescription: str | NotImportant = (
        None  # "Сериал Инстинкт Сезон 2 (2018) все серии подряд смотреть в онлайн-кинотеатре Аmediateka...
    )
    ratingKinopoisk: Optional[float] = None  # null
    ratingImdb: Optional[float] = None  # null
    ratingAmediateka: Optional[float] = None  # "5.0"
    ageRestrictions: int | NotImportant = None  # 18
    audioInfo: Optional[str] = None  # null
    subsInfo: Optional[str] = None  # null
    series: ShortTitleInfo
    countries: list[CountryInfo] | NotImportant = None
    studios: list[StudioInfo] | NotImportant = None
    tags: list[TagInfo] | NotImportant = None
    genres: list[GenreInfo] | NotImportant = None
    trailers: list | NotImportant = None  # [] i don't know
    recommendations: list | NotImportant = None  # [] i don't lnow
    licenseTypes: list[str] | NotImportant = None
    episodes: list[EpisodeInfo]
    grants: Optional[list[GrantInfo]] = None
    url: Optional[str] = None  # we don't get this field in "full" info.
    episodeCount: Optional[int] = None  # we don't get this field in "full" info.
    published: Optional[bool] = None  # we don't get this field in "full" info.


class BriefTitleInfo(BaseAmediaModel):
    """Short info about movies, acquired in batches from list endpoints."""

    title: str  # Стоктон в моих мыслях
    assets: AssetsInfo
    badge: Optional[BadgeInfo] = None
    genres: list[GenreInfo]
    slug: str | NotImportant = None  # "stokton-v-moih-myislyah"
    url: Optional[str] = None  # "http://papi.amediateka.tech/cms/partner/content/movies/24896/"
    webUrl: str | NotImportant = None  # "/watch/movies_24896_stokton-v-moih-myislyah"
    type: TitleType
    tags: list[TagInfo]
    playbackUrl: str | NotImportant = None  # "http://papi.amediateka.tech/playback-options/movie/24896/"
    premierYear: int
    shootEndYear: Optional[int] = None
    shortDescription: str  # "Документальный фильм Марка Левина (обладателя «Золотой камеры» Каннского..."
    ratingAmediateka: float | NotImportant = None
    seasons: Optional[int] = None


class RecommendationInfo(BaseAmediaModel):
    title: str | NotImportant = None  # "Аманда Силз: В курсах"
    type: TitleType | NotImportant = None  # "movie"
    url: str | NotImportant = None  # "http://papi.amediateka.tech/cms/partner/content/movies/21098/"
    webUrl: str | NotImportant = None  # "/watch/movies_21098_finalamanda-silz-v-kursah"
    assets: AssetsInfo | NotImportant = None


class TitleInfo(BriefTitleInfo):
    """Full info about title."""

    originalTitle: str  # "Yvonne Orji: Mama I Made It"
    description: str | NotImportant = None  # may actually have same content as shortDescription
    labelText: Optional[str] = None  # null
    textColor: Optional[str] = None  # null
    labelColor: Optional[str] = None  # null
    mainGenre: GenreInfo | NotImportant = None
    publicationDate: datetime.datetime  # 2020-06-04T20:59:16+03:00
    seoTitle: str | NotImportant = None  # "Ивонн Орджи: Мама, я это сделала! смотреть онлайн"
    seoDescription: str | NotImportant = None  # Фильм Ивонн Орджи: Мама, я это сделала! (2020) смотреть в онлайн-...
    ratingKinopoisk: Optional[float] = None  # null
    ratingImdb: Optional[float] = None  # null
    ageRestrictions: Optional[int] = None  # 18
    audioInfo: Optional[str] = None  # null
    subsInfo: Optional[str] = None  # null
    countries: list[CountryInfo]
    studios: list[StudioInfo]
    persons: list[PersonInfo]
    trailers: list | NotImportant = None  # []
    recommendations: list[RecommendationInfo] = []
    licenseTypes: list[str] | NotImportant = None  # ["SVOD"]
    episodes: Optional[list[EpisodeInfo]] = None  # for movies
    seasons: Optional[list[BriefSeasonInfo]] = None  # for series
    quote: str | NotImportant = None  # ""
    facts: list[str] | NotImportant = None  # []
    grants: list[GrantInfo]


class TitlesPage(BaseModel):
    count: int
    next: Optional[str] = None  # "http://papi.amediateka.tech/cms/partner/content/movies/?limit=20&offset=20&platf...
    previous: Optional[str] = None  # null
    results: list[BriefTitleInfo]


class Amedia2CatalogueType(str, Enum):
    movies = "movies"
    series = "series"


class Amedia2API(BaseAPI):
    """Class for connection to Amedia API."""

    def __init__(self, api_key: str, platform: str, api_url: str, http_retries=5):
        """Init.

        :param api_key: Secret key for access API
        :param platform: Platform for access API
        :param api_url: Base URL for API
        :param http_retries: Amount of retries for HTTP requests
        """
        super().__init__(http_retries=http_retries)
        self.default_request_params = {
            "platform": platform,
        }
        self.default_headers = {
            "Partner-Api-Key": api_key,
        }
        self.api_url = api_url

    def get_all_pages(self, first_page_url: str) -> Generator[TitlesPage, None, None]:
        limit = 100
        has_next = True
        offset = 0
        while has_next:
            page_json = self.get_json_response(url=first_page_url, get_params={"limit": limit, "offset": offset})
            page = TitlesPage(**page_json)
            yield page
            has_next = bool(page.next)
            offset += limit

    def get_all_titles_info(self, catalogue_type: Amedia2CatalogueType) -> Generator[BriefTitleInfo, None, None]:
        for title_page in self.get_all_pages(f"{self.api_url}/cms/partner/content/{catalogue_type}/"):
            for info in title_page.results:
                yield info

    def get_title_info_json(
        self,
        remote_id: str,
        catalogue_type: Amedia2CatalogueType,
    ) -> dict:
        url = f"{self.api_url}/cms/partner/content/{catalogue_type}/{remote_id}/"
        return self.get_json_response(url=url)

    def get_season_info_json(self, remote_id: str) -> dict:
        url = f"{self.api_url}/cms/partner/content/seasons/{remote_id}/"
        return self.get_json_response(url=url)

    def get_genres_info_list(self, catalogue_type: Amedia2CatalogueType) -> list[GenreInfo]:
        url = f"{self.api_url}/cms/partner/catalogue/genres/"
        json_response: list[dict] = self.get_json_response(url=url, get_params={"catalogue_type": catalogue_type})
        return [GenreInfo(**item) for item in json_response]

    def get_season_info(self, remote_id: str) -> SeasonInfo:
        return SeasonInfo(**self.get_season_info_json(remote_id))

    def get_title_info(self, remote_id: str, catalogue_type: Amedia2CatalogueType):
        return TitleInfo(**self.get_title_info_json(remote_id, catalogue_type))


def init_amedia2_api(app: Flask):
    """Creates Amedia2API client using app's configuration and adds it to app's extensions under `amedia2_api` key."""
    amedia2_api = Amedia2API(
        api_key=app.config["AMEDIA_API_KEY"],
        platform=app.config["AMEDIA_PLATFORM"],
        api_url=app.config["AMEDIA_API_URL"],
        http_retries=app.config["AMEDIA_MAX_RETRIES"],
    )

    app.extensions = getattr(app, "extensions", {})
    app.extensions["amedia2_api"] = amedia2_api
