from mongoengine import (
    PULL,
    EmbeddedDocumentField,
    IntField,
)
from mongoengine.fields import (
    ListField,
    ReferenceField,
    StringField,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodEpisode,
    AbcvodGenre,
    AbcvodSeason,
    AbcvodSimilarTitleReference,
    AbcvodTitle,
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.abcvod.core.models.task import AbcvodImportTracker
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)
from cmf.core.models import CmsEmbeddedDocument
from cmf.core.utils import Choices

DB_ALIAS = "amediateka2"


class Amedia2ImportTracker(AbcvodImportTracker):
    job_title = "Amedia2 VOD content import"
    job_id = "amedia2-import"


class Amedia2Genre(AbcvodGenre):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "amediateka_genre",
    }


class Amedia2Category(AbcvodCategory):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "amediateka_category",
    }
    genres = ListField(ReferenceField(Amedia2Genre, reverse_delete_rule=PULL))


class AmediaImage(CmsEmbeddedDocument):
    """Info about image, used in AmediaImagesField."""

    name = StringField()
    path = StringField()


# noinspection PyPep8Naming
def AmediaImagesField():
    """Shortcut for 'images' field.

    This could be a normal class AmediaImagesField(ListField), but somewhere deep in
    flask-admin there is logic, that relies on 'ListField' type name.
    """
    help_text = (
        "These images should not be used by any service. "
        "Their purpose is to give us a general idea which else images are we provided with."
    )
    return ListField(EmbeddedDocumentField(AmediaImage), help_text=help_text)


class Amedia2TitleType(Choices):
    movie = "Фильм"
    series = "Сериал"
    tvmovie = "Многосерийный фильм"


class Amedia2SimilarTitleReference(AbcvodSimilarTitleReference):
    title = ReferenceField("Amedia2Title")


class Amedia2Title(AbcvodTitle):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "amediateka_title",
    }
    # generic fields
    categories = ListField(ReferenceField(Amedia2Category, reverse_delete_rule=PULL))
    genres = ListField(ReferenceField(Amedia2Genre, reverse_delete_rule=PULL))
    collections = ListField(ReferenceField("Amedia2Collection"))
    similar_titles = ListField(EmbeddedDocumentField(Amedia2SimilarTitleReference))

    # Custom Amedia fields
    images = AmediaImagesField()
    title_type = StringField(choices=Amedia2TitleType.choices, help_text="Type of title in Amedia API.")


class Amedia2TitleReference(AbcvodTitleReference):
    title = ReferenceField(Amedia2Title)


class Amedia2Collection(AbcvodCollection):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "amediateka_collection",
        "index_cls": False,
    }
    titles = TitlesField(Amedia2TitleReference)
    titles_excluded = TitlesExcludedField(Amedia2TitleReference)
    categories = ReferenceCriteriaField(Amedia2Title.categories)
    genres = ReferenceCriteriaField(Amedia2Title.genres)
    imdb_rating = RatingCriteriaField(Amedia2Title.imdb_rating)
    kp_rating = RatingCriteriaField(Amedia2Title.kp_rating)


Amedia2Collection.register_delete_rule(Amedia2Title, "collections", PULL)


class Amedia2Season(AbcvodSeason):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "amediateka_season",
    }

    title = ReferenceField(Amedia2Title)
    images = AmediaImagesField()
    premier_year = IntField(help_text="This field is only required for making 'years' field in season's title.")


class Amedia2Episode(AbcvodEpisode):
    meta = {
        "db_alias": DB_ALIAS,
        "collection": "amediateka_episode",
    }

    title = ReferenceField(Amedia2Title)
    season = ReferenceField(Amedia2Season)
    images = AmediaImagesField()
