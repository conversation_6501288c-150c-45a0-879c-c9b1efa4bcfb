from flask import current_app

from app.abcvod.providers.start2.importer import Start2Importer
from app.cms.models import CeleryTaskTracker
from app.cms.tasks.utils import cms_task


@cms_task(task_processor_class=Start2Importer)
def start2_import_task(task_trigger: str, task_tracker: CeleryTaskTracker = None):
    importer = Start2Importer(app=current_app, task_tracker=task_tracker, task_trigger=task_trigger)
    importer.run()
