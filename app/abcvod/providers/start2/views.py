from flask import current_app
from flask_admin import Admin

from app.abcvod.core.views import (
    AbcVodCategoryView,
    AbcVodCollectionView,
    AbcVodEpisodeView,
    AbcVodGenreView,
    AbcvodImportTaskView,
    AbcVodSeasonView,
    AbcVodTitleView,
    AbcVodViewMixin,
    GetJsonFromApiMixin,
    ReimportMixin,
    init_vod_admin,
)
from app.abcvod.providers.start2.api import Start2API
from app.abcvod.providers.start2.celery import start2_import_task
from app.abcvod.providers.start2.importer import Start2Importer
from app.abcvod.providers.start2.models import (
    Start2Category,
    Start2Collection,
    Start2Episode,
    Start2Genre,
    Start2ImportTracker,
    Start2Season,
    Start2Title,
)
from app.cms.models import TaskTrigger


class Start2Mixin(AbcVodViewMixin):
    vod_name = "start2"
    genre_class = Start2Genre
    category_class = Start2Category
    title_class = Start2Title
    season_class = Start2Season
    episode_class = Start2Episode
    collection_class = Start2Collection

    @property
    def api(self) -> Start2API:
        return current_app.extensions["start2_api"]


class Start2TitleView(Start2Mixin, ReimportMixin, GetJsonFromApiMixin, AbcVodTitleView):
    model = Start2Title

    _system_fields = AbcVodTitleView._system_fields + ("url",)

    def get_api_response(self, document) -> dict:
        title: Start2Title = document
        if title.is_series:
            return self.api.get_series_info_raw(title.remote_id)
        return self.api.get_movie_info_raw(title.remote_id)

    def reimport_procedure(self, document):
        """Reimport movie or series from Start2 API.

        At current moment, collections are not fetched from API due to complications.
        """
        document: Start2Title
        importer = Start2Importer(app=current_app, task_trigger=TaskTrigger.MANUAL)
        with importer:
            importer.results["Reimport title with id"] = document.remote_id
            importer.reraise_all_log_errors = True
            importer.setup_categories()
            importer._populate_genres_cache()
            if document.is_series:
                with importer.ignore_do_not_autoupdate:
                    importer.import_series(document.slug)
                importer.import_all_seasons()
                importer.import_all_episodes()
            else:
                with importer.ignore_do_not_autoupdate:
                    importer.import_movie(document.slug)
            importer.download_all_images()


class Start2CategoryView(Start2Mixin, AbcVodCategoryView):
    model = Start2Category


class Start2GenreView(Start2Mixin, AbcVodGenreView):
    model = Start2Genre

    _system_fields = AbcVodGenreView._system_fields + ("url",)


class Start2SeasonView(GetJsonFromApiMixin, Start2Mixin, AbcVodSeasonView):
    model = Start2Season

    _system_fields = AbcVodSeasonView._system_fields + ("api_path",)

    def get_api_response(self, document):
        season: Start2Season = document
        return self.api.get_season_info_raw(season.api_path)


class Start2EpisodeView(GetJsonFromApiMixin, Start2Mixin, AbcVodEpisodeView):
    model = Start2Episode

    def get_api_response(self, document):
        episode: Start2Episode = document
        season: Start2Season = episode.season
        season_info = self.api.get_season_info_raw(season.api_path)
        episodes_info = season_info["items"]
        episode_remote_id = str(episode.remote_id)
        for episode_info in episodes_info:
            if str(episode_info["_id"]) == episode_remote_id:
                return episode_info
        return ["Episode info not found in Start2 API :("]


class Start2CollectionView(GetJsonFromApiMixin, Start2Mixin, AbcVodCollectionView):
    model = Start2Collection

    def get_api_response(self, document):
        collection: Start2Collection = document
        if collection.is_main:
            return self.api.get_main_collection_info(collection.slug)
        return self.api.get_collection_info(collection.slug)


class Start2ImportTaskView(AbcvodImportTaskView):
    model = Start2ImportTracker
    celery_task = start2_import_task


def init_start2_admin(admin: Admin, caption="Start2", parent_name=None):
    init_vod_admin(
        admin,
        caption,
        vod_mixin=Start2Mixin,
        category_view=Start2CategoryView,
        genre_view=Start2GenreView,
        title_view=Start2TitleView,
        season_view=Start2SeasonView,
        episode_view=Start2EpisodeView,
        collection_view=Start2CollectionView,
        parent_name=parent_name,
    )
    vod_name = Start2Mixin.vod_name
    admin.add_view(Start2ImportTaskView(name="Import tasks", category=caption, endpoint=f"{vod_name}_tasks"))
