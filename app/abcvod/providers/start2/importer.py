from __future__ import annotations

import logging
from datetime import datetime
from io import BytesIO
from typing import (
    Optional,
    Union,
)

from flask import Flask
from mongoengine import DoesNotExist

from app.abcvod.core.importer import (
    DoNotAutoupdateMixin,
    ImporterWithImages,
)
from app.abcvod.core.models.abcvod import (
    AbcvodCountry,
    AbcvodPerson,
)

from .api import (
    BasicGenreInfo,
    BriefEpisodeInfo,
    BriefSeasonInfo,
    CollectionInfo,
    FullSeasonInfo,
    GenreInfo,
    ImageInfo,
    MovieInfo,
    PersonInfo,
    SeriesInfo,
    SimilarInfo,
    Start2API,
    TitleInfo,
)
from .models import (
    Start2Category,
    Start2Collection,
    Start2Episode,
    Start2Genre,
    Start2ImportTracker,
    Start2Season,
    Start2SimilarTitleReference,
    Start2Title,
)

logger = logging.getLogger(__name__)


def _extract_int(mixed_str: str) -> Union[int, None]:
    filtered_str = "".join(c for c in mixed_str if c.isdigit())
    return int(filtered_str) if filtered_str else None


class Start2Importer(DoNotAutoupdateMixin, ImporterWithImages):
    """Class for importing Start content.

    For extra reference about genres & categories see:
    https://gitlab.lfstrm.tv/server-side/cms/cms/-/merge_requests/374
    https://dev.tightvideo.com/issues/64786
    """

    task_tracker_model = Start2ImportTracker
    title_model = Start2Title
    collection_model = Start2Collection

    # Default categories, will be created and assigned during import.
    movies_category: Start2Category
    series_category: Start2Category

    def __init__(self, app: Flask, task_tracker: Start2ImportTracker | None = None, task_trigger: str | None = None):
        storage = app.extensions["storage"]
        max_workers = app.config["START_MAX_WORKERS"]
        super().__init__(storage=storage, max_workers=max_workers, task_trigger=task_trigger, task_tracker=task_tracker)
        self.start_api: Start2API = app.extensions["start2_api"]
        self.images_path = app.config["START_STORAGE_FOLDER"]

        # Sets of `BasicGenreInfo.url` for movies and series categories. Will be filled during import.
        self.movies_genres: set[str] = set()
        self.series_genres: set[str] = set()

        # cache for all known imported genres
        self.genres_cache: dict[str, Start2Genre] = {}
        self.genres = []

        # Makeshift queues for import.
        self.movies_for_delayed_import: set[str] = set()
        self.series_for_delayed_import: set[str] = set()
        self.images_for_delayed_import: list[dict] = []  # list of kwargs for download_image() method.
        self.seasons_for_delayed_import: list[dict] = []  # list of kwargs for import_season() method.
        self.episodes_for_delayed_import: list[dict] = []  # list of kwargs for import_episode() method.

        # Make cache for persons and countries
        self.countries_cache = {country.iso2: country for country in AbcvodCountry.objects.all()}
        persons = AbcvodPerson.objects.filter(start_id__exists=True)
        self.persons_cache = {person.start_id: person for person in persons}

        # Cache for titles and similar titles.
        self.titles_cache: dict[str, Start2Title] = {}
        self.similar_titles_cache: dict[str, list[SimilarInfo]] = {}

    def get_image(self, image_url) -> BytesIO:
        return self.start_api.get_image(image_path=image_url)

    def import_procedure(self):
        self.setup_categories()
        self.import_genres()
        self.import_featured_titles()
        self.delay_import_titles_from_genres()
        self.delay_import_titles_from_main_collections()
        self.import_all_movies()
        self.import_all_series()
        self.import_all_seasons()
        self.import_all_episodes()
        self.apply_category_genres()

    def after_import(self):
        super(Start2Importer, self).after_import()
        self.mark_unpublished(
            {
                "Genres": Start2Genre,
                "Titles": Start2Title,
                "Episodes": Start2Episode,
                "Seasons": Start2Season,
                "Categories": Start2Category,
            },
        )
        # Delete all stale categories.
        # Categories may not be created by user, but they may be created by previous version of import.
        # There may be a lot of obsolete categories, so they pollute UI.
        for category in Start2Category.objects(is_published=False):
            category.delete()

        self.set_similar_titles()

    def create_or_update_genre(self, genre_info: GenreInfo, priority: int) -> Start2Genre:
        url = genre_info.url  # Start API internally refers to genres by URL or slug, not by ID.

        try:
            genre: Start2Genre = self.genres_cache[url]
        except KeyError:
            genre: Start2Genre = Start2Genre(url=url, created_during_import=self.uid)

        genre.is_published = True
        genre.updated_during_import = self.uid
        if not self.can_update(genre):
            return genre.save()

        genre.remote_id = str(genre_info.id)
        genre.slug = genre_info.slug
        genre.caption = genre_info.title
        genre.priority = priority
        genre.url = genre_info.url
        genre.save()
        self.genres_cache[url] = genre
        return genre

    def _populate_genres_cache(self):
        # populate genres cache with existing genres.
        existing_genres: list[Start2Genre] = Start2Genre.objects.all()
        self.genres_cache = {genre.url: genre for genre in existing_genres}

    def set_similar_titles(self):
        self.add_log(f"Setting {len(self.similar_titles_cache)} similar titles...")

        for title_id, similar in self.similar_titles_cache.items():
            title = self.titles_cache[title_id]

            if not self.can_update(title):
                continue

            similar_titles: list[Start2SimilarTitleReference] = []
            for similar_info in similar:
                similar_id = similar_info.uid
                similar_title = self.titles_cache.get(similar_id)

                if not similar_title:
                    self.add_warning(
                        f"Similar title {similar_id} {similar_info.title} not found for title {title_id}.",
                    )
                    continue

                similar_titles.append(Start2SimilarTitleReference(title=similar_title.pk))

            title.similar_titles = similar_titles
            title.save()

    def import_genres(self):
        """Imports categories from Start API."""
        genre_info_list: list[GenreInfo] = self.start_api.get_genres()
        self._populate_genres_cache()
        for priority, genre_info in enumerate(genre_info_list, start=1):
            genre = self.create_or_update_genre(genre_info, priority)
            self.genres.append(genre)

    def get_collection_info(self, brief_collection_info: CollectionInfo) -> CollectionInfo:
        slug = brief_collection_info.alias  # actual slug
        if "/main/" in (brief_collection_info.url or ""):
            return self.start_api.get_main_collection_info(slug)
        return self.start_api.get_collection_info(slug)

    def delay_import_titles_from_genres(self):
        """Import content of 'regular' collections.

        We don't create "Collection" for these, reference:
        https://gitlab.lfstrm.tv/server-side/cms/cms/-/merge_requests/374#note_45576
        """
        for genre in self.genres:
            genre_collection_info = self.start_api.get_genre_collection_info(slug=genre.slug)

            for collection_catalogue in genre_collection_info.items:
                for brief_title_info in collection_catalogue.items:
                    self.delay_import_title(brief_title_info)

    def delay_import_titles_from_main_collections(self):
        main_showcase_collections_info = self.start_api.get_main_collections()
        for order, collection_info in enumerate(main_showcase_collections_info):
            for brief_title_info in collection_info.items:
                self.delay_import_title(brief_title_info)

    def import_featured_titles(self) -> None:
        brief_titles_info: list[TitleInfo] = self.start_api.get_featured_collection_info()
        for brief_title_info in brief_titles_info:
            self.delay_import_title(brief_title_info)

    def delay_import_title(self, brief_title_info: TitleInfo):
        """Add title to improvised queue and assign category(-ies) and collection(-s) for it."""
        slug = brief_title_info.alias  # Real slug. There is also "slug" field, which is actually relative url.
        if brief_title_info.is_series:
            self.series_for_delayed_import.add(slug)
        else:
            self.movies_for_delayed_import.add(slug)

    def delay_import_season(self, brief_season_info: BriefSeasonInfo, title: Start2Title):
        self.seasons_for_delayed_import.append(
            {
                "season_api_path": brief_season_info.url,
                "title": title,
            }
        )

    def delay_import_episode(
        self,
        episode_info: BriefEpisodeInfo,
        season: Start2Season,
    ):
        if episode_info.announcement:
            return
        self.episodes_for_delayed_import.append(
            {
                "episode_info": episode_info,
                "season": season,
            }
        )

    def import_all_movies(self):
        self.task_tracker.add_log_message(f"Importing {len(self.movies_for_delayed_import)} movies...")
        with self.bunch_of_tasks() as submit:
            for slug in self.movies_for_delayed_import:
                submit(self.import_movie, slug=slug)

    def import_all_series(self):
        self.task_tracker.add_log_message(f"Importing {len(self.series_for_delayed_import)} series...")
        with self.bunch_of_tasks() as submit:
            for slug in self.series_for_delayed_import:
                submit(self.import_series, slug=slug)

    def import_all_seasons(self):
        self.task_tracker.add_log_message(f"Importing {len(self.seasons_for_delayed_import)} seasons...")
        with self.bunch_of_tasks() as submit:
            for kwargs in self.seasons_for_delayed_import:
                submit(self.import_season, **kwargs)

    def import_all_episodes(self):
        self.task_tracker.add_log_message(f"Importing {len(self.episodes_for_delayed_import)} episodes...")
        with self.bunch_of_tasks() as submit:
            for kwargs in self.episodes_for_delayed_import:
                submit(self.import_episode, **kwargs)

    def import_season(self, season_api_path: str, title: Start2Title):
        with self.log_import_error(custom_message=f"Failed to import season '{season_api_path}'"):
            season_info = self.start_api.get_season_info(season_api_path=season_api_path)
            season = self._import_season(season_info, season_api_path, title)
            for brief_episode_info in season_info.items:
                self.delay_import_episode(brief_episode_info, season)

    def _import_season(self, season_info: FullSeasonInfo, season_api_path: str, title: Start2Title) -> Start2Season:
        remote_id = season_info.uid
        try:
            season: Start2Season = Start2Season.objects.get(remote_id=remote_id)
        except DoesNotExist:
            season: Start2Season = Start2Season(
                remote_id=remote_id,
                created_during_import=self.uid,
            )

        season.is_published = season_info.is_published
        season.updated_during_import = self.uid
        if not self.can_update(season):
            return season.save()

        # get poster for season
        storage_dir = f"{self.images_path}/{title.remote_id}/{remote_id}"
        season.poster = self.delay_download_poster(season_info.vertical, storage_dir)

        season.caption = season_info.title
        season.original_title = season_info.title_original
        season.slug = season_info.alias
        season.title = title
        season.number = season_info.num
        season.description = season_info.description
        season.api_path = season_api_path

        if rating_start := season_info.rating_start:
            season.rating = float(rating_start)
        if rating_kp := season_info.rating_kp:
            season.kp_rating = float(rating_kp)
        if rating_imdb := season_info.rating_imdb:
            season.imdb_rating = float(rating_imdb)
        return season.save()

    def import_episode(self, episode_info: BriefEpisodeInfo, season: Start2Season):
        episode_remote_id = episode_info.id
        with self.log_import_error(custom_message=f"Failed to import episode with remote id = '{episode_remote_id}'"):
            self._import_episode(episode_info, season)

    def _import_episode(self, episode_info: BriefEpisodeInfo, season: Start2Season) -> Start2Episode:
        remote_id = episode_info.id  # unlike other entities in Start, this is "uid" (uuid), not "_id" (guid).
        try:
            episode = Start2Episode.objects.get(remote_id=remote_id)
        except DoesNotExist:
            episode = Start2Episode(
                remote_id=remote_id,
                created_during_import=self.uid,
            )

        episode.is_published = True
        episode.updated_during_import = self.uid
        if not self.can_update(episode):
            return episode.save()

        # get poster for episode
        storage_dir = f"{self.images_path}/{season.title.remote_id}/{season.remote_id}/{remote_id}"
        episode.poster = self.delay_download_poster(episode_info.packshot, storage_dir)

        episode.season = season
        episode.number = episode_info.num
        episode.title = season.title
        episode.slug = f"{season.slug}-episode-{episode.number}-{episode.remote_id}"
        episode.caption = episode_info.title
        episode.playback_url = episode_info.playback_options
        episode.save()

    def _get_or_create_title(self, slug) -> Start2Title:
        try:
            return Start2Title.objects.get(slug=slug)
        except DoesNotExist:
            return Start2Title(
                slug=slug,
                created_during_import=self.uid,
            )

    def import_movie(self, slug: str):
        with self.log_import_error(custom_message=f"Failed to import movie slug: {slug}"):
            self._import_movie(slug)

    def _import_movie(self, slug):
        title = self._get_or_create_title(slug)
        title.is_series = False
        movie_info = self.start_api.get_movie_info(slug)
        self.movies_genres |= {genre_info.url for genre_info in movie_info.genres}
        self.import_title(title, movie_info)

    def import_series(self, slug):
        with self.log_import_error(custom_message=f"Failed to import series with slug: {slug}"):
            self._import_series(slug)

    def _import_series(self, slug):
        series_info = self.start_api.get_series_info(slug)
        title = self._get_or_create_title(slug)
        for brief_season_info in series_info.items:
            self.delay_import_season(
                brief_season_info=brief_season_info,
                title=title,
            )
        title.is_series = True
        self.series_genres |= {genre_info.url for genre_info in series_info.genres}
        self.import_title(title, series_info)

    def import_title(self, title: Start2Title, title_info: MovieInfo | SeriesInfo):
        title_remote_id = title_info.uid
        with self.log_import_error(custom_message=f"Failed to import title, remote id = {title_remote_id}"):
            self._import_title(title, title_info)

    def _import_title(self, title: Start2Title, title_info: MovieInfo | SeriesInfo):
        """Common code for import movies and series."""
        title.is_published = title_info.is_published
        title.updated_during_import = self.uid
        if not self.can_update(title):
            return title.save()

        if title_info.kinopoisk_id:
            try:
                title.kp_id = int(title_info.kinopoisk_id)
            except ValueError:
                title.kp_id = None

        # use "uid" instead of "_id" field, because "_underscore" are not recommended for external use.
        title.remote_id = title_info.uid
        title.original_title = title_info.title_original
        title.caption = title_info.title
        title.playback_url = title_info.playback_options
        title.description = title_info.description
        if isinstance(title_info, MovieInfo):
            title.duration = title_info.duration

        years = title.years = []

        if title_info.year:
            years.append(title_info.year)
        if release_year_start := title_info.release_year_start:
            years.append(release_year_start)
        if release_year_end := title_info.release_year_end:
            years.append(release_year_end)

        if isinstance(title_info, SeriesInfo):
            for season in title_info.items:
                if season.year:
                    years.append(season.year)

        if release_date := title_info.release_date:
            title.release_date = datetime.fromtimestamp(release_date / 1000)

        title.age_rating = _extract_int(title_info.rating_age)
        title.rating = title_info.rating_start
        title.kp_rating = title_info.rating_kp
        title.imdb_rating = title_info.rating_imdb
        title.url = title_info.url

        storage_dir = f"{self.images_path}/{title.remote_id}"
        title.poster = self.delay_download_poster(title_info.vertical, storage_dir)
        title.poster_horizontal = self.delay_download_poster(title_info.horizontal, storage_dir)
        title.poster_background = self.delay_download_poster(title_info.background, storage_dir)
        title.title_logo = self.delay_download_poster(title_info.logotype, storage_dir)

        if title.is_series:
            title.categories = [self.series_category]
        else:
            title.categories = [self.movies_category]

        # get genres for title
        genres_info: list[BasicGenreInfo] = title_info.genres
        title.genres = []
        for genre_info in genres_info:
            # This  is a bit complicated, because Start API mix genres and categories.
            # For now, if we don't have genre in our genres_cache - then we skip this genre.
            genre_url = genre_info.url
            genre = self.genres_cache.get(genre_url)
            if genre:
                title.genres.append(genre)

        title.for_kids = title_info.for_kids
        title.countries = self._get_countries_for_title(title_info)
        title.directors = self._get_persons(title_info.directors)
        title.actors = self._get_persons(title_info.cast)

        self.titles_cache[title.remote_id] = title
        self.similar_titles_cache[title.remote_id] = title_info.similar

        return title.save()

    def _get_countries_for_title(self, title_info: MovieInfo | SeriesInfo):
        """Extract countries from title info."""
        countries = []
        remote_id = title_info.uid
        for country_info in title_info.origin_countries:
            try:
                country = self.countries_cache[country_info.code]
                countries.append(country)
            except KeyError:
                self.add_import_error(
                    "Unknown country", f"Unexpected country code - {country_info.code} in title {remote_id=}"
                )
        return countries

    def get_or_create_person(self, person_info: PersonInfo) -> Optional[AbcvodPerson]:
        """Get or create person."""
        person_id = person_info.alias
        if person_id in self.persons_cache:
            return self.persons_cache[person_id]
        with self.lock:
            person_name = person_info.name
            if not person_name:
                return
            person = AbcvodPerson.get_or_create_person(name=person_name, start_id=person_id)
            return person

    def _get_persons(self, persons_info: list[PersonInfo]) -> list[AbcvodPerson]:
        all_persons = [self.get_or_create_person(person_info) for person_info in persons_info]
        return [person for person in all_persons if person]

    def delay_download_poster(self, posters_info: ImageInfo, storage_dir) -> str:
        """Download poster.

        :returns: path to downloaded image
        """
        poster_url = posters_info.image_1x or posters_info.image_15x
        if poster_url:
            return self.delay_download_image_auto_name(storage_dir, poster_url)
        return ""

    def setup_categories(self):
        """Create default categories."""
        self.movies_category = self.get_or_create_category(caption="Фильмы", slug="movies", priority=1)
        self.series_category = self.get_or_create_category(caption="Сериалы", slug="series", priority=2)

    def get_or_create_category(self, caption, slug, priority) -> Start2Category:
        category: Start2Category
        try:
            category = Start2Category.objects.get(slug=slug)
            if category.do_not_autoupdate:
                return category
        except DoesNotExist:
            category = Start2Category(slug=slug, created_during_import=self.uid)
        category.priority = priority
        category.caption = caption
        category.is_published = True
        category.updated_during_import = self.uid
        return category.save()

    def apply_category_genres(self):
        self.movies_category.genres = [self.genres_cache.get(genre_url) for genre_url in self.movies_genres]
        self.movies_category.save()
        self.series_category.genres = [self.genres_cache.get(genre_url) for genre_url in self.series_genres]
        self.series_category.save()
