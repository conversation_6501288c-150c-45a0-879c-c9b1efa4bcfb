from mongoengine import (
    PULL,
    BooleanField,
    Document,
    EmbeddedDocumentField,
    FloatField,
    ListField,
    ReferenceField,
    StringField,
)

from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodEpisode,
    AbcvodGenre,
    AbcvodSeason,
    AbcvodSimilarTitleReference,
    AbcvodTitle,
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.abcvod.core.models.task import AbcvodImportTracker
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)


class Start2ImportTracker(AbcvodImportTracker):
    job_title = "Start2 VOD content import"
    job_id = "start2-import"


class Start2Document(Document):
    meta = {
        "db_alias": "start2",
        "abstract": True,
    }


class Start2Genre(Start2Document, AbcvodGenre):
    meta = {"collection": "start_genre"}

    # custom start2 fields
    url = StringField(
        help_text="System field for genres, can be used as 'remote_id'. "
        "Start API internally refers to genres by URL or by name."
    )


class Start2Category(Start2Document, AbcvodCategory):
    meta = {"collection": "start_category"}

    # uncommon fields
    genres = ListField(ReferenceField(Start2Genre, reverse_delete_rule=PULL))


class Start2TitleReference(AbcvodTitleReference):
    title = ReferenceField("Start2Title")


class Start2SimilarTitleReference(AbcvodSimilarTitleReference):
    title = ReferenceField("Start2Title")


class Start2Title(Start2Document, AbcvodTitle):
    meta = {"collection": "start_title"}

    # uncommon fields
    genres = ListField(ReferenceField(Start2Genre, reverse_delete_rule=PULL))
    collections = ListField(ReferenceField("Start2Collection"))
    categories = ListField(ReferenceField(Start2Category, reverse_delete_rule=PULL))
    # custom Start2 fields
    url = StringField(verbose_name="URL", help_text="System field, required by our API.")

    similar_titles = ListField(EmbeddedDocumentField(Start2SimilarTitleReference))


class Start2Collection(Start2Document, AbcvodCollection):
    meta = {"collection": "start_collection"}

    # uncommon fields
    titles = TitlesField(Start2TitleReference)
    titles_excluded = TitlesExcludedField(Start2TitleReference)
    categories = ReferenceCriteriaField(Start2Title.categories)
    genres = ReferenceCriteriaField(Start2Title.genres)
    imdb_rating = RatingCriteriaField(Start2Title.imdb_rating)
    kp_rating = RatingCriteriaField(Start2Title.kp_rating)
    is_main = BooleanField(verbose_name="'Main' collection", help_text="System flag, required for Start API.")


Start2Collection.register_delete_rule(Start2Title, "collections", PULL)


class Start2Season(Start2Document, AbcvodSeason):
    meta = {"collection": "start_season"}

    # uncommon fields
    title = ReferenceField(Start2Title)
    # custom Start2 fields
    imdb_rating = FloatField()
    kp_rating = FloatField()
    api_path = StringField(verbose_name="API path", help_text="System field, required by START API.")


class Start2Episode(Start2Document, AbcvodEpisode):
    meta = {"collection": "start_episode"}

    # uncommon fields
    title = ReferenceField(Start2Title)
    season = ReferenceField(Start2Season)
