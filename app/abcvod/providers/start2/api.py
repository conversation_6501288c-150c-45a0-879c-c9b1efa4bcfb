from __future__ import annotations

from enum import Enum
from io import BytesIO
from typing import (
    Optional,
    Union,
)
from urllib.parse import urljoin

from flask import Flask
from pydantic import (
    BaseModel,
    Field,
    computed_field,
)

from app.abcvod.core.api import (
    BaseAPI,
    NotImportant,
)


class ImageInfo(BaseModel):
    image_1x: Union[str, None]
    image_15x: Union[str, None]


class BadgeInfo(BaseModel):
    id: str | NotImportant = None  # '4eac2780-e860-4486-be97-baf093c9238a'
    alias: str | NotImportant = None  # 'novye_serii_pod_posterom_c319acb6-64f4-4bcd-9514-90c04b86a643'
    background_color: str | NotImportant = None  # | NotImportant = None '#FF2800'
    background_color_right: str | NotImportant = None  # ''
    image_1x: Union[str, None] | NotImportant = None  # None
    name: str | NotImportant = None  # 'новые серии под постером'
    size: str | NotImportant = None  # 'medium'
    structure: str | NotImportant = None  # 'single'
    text: str | NotImportant = None  # 'НОВЫЕ СЕРИИ'
    text_right: str | NotImportant = None  # ''
    text_color: str | NotImportant = None  # | NotImportant = None '#FFFFFF'
    text_color_right: str | NotImportant = None  # ''
    type: str | NotImportant = None  # 'badge'
    under_logo_position: bool | NotImportant = None  # False


class BriefGenreInfo(BaseModel):
    title: str  # 'Драма',
    url: str  # '/tv/genres/drama'


class CountryInfo(BaseModel):
    code: str  # 'RU'
    title: str  # 'Россия'


class TitleType(str, Enum):
    series = "Product.Series"
    movie = "Product.Movie"


class SimilarInfo(BaseModel):
    id: str = Field(alias="_id")  # "657873a865052d000d359194"
    uid: str  # "f5b1-4b13-acf3-e8a817ed9085"

    title: str = ""

    age_rating: int | NotImportant  # 18
    alias: str | NotImportant  # "razezd"

    background: ImageInfo | NotImportant = None


class TitleInfo(BaseModel):
    cls: TitleType = Field(alias="_cls")  # 'Product.Series'
    id: str = Field(alias="_id")  # '62ff6550cbfe65000db71e68'
    alias: str  # 'frendzona'
    background: ImageInfo | NotImportant = None
    badges: list[BadgeInfo] | NotImportant = None
    banner: ImageInfo | NotImportant = None
    budget: Optional[str]  # ''
    character: Optional[dict] = None
    description: str | NotImportant = None  # 'Эксперимент по выходу из френдзоны провален: курсовая...
    download_options: Optional[str] = None  # '/download/options/series/6fdd0783-2537-48d1-aed4-2a6b757...'
    downloadable: bool | NotImportant = None  # True
    drm_encrypted: bool | NotImportant = None  # False
    duration: Optional[int]  # 0
    enabled_for_partner: bool | NotImportant = None  # True
    for_kids: bool | NotImportant = None  # False
    genres: list[BriefGenreInfo] | NotImportant = None
    horizontal: ImageInfo | NotImportant = None
    hot_content: bool | NotImportant = None  # False
    in_subscription: bool | NotImportant = None  # True
    is_disabled: bool | NotImportant = None  # False
    is_free: bool | NotImportant = None  # False
    is_maxmind_proxy_enabled: bool | NotImportant = None  # False
    is_premier: bool | NotImportant = None  # False
    is_preview: bool | NotImportant = None  # False
    is_top250: bool | NotImportant = None  # False
    kinopoisk_id: Optional[str]  # '4639616'
    logotype: ImageInfo | NotImportant = None
    origin_countries: list[CountryInfo] | NotImportant = None
    packshot: ImageInfo | NotImportant = None
    play_last_season: bool | NotImportant = None  # False
    playback_options: str | NotImportant = None  # '/stream/options/series/6fdd0783-2537-48d1-aed4-2a6b757405b1'
    premium: bool | NotImportant = None  # False
    quote: str | NotImportant = None  # 'Экс-студент психфака пытается найти причину расставаний и вернуть...
    quote_source: str | NotImportant = None  # ''
    rating_age: str | NotImportant = None  # '18+'
    rating_imdb: Optional[float]  # None
    rating_kp: Optional[float]  # 6.9
    rating_start: Optional[float]  # 6.9
    release_date: int | NotImportant = None  # 1660904784263
    slug: str | NotImportant = None  # '/series/frendzona'
    special: bool | NotImportant = None  # False
    standard: bool | NotImportant = None  # False
    subtitle: str | NotImportant = None  # ''
    thumbnail: ImageInfo | NotImportant = None
    title: str | NotImportant = None  # 'Френдзона'
    title_original: str | NotImportant = None  # ''
    trailer_src: str | NotImportant = None  # '/stream/series/6fdd0783-2537-48d1-aed4-2a6b757405b1/trailer.m3u8'
    uid: str | NotImportant = None  # '6fdd0783-2537-48d1-aed4-2a6b757405b1'
    url: str | NotImportant = None  # '/tv/series/frendzona'
    vertical: ImageInfo | NotImportant = None
    video_cover: str | NotImportant = None  # '/stream/cover/series/6fdd0783-2537-48d1-aed4-2a6b757405b1/cover.m3u8'
    web_url: str | NotImportant = None  # 'https://start.ru/watch/frendzona'
    year: int | NotImportant = None  # 2024

    @computed_field
    @property
    def is_series(self) -> bool:
        return self.cls == TitleType.series


class CollectionInfo(BaseModel):
    alias: str  # 'novoe-na-start'
    banner_size: str | NotImportant = None  # 'small'
    product_display_mode: int | NotImportant = None  # 0
    title: str | NotImportant = None  # 'Премьеры на START'
    url: str  # '/tv/main/novoe-na-start'
    items: list[TitleInfo]
    items_total: int | NotImportant = None  # 20


class BasicGenreInfo(BaseModel):
    slug: str  # '/genres/drama'
    title: str  # 'Драма'
    url: str  # '/tv/genres/drama'


class GenreInfo(BasicGenreInfo):
    id: int = Field(alias="_id")  # 24
    logotype: ImageInfo | NotImportant = None


class CollectionsCatalogueItem(BaseModel):
    """this is incomplete info about collections, full info about each collection should be requested from API."""

    id: str = Field(alias="_id")  # '5c3de19505563d002ece741a'
    items: list[TitleInfo]
    slug: str | NotImportant = None  # '/collection/izvestnye-lichosti-kollekciya'
    title: str | NotImportant = None  # 'Известные личности'
    web_url: str | NotImportant = None  # 'https://start.ru/collection/izvestnye-lichosti-kollekciya'
    weight: Optional[int]  # 20


class GenreCollectionInfo(BaseModel):
    _id: int  # 24
    logotype: ImageInfo | NotImportant = None
    items: list[CollectionsCatalogueItem]
    title: str | NotImportant = None  # 'Биография'
    url: str | NotImportant = None  # '/tv/genres/bio'


class ColorInfo(BaseModel):
    back_color_1: str = ""
    back_color_2: str = ""
    font_color: str = ""


class HashtagInfo(BaseModel):
    id: str | NotImportant = None  # '71317da1-01ef-4766-83d9-f7ef7998471c'
    alias: str | NotImportant = None  # 'avtorskoe-kino'
    title: str | NotImportant = None  # 'Авторское кино'
    text_color: Optional[str]  # '#FFF900'
    url: str | NotImportant = None  # '/hashtags/avtorskoe-kino'


class PersonInfo(BaseModel):
    # If `alias` or `name` is missing - this may be a fictional character.
    alias: Optional[str]  # 'pyotr-mamonov'
    character: Optional[dict]  # always variable information, not important for import.
    filmography: Optional[str]  # '/tv/person/products/pyotr-mamonov'
    name: Optional[str]  # 'Петр Мамонов'
    photo: ImageInfo


class AdditionalContentInfo(BaseModel):
    announcement: bool = Field(default=False)  # False, may also be missing.
    description: str | NotImportant = None  # 'Ежики любят игры и приключения, но иногда робеют и стесняются....'
    packshot: ImageInfo | NotImportant = None
    restrictions: list | NotImportant = None  # []
    title: str | NotImportant = None  # 'Трейлер'
    uid: str | NotImportant = None  # '67e18af3-de90-418a-a23c-1db12f71f5b1'
    video_src: str | NotImportant = None  # '/stream/series/fe060d60-2bcb-4405-acb3-a393fe09019a/67e1...f5b1/promo.m3u8'


class BaseFullTitleInfo(BaseModel):
    """Common info for Series and Movie."""

    cls: TitleType = Field(alias="_cls")  # 'Product.Movie'
    id: str = Field(alias="_id")  # '620f6cf529482ed648853696'
    additional_content: list[AdditionalContentInfo] | NotImportant = None
    alias: str | NotImportant = None  # 'shapito-shou-lyubov-i-druzhba'
    background: ImageInfo
    background_1: ImageInfo | NotImportant = None
    background_2: ImageInfo | NotImportant = None
    background_3: ImageInfo | NotImportant = None
    badges: list[BadgeInfo] | NotImportant = None
    banner: ImageInfo | NotImportant = None
    budget: Optional[str]  # None
    color: ColorInfo | NotImportant = None
    description: str  # 'Одни верят в любовь, а другие в дружбу. Но что действительно реально — это ...'
    display: bool | NotImportant = None  # True
    download_options: Optional[str]  # '/download/options/movie/98c958ef-9274-44d3-8f93-a0e41042e3fb'
    downloadable: bool | NotImportant = None  # True
    drm_encrypted: bool | NotImportant = None  # False
    enabled_for_partner: bool | NotImportant = None  # False
    facts: list | NotImportant = None  # []
    for_kids: bool  # False
    genres: list[BasicGenreInfo]
    hashtags: list[HashtagInfo] | NotImportant = None
    horizontal: ImageInfo
    horizontal_poster: ImageInfo | NotImportant = None
    hot_content: bool | NotImportant = None  # False
    in_subscription: bool | NotImportant = None  # True
    is_4k: bool | NotImportant = None  # False
    is_51: bool | NotImportant = None  # False
    is_disabled: bool  # False
    is_free: bool | NotImportant = None  # False
    is_maxmind_proxy_enabled: bool | NotImportant = None  # False
    is_premier: bool | NotImportant = None  # False
    is_preview: bool  # False
    is_top250: bool | NotImportant = None  # False
    kinopoisk_id: Optional[str]  # '468201', can also be 'NA'
    logotype: ImageInfo
    origin_countries: list[CountryInfo]
    packshot: ImageInfo | NotImportant = None
    play_last_season: bool | NotImportant = None  # False
    playback_options: str  # '/stream/options/movie/98c958ef-9274-44d3-8f93-a0e41042e3fb'
    premium: bool | NotImportant = None  # False
    quote: str | NotImportant = None  # 'Все дороги ведут в шапито. Абсурдистская музыкальная комедия Сергея Лобана...'
    quote_source: str | NotImportant = None  # ''
    rating_age: str  # '18+'
    rating_imdb: Optional[float]  # 7.6
    rating_kp: Optional[float]  # 7.6
    rating_start: Optional[float]  # 7.7
    release_date: int  # 1645165401000
    release_year_end: Optional[int]  # None
    release_year_start: Optional[int]  # None
    restrictions: list | NotImportant = None  # 0} []
    slug: str | NotImportant = None  # '/movie/shapito-shou-lyubov-i-druzhba'
    special: bool | NotImportant = None  # False
    special_description: dict | NotImportant = None  # 4} {'paragraph_1': '', 'paragraph_2': '', 'paragraph_3': '',...}
    standard: bool | NotImportant = None  # False
    start_release_date: Optional[str]  # '2022-02-20T00:01:00'
    subtitle: str | NotImportant = None  # ''
    thumbnail: ImageInfo | NotImportant = None
    title: str  # 'Шапито-шоу: Любовь и дружба'
    title_original: str  # ''
    trailer_src: str | NotImportant = None  # '/stream/movie/98c958ef-9274-44d3-8f93-a0e41042e3fb/trailer.m3u8'
    uid: str  # '98c958ef-9274-44d3-8f93-a0e41042e3fb'
    url: str  # '/tv/movie/shapito-shou-lyubov-i-druzhba'
    vertical: ImageInfo
    vertical_poster: ImageInfo | NotImportant = None
    web_url: str | NotImportant = None  # 'https://start.ru/watch/shapito-shou-lyubov-i-druzhba'
    year: int  # 2012
    cast: list[PersonInfo]
    composers: list[PersonInfo] | NotImportant = None
    directors: list[PersonInfo]
    operators: list[PersonInfo] | NotImportant = None
    producers: list[PersonInfo] | NotImportant = None
    writers: list[PersonInfo] | NotImportant = None
    similar: list[SimilarInfo] = []

    @computed_field
    @property
    def is_published(self) -> bool:
        return not (self.is_preview or self.is_disabled)


class MovieInfo(BaseFullTitleInfo):
    duration: int  # 6078880
    duration_minutes: str | NotImportant = None  # '102 мин'
    weight: Optional[int]  # None


class BriefEpisodeInfo(BaseModel):
    cls: str = Field(alias="_cls")  # 'Episode'
    id: str = Field(alias="_id")  # '0d6dfc61-b978-4b13-acf3-e8a817ed9085'
    announcement: bool | NotImportant = None  # False
    badges: list[BadgeInfo] | NotImportant = None
    description: str | NotImportant = None  # ''
    download_options: Optional[str]  # '/download/options/series/episode/9cfe454d......-b978-4b13-acf3-e8a817ed9085'
    duration: int | NotImportant = None  # 2627320
    num: int  # 1
    packshot: ImageInfo
    playback_options: str  # '/stream/options/series/episode/9cfe454d-11f5-448c...4b13-acf3-e8a817ed9085'
    release_date: int | NotImportant = None  # 1684148251694
    start_release_date: Optional[str]  # None
    title: str  # '1 серия'
    trailer_src: Optional[str] = None  # None
    web_url: str | NotImportant = None  # 'https://start.ru/watch/orlinskaya-tayna-venery/110327'


class EpisodeInfo(BriefEpisodeInfo):
    display: bool | NotImportant = None  # True
    is_free: bool | NotImportant = None  # False
    number: int | NotImportant = None  # 1
    packshot_free: ImageInfo | NotImportant = None
    restrictions: list | NotImportant = None  # 0} []
    uid: str | NotImportant = None  # '304cc005-d82e-4b96-8159-c651ede485e5'
    url: str | NotImportant = None  # '/tv/series/season/a86ec2af-2d3b-46ad-a843-f6421cd11434/304c...-8159-c651ede485e5'


class BaseSeasonInfo(BaseModel):
    cls: str = Field(alias="_cls")  # 'Product.Season'
    id: str = Field(alias="_id")  # '620cf225e9970c7ebb853693'
    alias: str  # 'klubok-i-kolyuchka-1-sezon'
    background: ImageInfo | NotImportant = None
    badges: list[BadgeInfo] | NotImportant = None
    banner: ImageInfo | NotImportant = None
    description: str  # ''
    downloadable: bool | NotImportant = None  # True
    drm_encrypted: bool | NotImportant = None  # False
    enabled_for_partner: bool | NotImportant = None  # True
    for_kids: bool | NotImportant = None  # True
    hot_content: bool | NotImportant = None  # False
    in_subscription: bool | NotImportant = None  # True
    is_disabled: bool  # False
    is_free: bool | NotImportant = None  # False
    is_maxmind_proxy_enabled: bool | NotImportant = None  # False
    is_premier: bool | NotImportant = None  # False
    is_preview: bool  # False
    is_top250: bool | NotImportant = None  # False
    items: list[EpisodeInfo] | NotImportant = None
    logotype: ImageInfo | NotImportant = None
    num: int  # 1
    packshot: ImageInfo | NotImportant = None
    play_last_season: bool | NotImportant = None  # False
    playback_options: str | NotImportant = None  # '/stream/options/series/season/a86ec2af-2d3b-46ad-a843-f6421cd11434'
    premium: bool | NotImportant = None  # False
    release_date: int | NotImportant = None  # 1645004550000
    slug: str | NotImportant = None  # '/series/klubok-i-kolyuchka/season_1'
    special: bool | NotImportant = None  # False
    standard: bool | NotImportant = None  # False
    subtitle: str | NotImportant = None  # '26 серий'
    thumbnail: ImageInfo | NotImportant = None
    title: str  # 'Клубок и Колючка: 1 сезон'
    title_original: str  # ''
    uid: str  # 'a86ec2af-2d3b-46ad-a843-f6421cd11434'
    vertical: ImageInfo

    @computed_field
    @property
    def is_published(self) -> bool:
        return not (self.is_preview or self.is_disabled)


class BriefSeasonInfo(BaseSeasonInfo):
    display: bool | NotImportant = None  # True
    download_options: Optional[str]  # '/download/options/series/season/a86ec2af-2d3b-46ad-a843-f6421cd11434'
    number: int | NotImportant = None  # 1
    publish: str | NotImportant = None  # '2022-02-16T12:42:30'
    rating_age: str | NotImportant = None  # '0+'
    restrictions: list | NotImportant = None  # 0} []
    url: str  # '/tv/series/season/a86ec2af-2d3b-46ad-a843-f6421cd11434'
    year: int | NotImportant = None  # 2020


class FullSeasonInfo(BaseSeasonInfo):
    additional_content: list[AdditionalContentInfo] | NotImportant = None
    background_1: ImageInfo | NotImportant = None
    background_2: ImageInfo | NotImportant = None
    background_3: ImageInfo | NotImportant = None
    budget: Optional[str]  # None
    items_total: int | NotImportant = None  # 9
    facts: list | NotImportant = None  # 0} []
    quote: str | NotImportant = None  # ''
    quote_source: str | NotImportant = None  # ''
    rating_imdb: Optional[float]  # 0
    rating_kp: Optional[float]  # 0
    rating_start: Optional[float]  # 0
    release_year_end: Optional[int]  # None
    release_year_start: int | NotImportant = None  # 2022
    start_release_date: Optional[str]  # '2022-05-13T00:01:00'
    trailer_src: Optional[str]  # None
    web_url: str | NotImportant = None  # 'https://start.ru/watch/smychok/season-1'
    items: list[BriefEpisodeInfo]


class SeriesInfo(BaseFullTitleInfo):
    items: list[BriefSeasonInfo]
    items_total: int | NotImportant = None  # 1
    publish: str | NotImportant = None  # '2022-02-16T12:03:43'


class Start2API(BaseAPI):
    """Class for connection to Start API."""

    def __init__(self, api_key, api_url="https://api.start.ru", http_retries=5):
        """Init.

        :param api_key: Secret key for access API
        :param api_url: Base URL for API
        :param http_retries: Retries count for HTTP requests
        """
        super().__init__(http_retries=http_retries)

        self.api_url = api_url
        self.default_request_params = {
            "apikey": api_key,
        }

    def get_genres(self) -> list[GenreInfo]:
        """Returns categories from Start API.

        For some reason, "genres" endpoint in Start API is actually "categories".
        """
        url = f"{self.api_url}/tv/genres"
        response: list[dict] = self.get_json_response(url=url)
        return [GenreInfo(**raw_genre_info) for raw_genre_info in response]

    def get_genre_collection_info(self, slug: str) -> GenreCollectionInfo:
        """Get all content for Start Genre."""
        catalogue_url = f"{self.api_url}/tv/genres/{slug}"
        raw_response = self.get_json_response(url=catalogue_url)
        return GenreCollectionInfo(**raw_response)

    def get_movie_info(self, remote_id: str) -> MovieInfo:
        return MovieInfo(**self.get_movie_info_raw(remote_id))

    def get_movie_info_raw(self, remote_id: str) -> dict:
        title_url = f"{self.api_url}/tv/movie/{remote_id}"
        return self.get_json_response(url=title_url)

    def get_series_info(self, remote_id: str) -> SeriesInfo:
        return SeriesInfo(**self.get_series_info_raw(remote_id))

    def get_series_info_raw(self, remote_id: str) -> dict:
        series_url = f"{self.api_url}/tv/series/{remote_id}"
        return self.get_json_response(url=series_url)

    def get_collection_info(self, slug: str) -> CollectionInfo:
        """Return info about collection.

        Key 'items' contain list of movies and series within collection. This is brief info, full info about movies and
        series available only via `get_movie_info` and `get_series_info` methods.
        """
        collection_url = f"{self.api_url}/tv/collection/{slug}"
        raw_collection_info = self.get_json_response(url=collection_url)
        return CollectionInfo(**raw_collection_info)

    def get_main_collection_info(self, slug: str) -> CollectionInfo:
        collection_url = f"{self.api_url}/tv/main/{slug}"
        raw_collection_info = self.get_json_response(url=collection_url)
        return CollectionInfo(**raw_collection_info)

    def get_featured_collection_info(self) -> list[TitleInfo]:
        """Return list of featured movies and series."""
        collection_url = f"{self.api_url}/tv/featured"
        response = self.get_json_response(url=collection_url)
        return [TitleInfo(**raw_title_info) for raw_title_info in response]

    def get_main_collections(self) -> list[CollectionInfo]:
        """Returns main collections from Start API."""
        url = f"{self.api_url}/tv/main"
        raw_response = self.get_json_response(url=url)
        return [CollectionInfo(**info) for info in raw_response]

    def get_image(self, image_path: str) -> BytesIO:
        """Get image from Start API."""
        url = urljoin(self.api_url, image_path)
        response = self.http_session.get(url=url)
        return BytesIO(response.content)

    def get_season_info(self, season_api_path: str) -> FullSeasonInfo:
        return FullSeasonInfo(**self.get_season_info_raw(season_api_path))

    def get_season_info_raw(self, season_api_path: str) -> dict:
        """Returns season from Start API.

        :param season_api_path: relative path for season info in StartAPI
        """
        url = urljoin(self.api_url, season_api_path)
        return self.get_json_response(url=url)


def init_start2_api(app: Flask):
    """Creates Start client using app's config and adds it to app's extensions under `start` key."""
    start2_api = Start2API(
        api_key=app.config["START_API_KEY"],
        api_url=app.config["START_API_URL"],
        http_retries=app.config["START_MAX_RETRIES"],
    )

    app.extensions = getattr(app, "extensions", {})
    app.extensions["start2_api"] = start2_api
