from __future__ import annotations

import hashlib
import logging
import os
import warnings
from abc import (
    ABC,
    abstractmethod,
)
from concurrent.futures import (
    Future,
    as_completed,
)
from contextlib import contextmanager
from functools import cached_property
from io import BytesIO
from typing import (
    Callable,
    Generator,
    Optional,
    Union,
)

from fs.base import FS
from mongoengine import (
    Document,
    DoesNotExist,
)

from app.abcvod.core.exceptions import (
    ApiBadResponseError,
    ApiRequestError,
    BaseApiError,
)
from app.abcvod.core.mixins import MultiThreadMixin
from app.abcvod.core.models.abcvod import (
    AbcvodCollection,
    AbcvodTitle,
    ImportableMixin,
)
from app.abcvod.core.models.task import (
    AbcvodImportTracker,
    TitleImportError,
)
from app.abcvod.models import AbcvodImportInfo
from app.cms.tasks.utils import CeleryTaskProcessor
from app.common.mixins import HttpSessionMixin
from app.common.utils import (
    ping_mongo,
    require_attribute,
    upload_binary_data_to_storage,
)
from app.security.models import User
from app.utils import Timer

logger = logging.getLogger(__name__)


class _LogImportError:
    def __init__(self, *, importer: "BaseImporter", custom_message="", reraise=False):
        self.importer = importer
        self.custom_message = custom_message
        self.reraise = reraise

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            return  # Nothing to log here.

        error_text = f"{exc_type.__name__} - {exc_val}"
        if self.custom_message:
            error_text = f"{self.custom_message}: {error_text}"

        error_type = self.get_error_type(exc_type)
        if isinstance(exc_val, BaseApiError):
            self.importer.add_import_error(
                error_type=error_type,
                error_message=str(exc_val),
                exception=exc_val.original_exception,
            )
        else:
            self.importer.add_import_error(
                error_type=error_type,
                error_message=str(exc_val),
                exception=exc_val,
            )
        logger.exception(error_text)

        if self.reraise or self.importer.reraise_all_log_errors:
            return False  # Exception will be raised.
        return True  # Exception will be suppressed.

    def get_error_type(self, exc_type) -> str:
        if exc_type == ApiRequestError:
            return "Error during request"
        if exc_type == ApiBadResponseError:
            return "Bad data in response"
        return f"Unknown error - {exc_type}"


class _BunchOfImportTasks:
    def __init__(self, *, importer: "BaseImporter"):
        self.importer = importer
        self.deferred_tasks: list[Future] = []

    def submit(self, callable: Callable, *args, **kwargs) -> None:
        """Submit a task to the executor and store the Future."""
        self.deferred_tasks.append(self.importer.executor.submit(callable, *args, **kwargs))

    def __enter__(self) -> Callable:
        return self.submit

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Process all deferred tasks and call their results."""
        for future in self.deferred_tasks:
            with self.importer.log_import_error():
                future.result()


class DownloadImagesMixin(HttpSessionMixin, MultiThreadMixin):
    skip_download_images = False  # Set this attribute externally to True if you don't need images right now.

    def __init__(self, storage: FS, *args, **kwargs):
        super(DownloadImagesMixin, self).__init__(*args, **kwargs)
        self.storage = storage
        self.images_for_delayed_import: list[dict] = []  # list of kwargs for download_image() method.
        self._new_images = 0
        self._skipped_images = 0
        self._force_downloaded_images = 0

    def delay_download_image(self, storage_dir: str, file_name, image_url: str, force_download=False) -> str:
        """Puts a download image task to queue.

        :param storage_dir: dir for image in storage
        :param file_name: name of file in storage
        :param image_url: URI of an image
        :param force_download: always replace existing images with the same name in storage
        :returns: path to file in storage
        """
        storage_path = os.path.join(storage_dir, file_name)
        self.images_for_delayed_import.append(
            {
                "storage_path": storage_path,
                "image_url": image_url,
                "force_download": force_download,
            }
        )
        return storage_path

    def delay_download_image_auto_name(self, storage_dir: str, image_url: str) -> str:
        """Same as delay_download_image(), but image name is md5 from image url.

        FIXME: At the moment, we assume all images are jpg.
        """
        file_name = hashlib.md5(image_url.encode("utf-8")).hexdigest()
        file_name = f"{file_name}.jpg"
        return self.delay_download_image(storage_dir, file_name, image_url)

    def log_images_download_error(self, message):
        logger.exception(message)

    def download_all_images(self):
        logger.info(f"Downloading {len(self.images_for_delayed_import)} images!")
        if self.skip_download_images:
            logger.info("Download images skipped due to importer settings")
            return
        deferred_map = {}
        for kwargs in self.images_for_delayed_import:
            deferred = self.executor.submit(self._download_image, **kwargs)
            deferred_map[deferred] = kwargs
        for future in as_completed(deferred_map):
            kwargs = deferred_map[future]
            try:
                future.result()
            except Exception as ex:
                self.log_images_download_error(f"Failed to import image: {kwargs}. {type(ex).__name__}: {ex}")
        logger.info("Download images complete.")

    def _download_image(self, storage_path, image_url, force_download=False) -> None:
        """Download an images to storage."""
        with self.lock:
            image_exists = self.storage.exists(storage_path)

        if image_exists:
            if not force_download:
                self._skipped_images += 1
                return
            else:
                self._force_downloaded_images += 1
        else:
            self._new_images += 1

        image_bytes = self.get_image(image_url)
        with self.lock:
            upload_binary_data_to_storage(self.storage, content=image_bytes, remote_file_path=storage_path)

    def get_image(self, image_url) -> BytesIO:
        """Receive image binary."""
        response = self.http_session.get(image_url)
        return BytesIO(response.content)


class BaseImporter(MultiThreadMixin, CeleryTaskProcessor, ABC):
    task_tracker_model: type[AbcvodImportTracker]
    task_tracker: AbcvodImportTracker
    reraise_all_log_errors = False  # When True, all caught and muted exceptions will be reraised after being logged.
    title_model: type[AbcvodTitle]  # required for `register_import_result` method.
    collection_model: type[AbcvodCollection]  # required for 'update_collections' method.

    def __init__(self, max_workers: Optional[int] = None, *args, **kwargs):
        super().__init__(max_workers, *args, **kwargs)
        require_attribute(self, "title_model")
        require_attribute(self, "collection_model")

    @property
    def results(self):
        return self.task_tracker.results

    def __exit__(self, exc_type, exc_val, exc_tb):
        super(BaseImporter, self).__exit__(exc_type, exc_val, exc_tb)
        self.register_import_result()

    def register_import_result(self):
        """#84198 - Store info for triggering service, which will rebuild search index."""
        db_name = self.title_model._get_db().name
        try:
            import_info = AbcvodImportInfo.objects.get(db_name=db_name)
        except DoesNotExist:
            import_info = AbcvodImportInfo(db_name=db_name)
        import_info.status = self.task_tracker.status
        import_info.save()

    def handle(self):
        super(BaseImporter, self).handle()
        self.before_import()
        self._import_procedure()
        self.after_import()

    def _import_procedure(self):
        self._ensure_managed()  # just in case
        with Timer() as timer:
            self.import_procedure()
        self.results["Base info import time"] = timer.time

    @abstractmethod
    def import_procedure(self):
        """Everything about actual import procedure goes here.

        You shouldn't call this outside context manager.
        """
        raise NotImplementedError

    def before_import(self):
        self._log_basic_import_info()

    def after_import(self):
        self.update_collections()

    def _log_basic_import_info(self):
        if not self.max_workers:
            self.add_warning("'max workers' parameter is not set. It will automatically set to CPU count + 4 (max 32).")
        self.task_tracker.add_log_message(f"Workers count: {self.max_workers or 'Not set (auto).'}")
        ping = ping_mongo(self.title_model)
        self.add_log(f"Ping to database: {ping}")
        if ping > 30:
            self.add_warning(f"High ping to database - {ping}!")
        self.task_tracker.save()

    def update_collections(self):
        collections = self.collection_model.objects.filter(is_published=True)
        self.add_log(f"Updating {len(collections)} collections...")
        with Timer() as timer:
            for collection in collections:
                collection.update_collection()
        self.results["Update collections time"] = timer.time

    @cached_property
    def uid(self) -> str:
        """Backward compatibility / shortcut."""
        return str(self.task_tracker.id)

    def add_title_error(self, title_remote_id: Union[str, int], error_text: str):
        """Shortcut."""
        warnings.warn(
            "Method 'add_title_error' is deprecated, use 'add_import_error' instead.", DeprecationWarning, stacklevel=2
        )
        self.task_tracker.titles_with_error.append(
            TitleImportError(title_id=str(title_remote_id), error_message=error_text)
        )

    def add_import_error(self, error_type: str, error_message: str, exception: Exception | None = None):
        """Shortcut."""
        self.task_tracker.add_import_error(error_type, error_message, exception)

    def log_import_error(self, *, custom_message: str = "", reraise=False):
        return _LogImportError(
            importer=self,
            custom_message=custom_message,
            reraise=reraise or self.reraise_all_log_errors,
        )

    def bunch_of_tasks(self):
        return _BunchOfImportTasks(importer=self)

    @classmethod
    def create_tracker(cls, task_trigger: str, user: User | None = None):
        """Shortcut."""
        return cls.task_tracker_model.create(task_trigger=task_trigger, user=user)

    def mark_unpublished(self, collections: dict[str, type[Document]]):
        """DRY method, unpublish stale documents and collect info about created and updated documents.

        :param collections: dict, like {"Titles": ProviderTitle, "Episodes": ProviderEpisode, "Seasons": ProviderSeason}
        """
        self.add_log("Unpublish stale documents...")

        created = self.results["Created"] = {}
        updated = self.results["Updated"] = {}
        unpublished = self.results["Unpublished"] = {}

        for name, model in collections.items():
            # Marks all objects as unpublished if they have not been imported during last import task
            docs_to_unpublish = model.objects(updated_during_import__ne=self.uid, is_published=True)
            if num_unpublished := docs_to_unpublish.count():
                with Timer() as timer:
                    for doc in docs_to_unpublish:
                        doc.is_published = False
                        doc.save()
                self.add_log(f"Unpublished {num_unpublished} '{name}', took {timer.time}")

            # collect stats about created and updated objects
            num_created = model.objects(created_during_import=self.uid).count()
            num_updated = model.objects(updated_during_import=self.uid, created_during_import__ne=self.uid).count()
            unpublished[name] = num_unpublished
            created[name] = num_created
            updated[name] = num_updated


class ImporterWithImages(DownloadImagesMixin, BaseImporter, ABC):
    """Variant of importer, which takes care of images and tracks images import time."""

    def download_all_images(self):
        with Timer() as timer:
            super(ImporterWithImages, self).download_all_images()
        results = self.results["Images"] = {}
        results["New images downloaded"] = self._new_images
        results["Images skipped (already have)"] = self._skipped_images
        results["Images force-downloaded"] = self._force_downloaded_images
        results["Images import time"] = timer.time

    def log_images_download_error(self, message):
        super(ImporterWithImages, self).log_images_download_error(message)
        self.add_import_error("Download image error", error_message=message)

    def _import_procedure(self):
        super(ImporterWithImages, self)._import_procedure()
        self.download_all_images()


class DoNotAutoupdateMixin(BaseImporter, ABC):
    """This mixin keeps "do not autoupdate" logic in importer."""

    _ignore_do_not_autoupdate = False
    _can_update_only = None

    @property
    @contextmanager
    def ignore_do_not_autoupdate(self) -> Generator[None, None, None]:
        try:
            self._ignore_do_not_autoupdate = True
            yield
        finally:
            self._ignore_do_not_autoupdate = False

    @contextmanager
    def can_update_only(self, *, models: list[type[Document]]) -> Generator[None, None, None]:
        try:
            self._can_update_only = models
            yield
        finally:
            self._can_update_only = None

    def can_update(self, document: ImportableMixin) -> bool:
        if not document.id:
            return True
        if self._can_update_only:
            return document.__class__ in self._can_update_only
        return self._ignore_do_not_autoupdate or not document.do_not_autoupdate
