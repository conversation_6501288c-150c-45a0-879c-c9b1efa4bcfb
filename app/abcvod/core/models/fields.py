from __future__ import annotations

from typing import Type

from mongoengine import (
    EmbeddedDocumentField,
    ListField,
    ReferenceField,
)

from cmf.core.models import CmsEmbeddedDocument


class AbcvodTitleReference(CmsEmbeddedDocument):
    """Embedded document with a single reference to Title.

    In the past, this was considered a better solution, than plain ReferenceField("AbcvodTitle"), because
    admin can draw list widget with this out of box.
    This structure is used internally within CMS and should never be queried by any service.
    """

    meta = {"abstract": True}

    title = ReferenceField("AbcvodTitle")


# noinspection PyPep8Naming
def TitlesField(title_reference: Type[AbcvodTitleReference]):
    """DRY shortcut for 'titles' field, should be used by AbcvodCollection's descendants.

    This could be a subclass of ListField, but deep inside /site-packages/flask_admin/contrib/mongoengine/ajax.py
    there is strict type comparison line:
        ftype = type(field).__name__
        if ftype == 'ListField' or ftype == 'SortedListField':
    Thus we can't just subclass <PERSON>Field as normal people do, because type will change.
    """
    help_text = (
        "These titles will be explicitly added to current collection. "
        "Content of this field should not be used by any service - "
        "field 'collections' on Title should be used instead."
    )
    return ListField(verbose_name="Included titles", field=EmbeddedDocumentField(title_reference), help_text=help_text)


# noinspection PyPep8Naming
def TitlesExcludedField(title_reference: Type[AbcvodTitleReference]):
    """DRY shortcut for 'titles_excluded' field, should be used by AbcvodCollection's descendants."""
    help_text = (
        "These titles will be explicitly excluded from current collection, even if they match collection criteria."
    )
    return ListField(verbose_name="Excluded titles", field=EmbeddedDocumentField(title_reference), help_text=help_text)
