"""Embedded documents to represent criterias for abcvod collections."""

from mongoengine import (
    DateTimeField,
    Q,
)

from app.common.collections.criterias import AbstractCriteria


class ReleaseDateCriteria(AbstractCriteria):
    min_release_date = DateTimeField(verbose_name="Min release date")
    max_release_date = DateTimeField(verbose_name="Max release date")

    def get_criteria(self) -> Q:
        criteria = Q()
        if self.min_release_date:
            criteria &= Q(release_date__gte=self.min_release_date)
        if self.max_release_date:
            criteria &= Q(release_date__lte=self.max_release_date)
        return criteria
