from __future__ import annotations

from mongoengine import (
    EmbeddedDocument<PERSON>ist<PERSON>ield,
    StringField,
)

from app.cms.models import (
    CeleryTaskTracker,
    TaskStatus,
)
from cmf.core.models import CmsEmbeddedDocument


# FIXME: This is DEPRECATED. Use `ContentImportError` instead.
class TitleImportError(CmsEmbeddedDocument):
    title_id = StringField()
    error_message = StringField()

    def __str__(self):
        return f"Title id: {self.title_id}, error_message = {self.error_message}"


class ContentImportError(CmsEmbeddedDocument):
    error_type = StringField(help_text="Any code name for the error, that will help tracking it.", required=True)
    error_message = StringField(help_text="Brief, 1 line of error message.", required=True)
    exception_details = StringField(help_text="Detailed and full info about occurred exception, not required.")


class AbcvodImportTracker(CeleryTaskTracker):
    job_group_id = "vod-import"

    # FIXME: this attribute is deprecated, use `import_errors` instead.
    titles_with_error = EmbeddedDocumentList<PERSON>ield(
        TitleImportError,
        verbose_name="Titles with error",
        help_text="Detailed info about any import errors, occurred during import of certain titles",
    )

    import_errors = EmbeddedDocumentListField(
        ContentImportError,
        verbose_name="Content import errors",
        help_text="Info about any errors, occurred during the import.",
    )

    def add_import_error(self, error_type: str, error_message: str, exception: Exception | None = None) -> None:
        self.import_errors.append(
            ContentImportError(
                error_type=error_type,
                error_message=error_message,
                exception_details=str(exception) if exception else "",
            )
        )

    def get_done_status(self):
        # TODO: this 'if' statement should be deleted along with `titles_with_error` field.
        if self.titles_with_error:
            return TaskStatus.DONE_WITH_ERRORS
        if self.import_errors:
            return TaskStatus.DONE_WITH_ERRORS
        return super(AbcvodImportTracker, self).get_done_status()

    def get_error_count(self):
        result = super(AbcvodImportTracker, self).get_error_count()
        result += len(self.titles_with_error)  # TODO: delete along with `titles_with_error` field.
        result += len(self.import_errors)
        return result
