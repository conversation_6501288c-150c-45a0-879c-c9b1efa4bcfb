from __future__ import annotations

from typing import Generator

from bson import DBRef
from mongoengine import (
    PULL,
    BooleanField,
    DateTimeField,
    EmbeddedDocumentField,
    FloatField,
    IntField,
    ListField,
    Q,
    QuerySet,
    ReferenceField,
    StringField,
)

from app.abcvod.core.models.criterias import ReleaseDateCriteria
from app.abcvod.core.models.fields import (
    AbcvodTitleReference,
    TitlesExcludedField,
    TitlesField,
)
from app.abcvod.models import (
    AbcvodCopyrightHolder,
    AbcvodCountry,
    AbcvodPerson,
    AbcvodTopic,
)
from app.common.collections.criterias import (
    AgeRatingCriteria,
    YearsCriteria,
)
from app.common.collections.fields import (
    RatingCriteriaField,
    ReferenceCriteriaField,
)
from app.common.collections.models import AbstractCollection
from app.common.models import (
    CreatedUpdatedMixin,
    ImageWithRatioField,
)
from cmf.core.fields import <PERSON>lang<PERSON>ield
from cmf.core.image_field.model import CmsI<PERSON><PERSON><PERSON>pleField
from cmf.core.models import (
    CmsDocument,
    CmsEmbeddedDocument,
)


class SlugOwnerMixin(CmsDocument):
    meta = {"abstract": True, "indexes": ["#slug"]}

    slug = StringField(required=True, max_length=300)


def __collect_slugs(docs: list[SlugOwnerMixin | DBRef]) -> Generator[str, None, None]:
    """Attempt to get attribute may trigger dereference of DBRef, which is good.

    If we have AttributeError - that's broken reference. Broken references are "OK", we just skip them.
    """
    for doc in docs:
        try:
            yield doc.slug
        except AttributeError:
            continue


def _collect_slugs(docs: list[SlugOwnerMixin | DBRef]) -> list[str]:
    # This could be written in 1-liner, but I've decided to not upset my coworkers with python. 1-liner:
    #   return [slug for doc in docs if (slug := getattr(doc, 'slug', None)) is not None]
    return list(__collect_slugs(docs))


class ImportableMixin(CmsDocument):
    """Mixin for all documents, which can be created or updated during automatic import tasks."""

    meta = {
        "abstract": True,
        "indexes": [
            {
                "fields": ["remote_id"],
                "unique": True,
                "partialFilterExpression": {
                    "remote_id": {"$gt": ""},
                },
            },
        ],
    }

    diff_history_ignore_fields = {"updated_during_import"}

    updated_during_import = StringField(
        verbose_name="Updated during import",
        help_text="UID of last task, which has updated this document",
    )
    created_during_import = StringField(
        verbose_name="Created during import",
        help_text="UID of import task, which has created this document",
    )
    do_not_autoupdate = BooleanField(
        verbose_name="Disable autoupdate",
        help_text="Disable scheduled automatic updates for current document",
    )
    remote_id = StringField(
        verbose_name="Remote ID",
        help_text="The ID of this content in the import source",
    )
    is_published = BooleanField()
    end_publish_date = DateTimeField()

    badge_primary = StringField(max_length=18)
    badge_secondary = StringField(max_length=18)

    @classmethod
    def get_do_not_autoupdate_ids(cls, id_field="remote_id") -> set:
        """Return set of remote_ids of documents, which are marked with "do_not_autoupdate" flag.

        If you like - you may choose any "id" field for this purpose. In example: "slug".
        """
        return set(cls.objects.filter(do_not_autoupdate=True).values_list(id_field))

    @classmethod
    def get_published_ids(cls, id_field="remote_id", **kwargs) -> set:
        """Shortcut, return set of ids of documents, which are published.

        :param id_field: any field, which value can be used as "id".
        :param kwargs: additional query params.
        :return: set of ids.
        """
        return set(cls.objects.filter(**kwargs).values_list(id_field))


class AbcvodDocument(CreatedUpdatedMixin, CmsDocument):
    meta = {
        "abstract": True,
        "strict": False,
        "indexes": ["default_caption"],
    }
    ajax_search_fields = ("default_caption",)

    caption = MultilangField(required=True, max_length=300)
    default_caption = StringField(help_text="System field for simple search by caption")

    def __str__(self):
        # May happen for some old documents
        if not self.caption:
            return str(self.id) or ""
        return str(self.caption.translations.get("default") or self.id)

    def _set_default_caption(self):
        self.default_caption = str(self.caption)

    def clean(self):
        self._set_default_caption()
        super(AbcvodDocument, self).clean()


class AbcvodGenre(SlugOwnerMixin, ImportableMixin, AbcvodDocument):
    meta = {
        "abstract": True,
        "indexes": [
            {"fields": ["slug"], "unique": True},
        ],
    }

    priority = IntField()
    for_kids = BooleanField(verbose_name="For kids", help_text="Used by API. Importers does not use this flag.")


class AbcvodCategory(SlugOwnerMixin, ImportableMixin, AbcvodDocument):
    meta = {
        "abstract": True,
        "indexes": [
            {"fields": ["slug"], "unique": True},
        ],
    }
    # uncommon_fields
    genres = ListField(ReferenceField(AbcvodGenre, reverse_delete_rule=PULL))
    # common fields
    priority = IntField()
    for_kids = BooleanField(verbose_name="For kids", help_text="Used by API. Importers does not use this flag.")

    # Slug fields, required for front-end optimization.
    genre_slugs = ListField(verbose_name="Genre slugs", field=StringField(), help_text="System field.")

    def clean(self):
        super().clean()
        self.genre_slugs = _collect_slugs(self.genres)


class AbcvodSimilarTitleReference(CmsEmbeddedDocument):
    """Class to keep a reference to a similar title.

    May be extended in the future to keep additional information about the similarity or
    some kind of denormalized data.
    """

    meta = {"abstract": True}

    title = ReferenceField("AbcvodTitle")
    slug = StringField()

    def clean(self):
        try:
            self.slug = self.title.slug if self.title else ""
        except AttributeError:
            # Probably broken reference.
            self.slug = ""


class AbcvodTitle(SlugOwnerMixin, ImportableMixin, AbcvodDocument):
    meta = {
        "abstract": True,
        "indexes": [
            {"fields": ["slug"], "unique": True},
        ],
    }

    # uncommon fields
    genres = ListField(ReferenceField(AbcvodGenre, reverse_delete_rule=PULL))
    # ATTENTION!  We cannot add `reverse_delete_rule`, for `collections` field, but we must.
    # Read more about the problem  below "AbcvodCollection" class.
    collections = ListField(ReferenceField("AbcvodCollection"))
    categories = ListField(ReferenceField(AbcvodCategory, reverse_delete_rule=PULL))
    similar_titles = ListField(EmbeddedDocumentField(AbcvodSimilarTitleReference))

    # common fields
    original_title = StringField()
    playback_url = StringField(max_length=2048)
    description = MultilangField()
    duration = IntField()

    poster = CmsImageSimpleField(verbose_name="Vertical poster", storage_path="cms/abcvod")
    poster_horizontal = CmsImageSimpleField(verbose_name="Horizontal poster", storage_path="cms/abcvod")
    poster_background = CmsImageSimpleField(verbose_name="Background poster", storage_path="cms/abcvod")
    title_logo = CmsImageSimpleField(verbose_name="Title logo", storage_path="cms/abcvod")

    countries = ListField(ReferenceField(AbcvodCountry))
    actors = ListField(ReferenceField(AbcvodPerson))
    directors = ListField(ReferenceField(AbcvodPerson))
    topics = ListField(ReferenceField(AbcvodTopic, reverse_delete_rule=PULL))
    copyright_holders = ListField(ReferenceField(AbcvodCopyrightHolder, reverse_delete_rule=PULL))

    years = ListField(IntField())
    release_date = DateTimeField()
    age_rating = IntField()
    for_kids = BooleanField(verbose_name="For kids", help_text="Allowed in 'Kids' profile")
    rating = FloatField()
    imdb_rating = FloatField()
    kp_rating = FloatField()
    kp_id = IntField()
    imdb_id = StringField()
    is_series = BooleanField()
    drm_required = BooleanField(help_text="Content requires DRM for playback.")

    # Slug fields, required for front-end optimization.
    genres_slugs = ListField(verbose_name="Genre slugs", field=StringField(), help_text="System field.")
    categories_slugs = ListField(verbose_name="Categories slugs", field=StringField(), help_text="System field.")
    collections_slugs = ListField(verbose_name="Collections slugs", field=StringField(), help_text="System field.")
    actors_slugs = ListField(verbose_name="Actor slugs", field=StringField(), help_text="System field.")
    directors_slugs = ListField(verbose_name="Directors slugs", field=StringField(), help_text="System field.")

    @property
    def is_movie(self):
        return not self.is_series

    def clean(self):
        super().clean()

        if self.years:
            self.years = sorted(set(self.years))

        # Update '*_slugs' fields.
        self.genres_slugs = _collect_slugs(self.genres)
        self.categories_slugs = _collect_slugs(self.categories)
        self.collections_slugs = _collect_slugs(self.collections)
        self.actors_slugs = _collect_slugs(self.actors)
        self.directors_slugs = _collect_slugs(self.directors)


class AbcvodSeason(SlugOwnerMixin, ImportableMixin, AbcvodDocument):
    meta = {
        "abstract": True,
        "indexes": [
            {"fields": ["slug"], "unique": True},
        ],
    }
    # uncommon fields
    title = ReferenceField(AbcvodTitle)

    # common fields
    number = IntField(required=True)
    description = MultilangField()
    original_title = StringField()

    # Slug fields, required for front-end optimization.
    title_slug = StringField(verbose_name="Title slug", help_text="System field.")

    def __str__(self):
        default_str = super(AbcvodSeason, self).__str__()
        if not self.title:
            return default_str
        return f"{(str(self.title))} - {default_str}"

    def clean(self):
        super().clean()
        self.title_slug = self.title.slug


class SortingOrder(CmsEmbeddedDocument):
    """Sorting order for Collections."""

    field = StringField(
        choices=(
            ("years", "Years"),
            ("rating", "Rating"),
            ("age_rating", "Age rating"),
            ("kp_rating", "KP rating"),
            ("imdb_rating", "IMDB rating"),
            ("release_date", "Release date"),
            ("created_at", "Created at"),
        )
    )
    order = IntField(
        verbose_name="Sort order",
        choices=(
            (1, "Ascending"),
            (-1, "Descending"),
        ),
    )


class AbcvodCollection(SlugOwnerMixin, ImportableMixin, AbcvodDocument, AbstractCollection):
    meta = {
        "abstract": True,
        "indexes": [
            {"fields": ["slug"], "unique": True},
        ],
    }
    # uncommon fields, need to redefine
    titles = TitlesField(AbcvodTitleReference)
    titles_excluded = TitlesExcludedField(AbcvodTitleReference)
    categories = ReferenceCriteriaField(AbcvodTitle.categories)
    genres = ReferenceCriteriaField(AbcvodTitle.genres)
    imdb_rating = RatingCriteriaField(AbcvodTitle.imdb_rating)
    kp_rating = RatingCriteriaField(AbcvodTitle.kp_rating)
    age_rating = EmbeddedDocumentField(AgeRatingCriteria)

    # common fields
    actors = ReferenceCriteriaField(AbcvodTitle.actors)
    directors = ReferenceCriteriaField(AbcvodTitle.directors)
    countries = ReferenceCriteriaField(AbcvodTitle.countries)
    topics = ReferenceCriteriaField(AbcvodTitle.topics)
    copyright_holders = ReferenceCriteriaField(AbcvodTitle.copyright_holders)
    years = EmbeddedDocumentField(YearsCriteria)
    release_date = EmbeddedDocumentField(ReleaseDateCriteria)
    sorting_orders = ListField(EmbeddedDocumentField(SortingOrder))

    sort = IntField()
    posters = ListField(ImageWithRatioField())

    # fields for similar content feature
    use_for_similar = BooleanField(
        help_text="Allows to use the collection as a source for similar titles for titles that are in the collection."
    )
    titles_count = IntField(
        help_text="Number of titles in the collection.",
    )  # must be updated every time the collection is updated to keep the number of titles in sync.

    def _explicit_criterias_has_changed(self, db_version: "AbcvodCollection") -> bool:
        if len(self.titles) != len(db_version.titles):
            return True
        if len(self.titles_excluded) != len(db_version.titles_excluded):
            return True
        db_title_ids = {title_reference.title.id for title_reference in db_version.titles}
        self_title_ids = {title_reference.title.id for title_reference in self.titles}
        if self_title_ids != db_title_ids:
            return True
        db_title_excluded_ids = {title_reference.title.id for title_reference in db_version.titles_excluded}
        self_title_excluded_ids = {title_reference.title.id for title_reference in self.titles_excluded}
        if self_title_excluded_ids != db_title_excluded_ids:
            return True
        return False

    def apply_explicit_include_criteria(self, criteria: Q) -> Q:
        if self.titles:
            return Q(id__in=[title_reference.title.id for title_reference in self.titles]) | criteria
        return criteria

    def apply_explicit_exclude_criteria(self, queryset: QuerySet) -> QuerySet:
        if self.titles_excluded:
            return queryset.filter(id__nin=[title_reference.title.id for title_reference in self.titles_excluded])
        return queryset

    def save(self, **kwargs):
        save_result = super().save(**kwargs)
        if self.use_for_similar:
            self.titles_count = self.find_titles().count()
            return super().save(**kwargs)
        return save_result


# DO THIS FOR CHILD MODELS.
#
# In "Titles" model we cannot simply do:
#     collections = ListField(ReferenceField("AbcvodCollection", reverse_delete_rule=PULL))
#
# Because for current version of MongoEngine, this line of code leads to `mongoengine.errors.NotRegistered` exception.
# To avoid the problem, do `AbcvodCollection.register_delete_rule(AbcvodTitle, 'collections', PULL)` after
# declaration of `AbcvodCollection` class.
AbcvodCollection.register_delete_rule(AbcvodTitle, "collections", PULL)


class AbcvodEpisode(SlugOwnerMixin, ImportableMixin, AbcvodDocument):
    meta = {
        "abstract": True,
        "indexes": [
            {"fields": ["slug"], "unique": True},
        ],
    }
    # uncommon fields, need to redefine
    title = ReferenceField(AbcvodTitle)
    season = ReferenceField(AbcvodSeason)
    # common fields
    poster = CmsImageSimpleField(storage_path="cms/abcvod")
    number = IntField(required=True)
    original_title = StringField()
    description = MultilangField()
    playback_url = StringField(max_length=2048)
    release_date = DateTimeField()
    duration = IntField()

    # Slug fields, required for front-end optimization.
    title_slug = StringField(verbose_name="Title slug", help_text="System field.")
    season_slug = StringField(verbose_name="Season slug", help_text="System field.")

    def clean(self):
        super().clean()
        self.title_slug = self.title.slug if self.title else ""
        self.season_slug = self.season.slug if self.season else ""
