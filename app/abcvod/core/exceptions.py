from __future__ import annotations


class BaseApiError(Exception):
    """Base class for errors, registered during interaction with API."""

    def __init__(self, request_url: str, original_exception: Exception):
        self.request_url = request_url
        self.original_exception = original_exception
        super().__init__(f"Error during request '{request_url}'")


class ApiRequestError(BaseApiError):
    """Any exception, raised during request to API."""


class ApiBadResponseError(BaseApiError):
    """Response data is corrupted somehow (bad structure, missing data etc.)."""


class ImportException(Exception):
    """Use when something violates logic of content import procedure."""
