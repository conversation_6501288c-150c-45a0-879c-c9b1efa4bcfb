from __future__ import annotations

import json
import logging
from typing import (
    Callable,
    Generator,
    Optional,
    Type,
)
from urllib import parse

import mongoengine
from bson import ObjectId
from flask import (
    flash,
    redirect,
    request,
    url_for,
)
from flask_admin import Admin
from markupsafe import (
    Markup,
    escape,
)
from requests import HTTPError

from app.abcvod.core.exceptions import BaseApiError
from app.abcvod.core.forms import (
    AddOrRemoveTopicsForm,
    RatingForm,
    SimilarTitlesForm,
)
from app.abcvod.core.models.abcvod import (
    AbcvodCategory,
    AbcvodCollection,
    AbcvodEpisode,
    AbcvodGenre,
    AbcvodSeason,
    AbcvodTitle,
)
from app.abcvod.models import AbcvodTopic
from app.auth import expose
from app.cms.tasks.utils import (
    ReimportException,
    TaskAlreadyStartedException,
)
from app.cms.tasks.views import CeleryTaskView
from app.cms.utils import CmsJsonResponse
from app.common.form import block
from app.common.models import ImageWithRatio
from app.common.utils import require_attribute
from cmf.core.form.widgets import TableListWidget
from cmf.core.mixins import RightMenuItem
from cmf.core.view import (
    CmsBaseModelView,
    RightMenuMixin,
)
from cmf.core.views.mass_actions_mixin import (
    CustomAction,
    EditMultipleAction,
    MassActionMixin,
)

logger = logging.getLogger()


class AbcVodViewMixin(CmsBaseModelView):
    """Mixin for coordination of VOD views."""

    # VOD name, required for urls and coordination between VOD classes.
    vod_name: str  # used in views for urls and formatters
    can_create = False
    can_delete = False

    # All models of VOD.
    genre_class: Type[AbcvodGenre]
    category_class: Type[AbcvodCategory]
    title_class: Type[AbcvodTitle]
    season_class: Type[AbcvodSeason]
    episode_class: Type[AbcvodEpisode]
    collection_class: Type[AbcvodCollection]

    def get_save_return_url(self, model, is_created=False):
        # Redefine base logic.
        # According base logic, if there is field "url" in request, we will be redirected to said url.
        # And some documents may contain field "url", which is going to request, so we will be redirected
        # to non-existent url, like `/tv/movie/vanya-dmitrienko-puskay` instead of `/admin/titles/edit/?id=...`
        return self.get_url(".index_view")

    def get_one(self, id):
        """Return a single model instance by its ID or slug."""
        if "slug" not in self.model._fields_ordered:
            return super().get_one(id)

        try:
            return self.get_query().filter(pk=id).get()
        except (mongoengine.ValidationError, mongoengine.DoesNotExist):
            pass

        try:
            return self.get_query().filter(slug=str(id)).get()
        except (mongoengine.ValidationError, mongoengine.DoesNotExist):
            pass
        return None


class FieldsOrderMixin(CmsBaseModelView):
    """Mixin, that divides ABCVod views to 4 main blocks.

    The blocks are:
    "Main info" - fundamental info like Title, Description etc.
    "Flags" - any editable control flags, like "is published", "disable autoupdate" etc.
    "Additional info" - some secondary and extra fields. "Extra" fields may be different among VOD providers.
    "System info fields" - collapsed block with read-only system fields, good for troubleshooting and debugging.
    """

    _top_fields = ()
    _badge = (
        "badge_primary",
        "badge_secondary",
    )
    _flags = ()
    _additional_fields = ()
    _common_system_fields = (
        "slug",
        "default_caption",
        "updated_at",
        "created_at",
    )
    _importable_mixin_system_fields = (
        "updated_during_import",
        "created_during_import",
        "remote_id",
    )
    _system_fields = _common_system_fields + _importable_mixin_system_fields

    _top_fields_label = "Main info"
    _badge_label = "Badge"
    _flags_label = "Flags"
    _additional_fields_label = "Additional info"
    _system_fields_label = "System Info Fields"

    @property
    def form_widget_args(self) -> dict:
        if self._system_fields:
            return {field_name: {"readonly": True} for field_name in self._system_fields}
        return {}

    @staticmethod
    def _block(title, *rules, collapsed=True):
        """Utility wrapper around `block` to skip empty blocks."""
        if not rules:
            return []
        return block(title, *rules, collapsed=collapsed)

    @property
    def form_rules(self):
        return (
            *self._block(self._top_fields_label, *self._top_fields, collapsed=False),
            *self._block(self._badge_label, *self._badge, collapsed=False),
            *self._block(self._flags_label, *self._flags, collapsed=False),
            *self._block(self._additional_fields_label, *self._additional_fields, collapsed=False),
            *self._block(self._system_fields_label, *self._system_fields, collapsed=True),
        )


class FormattersMixin(AbcVodViewMixin):
    """All formatters we need."""

    def __init__(self, *args, **kwargs):
        super(FormattersMixin, self).__init__(*args, **kwargs)
        self.series_formatter = self.create_series_formatter()
        self.collections_poster_formatter = self.create_collections_poster_formatter()
        self.season_formatter = self.create_season_formatter()
        self.genres_formatter = self.create_genres_formatter()
        self.seasons_formatter = self.create_seasons_formatter()
        self.episodes_formatter = self.create_episodes_formatter()

    @staticmethod
    def poster_formatter(view, context, model, name):
        value = getattr(model, name)
        if not value:
            return ""
        if isinstance(value, list):
            value = value[0]
        if isinstance(value, ImageWithRatio):
            value = value.path
        if not value.startswith("http"):
            value = url_for("uploads", path=value)
        return Markup(f"<img width='100%' src='{value}'>")

    def create_series_formatter(self) -> Callable:
        def series_formatter(view, context, model, name):
            if not model.title:
                return ""

            return Markup(
                "<a href='{url}'>{caption}</a>".format(
                    url=url_for(f"{self.vod_name}_titles.edit_view", id=model.title.id),
                    caption=model.title.caption.translations.get("default"),
                )
            )

        return series_formatter

    def create_collections_poster_formatter(self) -> Callable:
        return self.poster_formatter

    def create_season_formatter(self) -> Callable:
        def season_formatter(view, context, model, name):
            if not model.season:
                return ""

            return Markup(
                "<a href='{url}'>{caption}</a>".format(
                    url=url_for(f"{self.vod_name}_seasons.edit_view", id=model.season.id),
                    caption=model.season.number,
                )
            )

        return season_formatter

    def create_categories_formatter(self) -> Callable:
        def categories_formatter(view, context, model, name):
            if not hasattr(model, "categories") or not model.categories:
                return ""

            return Markup(
                ", ".join(
                    [
                        "<a href='{url}'>{caption}</a>".format(
                            url=url_for(f"{self.vod_name}_categories.edit_view", id=category.id),
                            caption=str(category),
                        )
                        for category in model.categories
                    ]
                )
            )

        return categories_formatter

    def create_genres_formatter(self) -> Callable:
        def genres_formatter(view, context, model, name):
            return Markup(
                ", ".join(
                    [
                        "<a href='{url}'>{caption}</a>".format(
                            url=url_for(f"{self.vod_name}_genres.edit_view", id=genre.id),
                            caption=genre.caption.translations.get("default"),
                        )
                        for genre in getattr(model, name)
                    ]
                )
            )

        return genres_formatter

    def create_seasons_formatter(self) -> Callable:
        def seasons_formatter(view, context, model, name):
            seasons_count = self.season_class.objects(title=model).count()
            if not seasons_count:
                return ""

            return Markup(
                "<a href='{url}'>{caption}</a>".format(
                    url=url_for(f"{self.vod_name}_seasons.index_view", flt_title_equals=model.id),
                    caption=f"Seasons ({seasons_count})",
                )
            )

        return seasons_formatter

    def create_episodes_formatter(self) -> Callable:
        def episodes_formatter(view, context, model, name):
            episodes_count = self.episode_class.objects(title=model).count()
            if not episodes_count:
                return ""

            return Markup(
                "<a href='{url}'>{caption}</a>".format(
                    url=url_for(f"{self.vod_name}_episodes.index_view", flt_title_equals=model.id),
                    caption=f"Episodes ({episodes_count})",
                )
            )

        return episodes_formatter


class AbcVodBaseView(FormattersMixin, FieldsOrderMixin, CmsBaseModelView):
    """Composition of everything we need for VOD views."""

    def __init__(self, *args, **kwargs):
        require_attribute(self, "genre_class")
        require_attribute(self, "category_class")
        require_attribute(self, "title_class")
        require_attribute(self, "season_class")
        require_attribute(self, "episode_class")
        super().__init__(*args, **kwargs)


class GetJsonFromApiMixin(RightMenuMixin):
    """Mixin allows to observe raw json response from API for current document."""

    # Extra get arguments, that may be passed to api. In example:
    #    extra_json_api_args = {"page": 1}
    # These arguments will be added to page with api response, they expected to be changed by hand.
    # Handling of these arguments must be implemented in `get_api_response` method.
    extra_json_api_args: dict = None

    def get_menu_items(self) -> Generator[RightMenuItem, None, None]:
        yield from super().get_menu_items()

        _id = request.args.get("id")
        extra_args = self.extra_json_api_args or {}

        yield RightMenuItem(
            url=self.get_url(self.endpoint + ".json_view", id=_id, **extra_args),
            endpoint=self.endpoint + ".json_view",
            label="Json from API",
        )

    def get_document(self):
        _id = request.args.get("id")
        return self.model.objects.get(id=_id)

    @expose("/json_view", methods=("GET",))
    def json_view(self):
        document = self.get_document()
        try:
            result = self.get_api_response(document)
            json_lines = json.dumps(
                result,
                sort_keys=True,
                indent=4,
                separators=(",", ": "),
                ensure_ascii=False,
            )
        except HTTPError as e:
            json_lines = f"HTTPError has occured:\n{e}"
        return self.render("common/json_view.html", json_lines=json_lines, model=document)

    def get_api_response(self, document) -> dict | list:
        raise NotImplementedError


class ReimportMixin(RightMenuMixin):
    def get_menu_items(self) -> Generator[RightMenuItem, None, None]:
        yield from super().get_menu_items()

        _id = request.args.get("id")

        yield RightMenuItem(
            url=self.get_url(self.endpoint + ".reimport", id=_id),
            endpoint=self.endpoint + ".reimport",
            label="Reimport from API",
        )

    @expose("/reimport", methods=("GET",))
    def reimport(self):
        document = self.get_document()
        try:
            self.reimport_procedure(document)
            flash("Reimport completed!", category="success")
        except TaskAlreadyStartedException:
            flash("There is already one running import job. Please wait for it and try again.", category="error")
        except ReimportException as e:
            flash(f"Reimport failed: {e}", category="error")
        except Exception as e:
            logger.exception(e)
            exception_text = escape(e)
            message = f"Exception during reimport:<br> {e.__class__.__name__}: {exception_text}"
            if isinstance(e, BaseApiError):
                message += f"<br>{e.original_exception}"
            flash(message, category="error")
        return redirect(self.get_url(".edit_view", id=document.id))

    def reimport_procedure(self, document):
        raise NotImplementedError


class AddOrRemoveTopicsAction(CustomAction):
    name = "add_or_remove_topics"
    text = "Add or remove topics"
    form_class = AddOrRemoveTopicsForm

    @staticmethod
    def handle_action(view: AbcVodTitleView, object_ids: list[str], form: AddOrRemoveTopicsForm):
        titles_queryset = view.model.objects(id__in=object_ids)
        topics_to_add: set[AbcvodTopic] = set(form.topics_to_add.data)
        topics_to_remove: set[AbcvodTopic] = set(form.topics_to_remove.data)

        if not (topics_to_add or topics_to_remove):
            return CmsJsonResponse.error(message="Select topics to add or topics to remove.")

        titles_skipped = 0
        titles_updated = 0

        for title in titles_queryset:
            title: AbcvodTitle
            title_topics = set(title.topics)
            can_add_topics = bool(topics_to_add - title_topics)
            can_remove_topics = bool(topics_to_remove & title_topics)
            if not (can_add_topics or can_remove_topics):
                # Avoid triggering save() when no changes for the title are available.
                titles_skipped += 1
                continue
            title_topics -= topics_to_remove
            title_topics |= topics_to_add
            title.topics = list(title_topics)
            title.save()
            titles_updated += 1

        return CmsJsonResponse.redirect(
            redirect_url=view.get_url(".index_view"),
            message=f"Success! Titles updated = {titles_updated}, titles skipped (already OK) = {titles_skipped}",
        )


class AbcVodTitleView(MassActionMixin, AbcVodBaseView):
    model: Type[AbcvodTitle]
    can_set_page_size = True

    mass_actions = [
        EditMultipleAction(
            name="set_is_published", text="Set 'Is published'", fields=("is_published", "do_not_autoupdate")
        ),
        EditMultipleAction(name="set_age_rating", text="Set 'Age rating'", fields=("age_rating", "do_not_autoupdate")),
        EditMultipleAction(
            name="set_end_publish_date", text="Set 'End publish date'", fields=("end_publish_date", "do_not_autoupdate")
        ),
        AddOrRemoveTopicsAction(),
    ]

    default_filter_equals = {
        "is_published": True,
    }

    list_columns = (
        "slug",
        "caption",
        "poster",
        "rating",
        "age_rating",
        "years",
        "genres",
        "is_published",
        "release_date",
        "end_publish_date",
        "created_at",
        "seasons",
        "episodes",
    )

    column_searchable_list = (
        "default_caption",
        "playback_url",
        "slug",
        "remote_id",
    )

    column_editables = (
        "rating",
        "is_published",
        "caption",
    )

    column_filters = (
        "genres",
        "categories",
        "collections",
        "actors",
        "directors",
        "countries",
        "is_published",
        "is_series",
        "for_kids",
        "topics",
        "copyright_holders",
    )

    _top_fields = (
        "caption",
        "description",
        "years",
        "original_title",
        "release_date",
        "copyright_holders",
        "topics",
        "categories",
        "genres",
        "poster",
        "poster_horizontal",
        "poster_background",
        "title_logo",
    )

    _flags = (
        "is_series",
        "is_published",
        "do_not_autoupdate",
    )

    _additional_fields = (
        "rating",
        "age_rating",
        "playback_url",
        "imdb_rating",
        "kp_rating",
        "kp_id",
        "imdb_id",
        "duration",
        "actors",
        "directors",
        "countries",
        "for_kids",
        "end_publish_date",
        "similar_titles",
    )

    _system_fields = ("drm_required",) + AbcVodBaseView._system_fields

    form_ajax_refs = {
        **AbcvodTopic.get_form_ajax_ref(),  # required by `AddOrRemoveTopicsAction`
    }

    form_args = {
        "similar_titles": {"widget": TableListWidget()},
    }

    form_subdocuments = {
        "similar_titles": {
            "form_subdocuments": {
                None: SimilarTitlesForm(),
            }
        }
    }

    def __init__(self, *args, **kwargs):
        self.model = self.title_class
        super().__init__(*args, **kwargs)

    @property
    def list_formatters(self):
        return {
            "genres": self.genres_formatter,
            "seasons": self.seasons_formatter,
            "episodes": self.episodes_formatter,
        }

    def get_menu_items(self) -> Generator[RightMenuItem, None, None]:
        yield from super().get_menu_items()

        yield RightMenuItem(
            url=self.get_url(f"{self.vod_name}_seasons.index_view", flt_title_equals=request.args.get("id")),
            endpoint=self.endpoint + ".episodes_view",
            label="Seasons",
        )
        yield RightMenuItem(
            url=self.get_url(f"{self.vod_name}_episodes.index_view", flt_title_equals=request.args.get("id")),
            endpoint=self.endpoint + ".episodes_view",
            label="Episodes",
        )


class AbcVodGenreView(AbcVodBaseView):
    can_set_page_size = True

    list_columns = (
        "slug",
        "caption",
        "priority",
        "is_published",
        "for_kids",
        "updated_at",
        "created_at",
    )

    _top_fields = (
        "caption",
        "priority",
    )

    _flags = (
        "do_not_autoupdate",
        "is_published",
        "for_kids",
    )

    column_filters = ("is_published",)

    column_searchable_list = (
        "default_caption",
        "slug",
        "remote_id",
    )

    column_editables = (
        "priority",
        "is_published",
        "for_kids",
    )

    def __init__(self, *args, **kwargs):
        self.model = self.genre_class
        super().__init__(*args, **kwargs)


class AbcVodCategoryView(AbcVodBaseView):
    can_set_page_size = True

    list_columns = (
        "slug",
        "caption",
        "priority",
        "is_published",
        "for_kids",
        "updated_at",
        "created_at",
    )

    column_searchable_list = (
        "default_caption",
        "slug",
        "remote_id",
    )

    column_editables = (
        "priority",
        "is_published",
        "for_kids",
    )

    column_filters = ("genres",)

    _flags = (
        "do_not_autoupdate",
        "is_published",
        "for_kids",
    )

    _top_fields = (
        "caption",
        "priority",
        "genres",
    )

    def __init__(self, *args, **kwargs):
        self.model = self.category_class
        super().__init__(*args, **kwargs)


class AbcVodSeasonView(AbcVodBaseView):
    can_set_page_size = True

    list_columns = [
        "slug",
        "number",
        "updated_at",
        "created_at",
        "is_published",
    ]

    column_filters = (
        "title",
        "is_published",
    )

    _top_fields = (
        "caption",
        "description",
        "number",
    )

    _flags = (
        "do_not_autoupdate",
        "is_published",
    )

    _additional_fields = (
        "title",
        "original_title",
        "end_publish_date",
    )

    @property
    def form_widget_args(self) -> dict:
        result = super(AbcVodSeasonView, self).form_widget_args
        result["title"] = {"readonly": True}
        return result

    column_editables = ("number",)

    column_searchable_list = (
        "slug",
        "remote_id",
    )

    def __init__(self, *args, **kwargs):
        self.model = self.season_class
        super().__init__(*args, **kwargs)

    @property
    def list_formatters(self):
        return {
            "title": self.series_formatter,
        }

    def get_menu_items(self) -> Generator[RightMenuItem, None, None]:
        yield from super().get_menu_items()

        yield RightMenuItem(
            url=self.get_url(f"{self.vod_name}_episodes.index_view", flt_season_equals=request.args.get("id")),
            endpoint=self.endpoint + ".episodes_view",
            label="Episodes",
        )

    def create_form(self, obj=None):
        form = super().create_form()
        url = request.args.get("url")
        get_params = dict(parse.parse_qsl(parse.urlsplit(url).query))
        title_id = self.get_filter_from_query("title", get_params)
        if title_id:
            form.title.data = ObjectId(title_id)
        return form


class AbcVodEpisodeView(AbcVodBaseView):
    can_set_page_size = True

    list_columns = (
        "title",
        "caption",
        "season",
        "number",
        "poster",
        "is_published",
        "updated_at",
        "created_at",
    )

    _top_fields = (
        "caption",
        "description",
        "season",
        "number",
        "poster",
    )

    _flags = (
        "do_not_autoupdate",
        "is_published",
    )

    _additional_fields = (
        "title",
        "original_title",
        "release_date",
        "playback_url",
        "duration",
        "end_publish_date",
    )

    column_editables = ("number",)

    column_searchable_list = (
        "default_caption",
        "playback_url",
        "slug",
        "remote_id",
    )

    column_filters = (
        "title",
        "season",
        "is_published",
    )

    def __init__(self, *args, **kwargs):
        self.model = self.episode_class
        super().__init__(*args, **kwargs)

    @property
    def list_formatters(self):
        return {
            "title": self.series_formatter,
            "season": self.season_formatter,
        }

    def create_form(self, obj=None):
        form = super().create_form()
        url = request.args.get("url")
        get_params = dict(parse.parse_qsl(parse.urlsplit(url).query))

        title_id = self.get_filter_from_query("title", get_params)
        if title_id:
            form.title.data = ObjectId(title_id)

        season_id = self.get_filter_from_query("season", get_params)
        if season_id:
            form.season.data = ObjectId(season_id)
            season = self.season_class.objects.get(id=form.season.data)
            if season.title:
                form.title.data = season.title

        return form


class AbcVodCollectionView(FormattersMixin, FieldsOrderMixin, CmsBaseModelView):
    model: Type[AbcvodCollection]

    can_create = True  # Collections are meant to be manual.
    can_delete = True

    column_list = (
        "caption",
        "posters",
        "is_published",
        "use_for_similar",
        "sort",
    )

    column_filters = (
        "is_published",
        "use_for_similar",
    )

    column_default_sort = "sort"

    column_editables = ("is_published", "use_for_similar")

    column_searchable_list = (
        "default_caption",
        "slug",
    )

    _top_fields = (
        "caption",
        "slug",
        "posters",
        "sort",
    )

    _flags = ("is_published", "use_for_similar")

    _additional_fields = (
        "titles",
        "titles_excluded",
        "years",
        "categories",
        "genres",
        "topics",
        "copyright_holders",
        "actors",
        "directors",
        "countries",
        "imdb_rating",
        "kp_rating",
        "age_rating",
        "sorting_orders",
        "end_publish_date",
    )

    _additional_fields_label = "Collection settings"

    _system_fields = (
        tuple(field for field in FieldsOrderMixin._common_system_fields if field != "slug")
        + FieldsOrderMixin._importable_mixin_system_fields
        + ("titles_count",)
    )

    @property
    def list_formatters(self):
        return {
            "posters": self.collections_poster_formatter,
        }

    form_args = {
        "posters": {
            "table_layout": True,
            "sortable": True,
            "field_args": {"allowed_aspect_ratios": [(2, 3), (16, 9)]},
        },
    }

    form_subdocuments = {
        "age_rating": RatingForm(),
        "imdb_rating": RatingForm(),
        "kp_rating": RatingForm(),
    }

    def get_menu_items(self) -> Generator[RightMenuItem, None, None]:
        yield from super().get_menu_items()

        yield RightMenuItem(
            label="Update collection",
            confirm="Trigger update collection?",
            url=self.get_url(".update_collection", id=request.args.get("id")),
            endpoint=self.endpoint + ".update_collection",
        )
        yield RightMenuItem(
            url=self.get_url(
                f"{self.vod_name}_titles.index_view",
                flt_collections_equals=request.args.get("id"),
                flt_is_published_equals=1,
            ),
            endpoint=self.endpoint + ".titles_view",
            label="Titles",
        )

    @expose("/update_collection", methods=("GET",))
    def update_collection(self):
        """Manual trigger for "update collection" action.

        GET params:
        id: id of Collection object in mongoDB
        """
        collection_id = request.args.get("id")
        collection: AbcvodCollection = self.model.objects.get(id=ObjectId(collection_id))
        collection.update_collection()
        flash("Обновление подборки выполнено", "success")
        return redirect(self.get_url(".edit_view", id=collection_id))


class AbcvodImportTaskView(CeleryTaskView):
    list_template = "abcvod/abcvod_import_task_list.html"
    details_modal_template = "abcvod/details_modal.html"


def init_vod_admin(
    admin: Admin,
    caption: str,
    vod_mixin: Type[AbcVodViewMixin],
    category_view: Type[AbcVodCategoryView],
    genre_view: Type[AbcVodGenreView],
    title_view: Type[AbcVodTitleView],
    season_view: Type[AbcVodSeasonView],
    episode_view: Type[AbcVodEpisodeView],
    collection_view: Type[AbcVodCollectionView],
    parent_name: Optional[str] = None,
) -> None:
    """DRY function, which replaces old "factory" functional.

    Don't call it directly in `app/views.py`. Instead, make `def init_my_vod_admin(admin):` in `/your-vod/views.py`,
    which will call this monstrosity under its hood.
    """
    vod_name = vod_mixin.vod_name
    admin.add_sub_category(caption, parent_name=parent_name)
    admin.add_view(category_view(name="Categories", category=caption, endpoint=f"{vod_name}_categories"))
    admin.add_view(genre_view(name="Genres", category=caption, endpoint=f"{vod_name}_genres"))
    admin.add_view(title_view(name="Titles", category=caption, endpoint=f"{vod_name}_titles"))
    admin.add_view(season_view(name="Seasons", category=caption, endpoint=f"{vod_name}_seasons"))
    admin.add_view(episode_view(name="Episodes", category=caption, endpoint=f"{vod_name}_episodes"))
    admin.add_view(collection_view(name="Collections", category=caption, endpoint=f"{vod_name}_collections"))
