import logging
from concurrent.futures import <PERSON>hr<PERSON>PoolExecutor
from contextlib import <PERSON>bstractContextManager
from threading import Lock
from typing import Optional

logger = logging.getLogger(__name__)


class MultiThreadMixin(AbstractContextManager):
    def __init__(self, max_workers: Optional[int] = None, *args, **kwargs):
        super(MultiThreadMixin, self).__init__(*args, **kwargs)
        self.max_workers = max_workers
        self.lock = Lock()

    def __enter__(self):
        super(MultiThreadMixin, self).__enter__()
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        super(MultiThreadMixin, self).__exit__(exc_type, exc_val, exc_tb)
        self.executor.shutdown(wait=True)
