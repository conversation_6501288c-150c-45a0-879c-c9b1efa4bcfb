from __future__ import annotations

import functools
import logging
import threading
from copy import deepcopy
from typing import Any
from urllib.error import HTTPError

import pydantic
import requests
from requests.exceptions import RetryError

from app.abcvod.core.exceptions import (
    ApiBadResponseError,
    ApiRequestError,
    BaseApiError,
)
from app.common.mixins import HttpSessionMixin

logger = logging.getLogger(__name__)

# Made-up type. Indicates that this field is not really optional, but we don't really care about it.
# Remove this type from a field, if this field is used in import.
#
# Example usage:
# If your pydantic model defines this field, which is not Optional[] and is not  used in current import scenario,
# instead of this:
#    class SeasonInfo(BaseModel):
#       episodeCount: int
#       ...
# Do this:
#    class SeasonInfo(BaseModel):
#        episodeCount: int | NotImportant = None
#       ...
#
# This marks that the field is actually expected to be in the response, but not important in current version of import.
# This preserves the functionality of the documentation (which is #1 reason for using Pydantic in the code), but also
# helps us to avoid scenario, when our 100% correct import script failing, because field-we-dont-really-need
# is SUDDENLY changed structure or is missing since random update on the partner's (provider's) side.
#
NotImportant = Any


def catch_api_error(func):
    """Decorator, will intercept and enrich any exception, which may happen during request to API.

    'Enriched' exceptions will then be used to populate `content_import_errors` field in `AbcvodImportTask`.
    """

    @functools.wraps(func)
    def wrapper(self: BaseAPI, *args, **kwargs):
        try:
            return func(self, *args, **kwargs)
        except BaseApiError:  # may happen if we call decorated method within decorated method.
            raise
        except pydantic.ValidationError as e:
            raise ApiBadResponseError(self.latest_request_url, e) from e
        except Exception as e:
            raise ApiRequestError(self.latest_request_url, e) from e

    return wrapper


class BaseAPI(HttpSessionMixin):
    """Base for classes, which do requests to our partner's API."""

    # default request parameters and headers for each request. Can be used for credentials or mandatory settings.
    default_request_params = {}
    default_headers = {}

    def __init__(self, **kwargs):
        self._thread_local = threading.local()
        super().__init__(**kwargs)

    @property
    def latest_request_url(self):
        return getattr(self._thread_local, "latest_request_url", "")

    @latest_request_url.setter
    def latest_request_url(self, value):
        self._thread_local.latest_request_url = value

    def get_response(self, url: str, get_params: dict[str, str] = None) -> requests.Response:
        """Send GET request to API and get requests.Response.

        responses with code > 400 will raise HTTPError.

        :param url: url for loading content as json
        :param get_params: extra GET params for request
        """
        self.latest_request_url = url  # For error handling. May be displayed in UI.

        request_params = deepcopy(self.default_request_params)
        if get_params:
            request_params.update(get_params)
        try:
            response = self.http_session.get(url, params=request_params, headers=self.default_headers)
            # print(response.request.url)  # uncomment for debug
            response.raise_for_status()
        except (HTTPError, RetryError) as e:
            logger.warning(f"Failed to load data: {e}")
            raise e
        else:
            return response

    def get_json_response(self, url: str, get_params: dict[str, int | str] = None) -> dict | list:
        """Shortcut.

        :param url: url for loading content as json
        :param get_params: extra GET params for request
        :return: dictionary made from response json and error
        """
        response = self.get_response(url=url, get_params=get_params)
        return response.json()
