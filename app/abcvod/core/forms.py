from flask_wtf import FlaskForm
from wtforms.validators import ValidationError
from wtforms.widgets.core import TextInput

from app.abcvod.core.models.abcvod import AbcvodSimilarTitleReference
from app.abcvod.models import AbcvodTopic
from app.common.fields import AjaxSelectMultipleField
from cmf.core.form.form import CmsEmbeddedForm


class RatingForm(CmsEmbeddedForm):
    form_columns = ("comparison", "value")


def has_no_intersection_validator(other_field_name):
    def _different_than(form, field):
        other_field = form[other_field_name]
        our_value = set(field.data)
        other_field_value = set(other_field.data)
        if our_value & other_field_value:
            other_field_label = form[other_field_name].label.text
            raise ValidationError(f"{field.label.text} must not include values from {other_field_label}.")

    return _different_than


class AddOrRemoveTopicsForm(FlaskForm):
    topics_to_add = AjaxSelectMultipleField(label="Topics to add", loader=AbcvodTopic.get_ajax_loader())
    topics_to_remove = AjaxSelectMultipleField(
        label="Topics to remove",
        loader=AbcvodTopic.get_ajax_loader(),
        validators=[has_no_intersection_validator("topics_to_add")],
    )


class SimilarTitlesForm(CmsEmbeddedForm):
    model = AbcvodSimilarTitleReference  # for quick reference

    form_args = {
        "slug": {
            "render_kw": {"readonly": True},
            "widget": TextInput(),
            "description": "Read-only system field.",
        },
    }
