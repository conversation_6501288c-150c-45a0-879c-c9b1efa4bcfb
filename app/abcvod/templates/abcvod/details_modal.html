{% extends 'cms/tasks/details_modal.html' %}
{% from 'cms/tasks/details_modal.html' import render_block with context %}
{% from 'cmf/utils.html' import modal %}

{% block modal_body %}
    {{ super() }}
    <br>
    {% if model.titles_with_error %} {# FIXME: this block is deprecated and must be deleted along with 'titles_with_error' field. #}
        <table class="table table-striped">
            <caption class="h4">
                {{ model._fields["titles_with_error"].verbose_name }}: {{ model.titles_with_error|length }}
            </caption>
            <thead>
                <tr>
                    <th scope="col" style="width: 20%">Title ID</th>
                    <th scope="col" >Error message</th>
                </tr>
            </thead>
            <tbody>
                {% for title_with_error in model.titles_with_error %}
                    <tr>
                        <td class="w-auto">{{ title_with_error.title_id }}</td>
                        <td>{{ title_with_error.error_message }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% endif %}

    {% if model.import_errors %}
        <table class="table table-striped">
            <caption class="h4">
                {{ model._fields["import_errors"].verbose_name }}: {{ model.import_errors|length }}
            </caption>
            <thead>
                <tr>
                    <th scope="col" style="width: 20%">Error type</th>
                    <th scope="col">Error message</th>
                    <th scope="col">{# details btn, if we have details. #}</th>
                </tr>
            </thead>
            <tbody>
                {% for content_import_error in model.import_errors %}
                    {% set modal_id = model.id|string + "_details_" + loop.index|string %}
                    <tr>
                        <td class="w-auto">{{ content_import_error.error_type }}</td>
                        <td>{{ content_import_error.error_message }}</td>
                        {% if content_import_error.exception_details %}
                            <td>
                                <span class="btn btn-primary btn-xs"
                                      data-toggle="collapse"
                                      data-target="#{{ modal_id }}"
                                >
                                    <i class="glyphicon glyphicon-eye-open"></i>
                                </span>
                            </td>
                        {% else %}
                            <td>{# Yep, nothing. #}</td>
                        {% endif %}
                    </tr>
                    {% if content_import_error.exception_details %}
                        <tr class="collapse" id="{{ modal_id }}">
                            <td colspan="3">
                                <pre class="exception-details">{{ content_import_error.exception_details|safe }}</pre>
                            </td>
                        </tr>
                    {% endif %}
                {% endfor %}
            </tbody>
        </table>
    {% endif %}


{% endblock %}