{% extends 'cmf/widgets/table_list_widget/table_list_widget.html' %}

{% block thead %}
    <td>{# For 'edit' button #}</td>
    {% if not widget_is_locked %}
        <td>{# Empty column for delete buttons. #}</td>
    {% endif %}

    {# Verbose names for fields in embedded documents. #}
    {% for column_caption in column_captions %}
        <td>{{ column_caption }}</td>
    {% endfor %}
{% endblock %}


{% block custom_class %}js-import-table import-table{% endblock %}

{% block buttons %}
    <a id="{{ field.id }}-button"
       href="javascript:void(0)"
       class="btn btn-default js-add-item-btn"
    >
        {{ _gettext('Add') }} {{ field.label.text }}
    </a>
{% endblock %}