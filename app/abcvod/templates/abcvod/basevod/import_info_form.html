{% import 'admin/lib.html' as lib with context %}

{% call lib.form_tag(form) %}
    <div class="col-md-12">
        {{ lib.render_form_fields(form, form_opts=form_opts) }}
    </div>
    
    <div class="col-md-12">
        {# Main buttons. #}
        {% if not model.is_published %}
            <span class="btn btn-primary js-form-button"
                  data-url="{{ url_for(".save", id=model.id) }}"
                  data-autotest-id="save-btn"
            >
                {{ _gettext("Save") }}
            </span>
            <span class="btn btn-default js-form-button"
                  data-url="{{ url_for(".validate", id=model.id) }}"
                  data-autotest-id="save-and-validate-btn"
            >
                {{ _gettext("Save and Validate") }}
            </span>
            <span class="btn btn-primary js-form-button"
                  data-url="{{ url_for(".save_and_publish", id=model.id) }}"
                  data-confirm="Publish changes?"
                  data-autotest-id="save-and-publish-btn"
            >
                Save & Publish
            </span>
        {% endif %}
        <a class="btn btn-danger" href="{{ url_for(".index_view") }}">{{ _gettext("Cancel") }}</a>
        {# Action buttons. #}
        <div style="float: right;">
            <span class="btn btn-warning js-unpublish-button"
                  data-url="{{ url_for(".unpublish", id=model.id) }}"
                  data-confirm="Search & delete all titles, created by this import? This action cannot be undone."
                  data-autotest-id="unpublish-btn"
            >
                Unpublish
            </span>

            <a class="btn btn-default" href="{{ url_for(".export_csv", id=model.id) }}" data-autotest-id="export-btn">
                <i class="glyphicon glyphicon-export"></i> Export to CSV
            </a>
            {% if not model.is_published %}
                <span class="btn btn-default js-upload-file-button"
                      data-url="{{ url_for(".import_csv", id=model.id) }}"
                      data-accept=".csv"
                      data-confirm="Imported content will replace current, this action cannot be undone. Continue?"
                      data-autotest-id="import-btn"
                >
                    <i class="glyphicon glyphicon-import"></i> Import from CSV
                </span>
            {% endif %}
        </div>
    </div>
{% endcall %}
