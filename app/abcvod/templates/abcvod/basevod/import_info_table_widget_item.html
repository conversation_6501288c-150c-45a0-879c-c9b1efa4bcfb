{% from 'cmf/widgets/table_list_widget/table_list_widget_item.html' import render_delete_td with context %}

{% macro render_field_tr(field) -%}
    <tr>
        <td>{{ field.label }} {% if field.flags.required %}<strong style="color: red">*</strong>{% endif %}</td>
        <td>{{ field }}
            {% if field.description %}
                <p class="text-muted ellipsis js-ellipsis">{{ field.description }}</p>
            {% endif %}
            {% for error in field.errors %}
                <p class="text-danger">{{ error }}</p>
            {% endfor %}
        </td>
    </tr>
{% endmacro %}

{% macro render_required_fields() -%}
    <table class="table table-bordered">
        {% for field in item.form if (field.flags.required or field.type == 'BooleanField') -%}
            {{ render_field_tr(field) }}
        {%- endfor %}
    </table>
{% endmacro %}

{% macro render_other_fields() -%}
    <table class="table table-bordered">
        {% for field in item.form if not (field.flags.required or field.type == 'BooleanField') -%}
            {{ render_field_tr(field) }}
        {%- endfor %}
    </table>
{% endmacro %}

{% macro render_modal() %}
    <div class="js-modal modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Edit line</h4>
                </div>
                <div class="modal-body row">
                    <div class="col-md-6">
                        {{ render_required_fields() }}
                    </div>
                    <div class="col-md-6">
                        {{ render_other_fields() }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">Save</button>
                    <button type="button" class="btn btn-primary js-save-and-add" data-dismiss="modal">Save & Add another</button>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}

{% macro render_field_data(field) -%}
    {% if field.data is none -%}
        ---
    {% elif field.timezone -%}
        {{ field.data|localize_date }}
    {% else -%}
        {{ field.data }}
    {% endif %}
{%- endmacro %}


{% macro render_edit_td() %}
    <td>
        {{ render_modal() }}
        <span class="btn btn-sm btn-primary js-edit-line-btn" title="Edit">
            <i class="glyphicon glyphicon-edit"></i>
        </span>
    </td>
{% endmacro %}

<tr class="inline-field js-highlight-invalid list-widget-item js-item {% if item.errors %}bg-danger{% endif %}" id="{{ item.id }}">
    {% block content %}
        {% if not widget_is_locked %}
            {{ render_delete_td() }}
        {% endif %}
        {{ render_edit_td() }}

        {% for field in item %}
            <td data-field-name="{{ field.name }}">{{ render_field_data(field) }}</td>
        {% endfor %}
    {% endblock %}
</tr>
