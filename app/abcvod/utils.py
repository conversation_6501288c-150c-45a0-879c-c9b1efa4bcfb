from __future__ import annotations

from functools import lru_cache
from typing import Type

from app.abcvod.core.models.abcvod import AbcvodTitle
from app.utils import all_subclasses


@lru_cache
def get_available_abcvod_providers() -> dict[str, str]:
    """Get all available ABCVOD providers.

    :return: {"db_alias": "Provider Verbose Name"}
    """
    from flask import current_app as app

    result = [("basevod", "Base VOD")]  # db alias, vod verbose name
    config_map = {
        "PREMIER": ("premier", "Premier"),
        "PREMIER_SHOWCASE": ("premier_showcase", "Premier"),
        "AMEDIA2": ("amediateka2", "Amediateka"),
        "VIPPLAY": ("vipplay", "VipPlay"),
        "START2": ("start2", "Start"),
        "WINK": ("wink", "Wink"),
        "ETNOMEDIA": ("etnomedia", "Etnomedia"),
    }
    for config_key, name in config_map.items():
        if app.config.get(config_key):
            result.append(name)

    for extra_basevod_config in app.config.get("BASEVOD_EXTRA_INSTANCES") or []:
        result.append((extra_basevod_config["db_alias"], extra_basevod_config["menu_caption"]))

    return dict(result)


@lru_cache
def get_title_model_by_db_alias(db_alias: str) -> Type[AbcvodTitle] | None:
    for cls in all_subclasses(AbcvodTitle):
        if cls._meta.get("db_alias") == db_alias:
            return cls


def get_vod_provider_name(doc: AbcvodTitle) -> str:
    providers_info = get_available_abcvod_providers()
    return providers_info[doc.db_alias]
