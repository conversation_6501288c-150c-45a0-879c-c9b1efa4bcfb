{% import 'admin/lib.html' as lib with context %}

{% extends 'admin/model/cms_edit.html' %}

{% block head_css %}
    {{ super () }}
{% endblock %}

{% block body %}
    {{ super() }}
    <div class="modal fade" id="modal-img" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content">
                <div class="modal-body no-padding"><img /></div>
            </div>
        </div>
    </div>
{% endblock %}

{% block form %}
    <form action="" method="POST" role="form" class="admin-form" enctype="multipart/form-data">
        <div class="col-md-9">
            <div class="box box-primary collapsed-box">
                <div data-widget="collapse" style="cursor: pointer">
                    <div class="box-header">
                        <h3 class="box-title">Info</h3>
                    </div>
                </div>
                <div class="box-body">
                    <dl class="dl-horizontal">
                        {% set description = model.description or {} %}
                        {% set series_name = model.series_name or {} %}
                        {% set episode_name = model.episode_name or {} %}

                        <dt>Title</dt><dd>{{ model.title }}</dd>
                        <dt>Description</dt><dd>{{ description.get('ru', description.get('default')) or '' }}</dd>
                        <dt>Channel ID</dt><dd>{{ model.local_channel_id }}</dd>
                        <dt>Series</dt><dd>{{ series_name.get('ru', series_name.get('default')) or '' }}</dd>
                        <dt>Episode</dt><dd>{{ episode_name.get('ru', episode_name.get('default')) or '' }}</dd>
                        <dt>Start Date</dt><dd>{{ model.start or ''}}</dd>
                        <dt>End Date</dt><dd>{{ model.end or ''}}</dd>
                        <dt>Edited Start Date</dt><dd>{{ model.start_edited or ''}}</dd>
                        <dt>Edited End Date</dt><dd>{{ model.end_edited or '' }}</dd>
                    </dd>
                </div>
            </div>
            <div class="box box-primary" style="margin-bottom: 0;">
                <div class="box-header">
                    <div class="btn-group btn-group-toggle" data-toggle="buttons" style="position: absolute; right: 50%">
                        <label id="start-input" class="btn btn-success active">
                            <input name="start" type="radio" value={{ start }} checked>Start</input>
                        </label>
                        <label id="end-input" class="btn btn-default">
                            <input name="end" type="radio" value={{ end }} checked>End</input>
                        </label>
                    </div>
                    <input type="submit" class="btn btn-primary" style="position: absolute; right: 40%" value="Save"/>
                    <div style="position: absolute; width: 50px; right: 150px">
                        <input id="scope-input" type="number" min=2 max=9 class="form-control"></input>
                    </div>
                    <div class="input-group-btn" style="position: absolute; right: 135px">
                        <a id="zoom-in" type="button" class="disabled btn btn-default btn-flat"><i class="fa fa-search-plus"></i></a>
                        <a id="zoom-input" class='btn btn-flat btn-default disabled'>1x</a>
                        <a id="zoom-out" type="button" class="btn btn-default"><i class="fa fa-search-minus"></i></a>
                    </div>
                    <h3 class="box-title">Timeline Editor</h3>
                </div>
                <div class="box-body">
                    <div id="timeline-editor"></div>
                </div>
            </div>
        </div>
    </form>
{% endblock %}


{% block tail_js %}
    {{ super() }}

    <script type="text/javascript">
        var TimelineEditor = App['timeline_editor'].default;

        var data = {
            thumbnails_url: '{{ thumbnails_url }}',
            loader: '{{ url_for('static', filename='img/loading.gif') }}',
            start: {{ start }},
            end: {{ end }}
        };

        var buttons = {
            zoom_in: $('#zoom-in'),
            zoom_out: $('#zoom-out'),
            zoom_input: $('#zoom-input'),
            scope_input: $('#scope-input'),
            start_input: $('#start-input'),
            end_input: $('#end-input')
        };

        TimelineEditor($('#timeline-editor').get(0), data, buttons);
    </script>
{% endblock %}
