{% import 'admin/lib.html' as lib with context %}

{% extends 'admin/model/cms_edit.html' %}

{% block head_css %}
    {{ super () }}
{% endblock %}

{% block body %}
    {{ super() }}
    <div class="modal fade" id="modal-img" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content">
                <div class="modal-body no-padding"><img /></div>
            </div>
        </div>
    </div>
{% endblock %}

{% block form %}
    <form action="" method="POST" role="form" class="admin-form" enctype="multipart/form-data">
        <div class="col-md-9">
            <div class="box box-primary">
                <div class="box-header">
                    <h3 class="box-title">Info</h3>
                </div>
                <div class="box-body">
                    <dl class="dl-horizontal">
                        <dt>Title</dt><dd>{{ model.title }}</dd>
                        <dt>Description</dt><dd>{{ model.description.get('ru', model.description.get('default')) or '' }}</dd>
                        <dt>Channel ID</dt><dd>{{ model.local_channel_id }}</dd>
                        <dt>Series</dt><dd>{{ model.series_name.get('ru', model.series_name.get('default')) or '' }}</dd>
                        <dt>Episode</dt><dd>{{ model.episode_name.get('ru', model.episode_name.get('default')) or '' }}</dd>
                        <dt>Start Date</dt><dd>{{ model.start }}</dd>
                        <dt>End Date</dt><dd>{{ model.end }}</dd>
                    </dd>
                </div>
            </div>
            <div class="box box-primary">
                <div class="box-header">
                    <div class="input-group-btn" style="position: absolute; right: 75px">
                        <a id="zoom-in" type="button" class="disabled btn btn-default btn-flat"><i class="fa fa-search-plus"></i></a>
                        <a id="zoom-out" type="button" class="btn btn-default"><i class="fa fa-search-minus"></i></a>
                    </div>
                    <h3 class="box-title">Timeline Editor</h3>
                </div>
                <div class="box-body">
                    <div id="timeline"></div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-md-6">
                        <div class="box-body">
                            <div class="form-group">
                                <label for="start" class="control-label">Start</label>
                                <div class="input-group">
                                    <input class="form-control" data-drops="up" data-date-format="YYYY-MM-DD HH:mm:ss" data-role="datetimepicker"
                                           id="start" name="start" type="text" value="{{ start|replace('T', ' ') }}">
                                    <span class="input-group-btn">
                                        <button id="start-current" type="button" class="go btn btn-default btn-flat">Current</button>
                                        <button id="start-initial" type="button" class="go btn btn-default">Initial</button>
                                    </span>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="box-body">
                            <div class="form-group">
                                <label for="end" class="control-label">End</label>
                                <div class="input-group">
                                    <input class="form-control" data-drops="up" data-date-format="YYYY-MM-DD HH:mm:ss" data-role="datetimepicker"
                                           id="end" name="end" type="text" value="{{ end|replace('T', ' ') }}">
                                    <span class="input-group-btn">
                                        <button id="end-current" type="button" class="go btn btn-default btn-flat">Current</button>
                                        <button id="end-initial" type="button" class="go btn btn-default">Initial</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="box-body">
                            <input type="submit" class="btn btn-primary" value="Save">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
{% endblock %}


{% block tail_js %}
    {{ super() }}
    <script>
            var isDragging = false;

            var to_time = function(data) {
                return new Date(data).toTimeString().substr(0, 8)
            }

            $start = $('input#start');
            $end = $('input#end');

            var container = document.getElementById('timeline');
            var timeline_data = {{ timeline|tojson }};
            var start = '{{ start }}';
            var end = '{{ end }}';

            var data = new vis.DataSet([
                {id: 'start-initial', content: '', start: start, type: 'point', editable: {remove: false, updateTime: false}, class: 'vis-start-mark', initial: true},
                {id: 'end-initial', content: '', start: end, type: 'point', editable: {remove: false, updateTime: false}, class: 'vis-end-mark', initial: true},
                {id: 'start-current', content: 'Start', start: start, type: 'point', editable: {remove: false, updateTime: true}, class: 'vis-start-mark'},
                {id: 'end-current', content: 'End', start: end, type: 'point', editable: {remove: false, updateTime: true}, class: 'vis-end-mark'},
            ].concat(timeline_data));

            var options = {
                align: 'left',
                height: '250px',
                zoomable: false,
                zoomMin: 1000 * 30,
                zoomMax: 1000 * 30,
                template: function (item, element, data) {
                    var html = '<img id="' + item.id + '" src="{{ url_for('static', filename='img/loading.gif')}}">';

                    if (item.type == 'point') {
                        if (item.initial) {
                            html = '<div class="' + item.class + ' initial"></div>';
                        } else {
                            html = '<div class="' + item.class + '">' +
                                   '    <div>' +
                                   '        <span>' + item.content + '</span>' +
                                   '        <span class="time">' + to_time(item.start) + '</span>' +
                                   '    </div>' +
                                   '</div>';

                            setTimeout(function() {
                                $('.' + item.class + ' .time').html(to_time(data.start));
                            }, 1);
                        }
                    };

                    return html;
                },
                onMove: function(item) {
                    update_time(item.id.split('-')[0], new Date(item.start));
                }
            };

            var timeline = new vis.Timeline(container, data, options);
            timeline.focus('start-current');

            $('.vis-item .initial').hide();

            var z_level = 1;
            var max_z_level = 1024;

            var zoomed = [];
            for (var l = z_level; l <= max_z_level; l *= 2) {
                zoomed[l] = timeline_data.filter(function(_, i) {return i % l == 0});
            }

            var update = function() {
                $('.vis-item img').hide();
                var focus = timeline.itemSet.groups.__background__.visibleItems.sort(function(a, b) { return a.id - b.id});

                if (focus.length) {
                    var start_id = focus[0].id;
                    var end_id = focus[focus.length - 1].id;

                    var start_shift = start_id - 5 * z_level;
                    var end_shift = end_id + 5 * z_level;

                    $.each(zoomed[z_level], function(_, img) {
                        if (img.id > start_shift && img.id < end_shift) {
                            $('.vis-item img#' + img.id).attr('src', timeline.itemsData._data[img.id].content)
                                .css('width', timeline.body.dom.background.clientWidth / 5 + 1 + 'px' ).show();
                        };
                    });

                    setTimeout(function() {
                        var img_height = $('.vis-item img').height();
                        img_height && timeline.setOptions({ height: img_height + 100 + 'px' });
                    }, 1000);
                };
            }

            timeline.on('rangechange', function(start, end, byUser, event) {
                $('.vis-item img').css('cursor', 'move');
                isDragging = true;
                update();
            });

            timeline.on('rangechanged', function(start, end, byUser, event) {
                $('.vis-item img').css('cursor', 'pointer');
            });

            var update_time = function(point, time, input) {
                    $('input#' + point).data('daterangepicker').setEndDate(time);

                    data.update({id: point + '-initial', class: 'vis-' + point + '-mark shown'});
                    data.update({id: point + '-current', start: time});
            };

            timeline.on('contextmenu', function (props) {
                if (!props.item) {
                    props.event.preventDefault();

                    var start = props.time < new Date(data._data['end-current'].start);
                    var end = props.time > new Date(data._data['start-current'].start);

                    $('.context-menu').remove();
                    $('.vis-panel.vis-top').append(
                    '<div class="context-menu btn-group"' +
                         'style="left:' + (props.x - 50) + 'px; top:' + props.y + 'px">' +
                        (start ? '<button class="btn btn-default btn-sm start">Start</a>' : '') +
                        (end ? '<button class="btn btn-default btn-sm end">End</a>' : '') +
                    '<div>');

                    $('.context-menu .start').click(function() {
                        update_time('start', props.time);
                    });

                    $('.context-menu .end').click(function() {
                        update_time('end', props.time);
                    });
                }
            });

            timeline.on('rangechange', function() { $('.context-menu').remove() });

            $('body').click(function() { $('.context-menu').remove() })

            $start.mouseup(function() {
                timeline.focus('start-current');
            });

            $end.mouseup(function() {
                timeline.focus('end-current');
            });

            $('button.go').each(function(_, btn) {
                var id = btn.id;

                $(btn).click(function() {
                    timeline.focus(id);
                })
            });

            $start.blur(function(event) {
                update_time('start', event.target.value);
            });

            $end.blur(function(event) {
                update_time('end', event.target.value);
            });

            $('#zoom-in').click(function() {
                $('#zoom-in').addClass('disabled').attr('disabled', true);

                setTimeout(function() {
                    z_level != 1 && $('#zoom-in').removeClass('disabled').removeAttr('disabled');
                }, 500);

                if (z_level > 1) {
                    z_level /= 2;

                    $('#zoom-out').removeClass('disabled');

                    timeline.zoomIn(1);
                    update();
                }
            });

            $('#zoom-out').click(function() {
                $('#zoom-out').addClass('disabled').attr('disabled', true);

                setTimeout(function() {
                    z_level != max_z_level && $('#zoom-out').removeClass('disabled').removeAttr('disabled');
                }, 500);

                if (z_level < max_z_level) {
                    z_level *= 2;

                    $('#zoom-in').removeClass('disabled');

                    timeline.zoomOut(1);
                    update();
                }
            });

            timeline.setOptions({
                zoomMax: 1000 * 30 * 1000
            });

            setTimeout(function() {
                timeline.focus('start-current');

                setTimeout(function() {
                    update();
                }, 1);
            }, 100);

            $(window, timeline.body.dom.content).resize(function() {
                update();
            });

            timeline.on('click', function(event) {
                var el = event.event.target;

                if (el.tagName === 'IMG' && !isDragging) {
                   $('#modal-img img').attr('src', el.src);
                   $('#modal-img').modal('show');
                };

                isDragging = false;
            });
        </script>
{% endblock %}
