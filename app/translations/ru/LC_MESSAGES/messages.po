#: app/abcvod/providers/basevod/validators.py:13
msgid "Must contain placeholder {number} and only lowercase latin letters, numbers, dashes or underscores"
msgstr "Должен содержать заполнитель {number} и только строчные латинские буквы, цифры, тире или подчеркивания"

#: app/accounts/components/account.py:146
msgid "Verified phone number cannot be changed"
msgstr "Изменить подтверждённый номер телефона нельзя"

#: app/accounts/components/account.py:155
msgid "Phone number has been saved"
msgstr "Номер телефона сохранён"

#: app/accounts/components/account.py:157
#: app/accounts/components/account.py:161
msgid "Error saving phone number"
msgstr "Ошибка сохранения номера телефона"

#: app/accounts/components/account.py:190
#: app/accounts/components/account.py:207
msgid "Error setting phone number"
msgstr "Ошибка установки номера"

#: app/accounts/components/account.py:197
msgid "Phone number has been set"
msgstr "Номер телефона установлен"

#: app/accounts/components/account.py:227
msgid "Phone number status has been changed to 'verified'"
msgstr "Статус номер телефона изменён на 'подтверждённый'"

#: app/accounts/components/account.py:229
msgid "Phone number status has been changed to 'not verified'"
msgstr "Статус номер телефона изменён на 'неподтверждённый'"

#: app/accounts/components/account.py:232
msgid "Error changing phone number status"
msgstr "Ошибка изменения статуса номера телефона"

#: app/accounts/components/account.py:234
#: app/accounts/components/account.py:258
msgid "Phone number not specified"
msgstr "Номера телефона не задан"

#: app/accounts/components/account.py:248
msgid "Failed to delete number"
msgstr "Не удалось удалить номер"

#: app/accounts/components/account.py:249
msgid "make sure that login does not match phone number"
msgstr "проверьте, что логин не совпадает с номером телефона"

#: app/accounts/components/account.py:254
msgid "The phone number has been unlinked and deleted"
msgstr "Номер телефона отвязан и удалён"

#: app/accounts/components/account.py:256
msgid "Error deleting phone number"
msgstr "Ошибка удаления номера телефона"

#: app/accounts/components/account.py:283
msgid "Account contact information saved"
msgstr "Контактная информация аккаунта сохранена"

#: app/accounts/components/account.py:287
#: app/accounts/components/account.py:294
msgid "Error saving account contact information"
msgstr "Ошибка сохранения контактной информации аккаунта"

#: app/accounts/components/account.py:350
msgid "Password reset successfully"
msgstr "Пароль успешно сброшен"

#: app/accounts/components/account.py:352
msgid "Password reset error"
msgstr "Ошибка сброса пароля"

#: app/accounts/components/account.py:391
msgid "The account has active purchases"
msgstr "Аккаунт имеет активные покупки"

#: app/accounts/components/account.py:429
msgid "The account has been successfully deactivated"
msgstr "Аккаунт успешно деактивирован"

#: app/accounts/components/account.py:433
#: app/accounts/components/account.py:438
msgid "Account deactivation error"
msgstr "Ошибка деактивации аккаунта"

#: app/accounts/components/accounts_list.py:32
#: app/accounts/components/forms.py:312
#: app/accounts/templates/accounts/upsale_request_details.html:45
msgid "Login"
msgstr "Логин"

#: app/accounts/components/accounts_list.py:33
msgid "E-mail"
msgstr "E-mail"

#: app/accounts/components/accounts_list.py:34
#: app/accounts/templates/accounts/account_yakassa.html:76
msgid "Subscriptions"
msgstr "Подписки"

#: app/accounts/components/accounts_list.py:35
#: app/accounts/components/forms.py:175
#: app/accounts/templates/accounts/account_region.html:3
msgid "Region"
msgstr "Регион"

#: app/accounts/components/accounts_list.py:36
#: app/accounts/components/forms.py:309
#: app/accounts/templates/accounts/account_info.html:32
#: app/accounts/templates/accounts/account_upsale_requests.html:30
#: app/accounts/templates/accounts/account_upsale_requests.html:54
#: app/accounts/templates/accounts/upsale_request_details.html:31
msgid "Provider"
msgstr "Провайдер"

#: app/accounts/components/devices.py:75
msgid "The device has been saved"
msgstr "Устройство сохранено"

#: app/accounts/components/devices.py:78 app/accounts/components/devices.py:81
msgid "Error saving device"
msgstr "Ошибка сохранения устройства"

#: app/accounts/components/devices.py:100
msgid "The device has been removed"
msgstr "Устройство удалено"

#: app/accounts/components/devices.py:102
msgid "Error deleting device"
msgstr "Ошибка удаления устройства"

#: app/accounts/components/extra.py:66
msgid "Provider information saved"
msgstr "Информация провайдера сохранена"

#: app/accounts/components/extra.py:68
msgid "Error saving provider information"
msgstr "Ошибка сохранения информации провайдера"

#: app/accounts/components/forms.py:38 app/accounts/components/forms.py:294
msgid "Incorrect email format"
msgstr "Неправильный формат электронной почты"

#: app/accounts/components/forms.py:39
msgid "Email address"
msgstr "Адрес электронной почты"

#: app/accounts/components/forms.py:64 app/accounts/components/forms.py:107
#: app/accounts/templates/accounts/account_promo_resources.html:22
#: app/accounts/templates/accounts/account_purchases.html:38
#: app/accounts/templates/accounts/account_upsale_requests.html:47
#: app/accounts/templates/accounts/account_yakassa.html:107
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:76
#: app/accounts/templates/accounts/subscription_details.html:120
#: app/accounts/templates/accounts/subscriptions_history.html:40
#: app/accounts/templates/accounts/upsale_request_details.html:34
msgid "Offer"
msgstr "Оффер"

#: app/accounts/components/forms.py:72 app/accounts/components/forms.py:165
#: app/accounts/components/forms.py:218
#: app/accounts/templates/accounts/account_purchases.html:14
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:105
#: app/accounts/templates/accounts/subscription_details.html:89
#: app/accounts/templates/accounts/subscriptions_history.html:19
msgid "Expiration time"
msgstr "Время истечения"

#: app/accounts/components/forms.py:76 app/accounts/components/forms.py:113
#: app/accounts/components/forms.py:346
#: app/accounts/templates/accounts/account_delete.html:22
msgid "Ignore integrations"
msgstr "Игнорировать интеграции"

#: app/accounts/components/forms.py:83 app/accounts/components/forms.py:121
#: app/accounts/components/forms.py:351
#: app/accounts/templates/accounts/account_delete.html:28
msgid "Do not send notifications"
msgstr "Не отправлять оповещения"

#: app/accounts/components/forms.py:129
msgid "Force disable"
msgstr "Принудительное отключение"

#: app/accounts/components/forms.py:145
msgid "ID Type"
msgstr "Тип ID"

#: app/accounts/components/forms.py:150
#: app/accounts/templates/accounts/account_devices.html:12
#: app/accounts/templates/accounts/account_purchases.html:10
#: app/accounts/templates/accounts/account_yakassa.html:85
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:101
#: app/accounts/templates/accounts/subscription_details.html:142
#: app/accounts/templates/accounts/subscriptions_history.html:15
msgid "Name"
msgstr "Название"

#: app/accounts/components/forms.py:151
#: app/media_assets_manager/templates/media_assets_manager/assets.html:57
msgid "Description"
msgstr "Описание"

#: app/accounts/components/forms.py:186
#: app/accounts/templates/accounts/account_info.html:109
msgid "Phone number"
msgstr "Номер телефона"

#: app/accounts/components/forms.py:189 app/accounts/components/forms.py:360
msgid "Incorrect phone number"
msgstr "Некорректный номер"

#: app/accounts/components/forms.py:223
msgid "Skip integration"
msgstr "Пропустить интеграцию"

#: app/accounts/components/forms.py:230
msgid "Do not notify the user"
msgstr "Не уведомлять пользователя"

#: app/accounts/components/forms.py:288
msgid "Incorrect login format"
msgstr "Неправильный формат логина"

#: app/accounts/components/forms.py:292
msgid "Login required"
msgstr "Логин обязателен"

#: app/accounts/components/forms.py:296
msgid "Email required"
msgstr "Электронная почта обязательна"

#: app/accounts/components/forms.py:300
msgid "Phone number required"
msgstr "Номер телефона обязателен"

#: app/accounts/components/forms.py:317
#: app/accounts/templates/accounts/account_info.html:106
msgid "Email"
msgstr "Электронная почта"

#: app/accounts/components/forms.py:324
msgid "Passwords do not match"
msgstr "Пароли не совпадают"

#: app/accounts/components/forms.py:328
msgid "Password"
msgstr "Пароль"

#: app/accounts/components/forms.py:330
msgid "Confirm password"
msgstr "Подтверждение пароля"

#: app/accounts/components/forms.py:336
#: app/accounts/templates/accounts/account_info.html:19
#: app/accounts/templates/accounts/upsale_request_details.html:47
msgid "Phone"
msgstr "Телефон"

#: app/accounts/components/forms.py:341
msgid "Phone number confirmed"
msgstr "Номер телефона подтверждён"

#: app/accounts/components/promo.py:110
msgid "Promo-resource exhaustion is cancelled"
msgstr "Расход промо-ресурса отменён"

#: app/accounts/components/promo.py:112
msgid "Error cancelling promo-resource exhaustion"
msgstr "Ошибка отмены расхода"

#: app/accounts/components/promo.py:135
msgid "Promo-resource is exhausted"
msgstr "Промо-ресурс израсходован"

#: app/accounts/components/promo.py:137
msgid "Error exhausting promo-resource"
msgstr "Ошибка расхода промо-ресурса"

#: app/accounts/components/promo.py:164
msgid "Promo-resources provided"
msgstr "Промо-ресурсы предоставлены"

#: app/accounts/components/promo.py:166
msgid "Error providing promo-resource"
msgstr "Ошибка предоставления промо-ресурса"

#: app/accounts/components/region.py:65
msgid "Region has been saved"
msgstr "Регион сохранён"

#: app/accounts/components/region.py:67 app/accounts/components/region.py:71
msgid "Error saving region"
msgstr "Ошибка сохранения региона"

#: app/accounts/components/sessions.py:107
msgid "All sessions have been successfully deleted"
msgstr "Все сессии успешно удалены"

#: app/accounts/components/sessions.py:111
msgid "Session successfully deleted"
msgstr "Сессия успешно удалена"

#: app/accounts/components/sessions.py:113
msgid "Error deleting session"
msgstr "Ошибка удаления сессии"

#: app/accounts/components/sessions.py:123
msgid "Sessions successfully deleted"
msgstr "Сессии успешно удалены"

#: app/accounts/components/sessions.py:125
msgid "Error deleting sessions"
msgstr "Ошибка удаления сессий"

#: app/accounts/components/stores.py:114
msgid "Failed to update subscription status"
msgstr "Не удалось обновить состояние подписки"

#: app/accounts/components/stores.py:119
msgid "Subscription status updated"
msgstr "Состояние подписки обновлено"

#: app/accounts/components/subscriptions.py:152
#, python-format
msgid "Parameters of %(service)s subscription does not match the offer"
msgstr "Параметры %(service)s подписки не соответствуют офферу"

#: app/accounts/components/subscriptions.py:273
msgid "Purchase saved"
msgstr "Покупка сохранена"

#: app/accounts/components/subscriptions.py:275
#: app/accounts/components/subscriptions.py:343
#: app/accounts/components/subscriptions.py:552
msgid "Error saving purchase"
msgstr "Ошибка сохранения покупки"

#: app/accounts/components/subscriptions.py:341
msgid "Purchase cancelled successfully"
msgstr "Покупка успешно отменена"

#: app/accounts/components/subscriptions.py:348
msgid "Error cancelling the purchase"
msgstr "Ошибка отмены покупки"

#: app/accounts/components/subscriptions.py:374
msgid "Error trying to recreate VOD subscriptions"
msgstr "Ошибка при попытке повторного создания подписок VOD"

#: app/accounts/components/subscriptions.py:377
msgid "Request to recreate VOD subscriptions successfully sent"
msgstr "Запрос на повторное создание подписок VOD успешно отправлен"

#: app/accounts/components/subscriptions.py:410
msgid "Error occurred while trying to change expiration date"
msgstr "Ошибка при попытке изменения срока истечения"

#: app/accounts/components/subscriptions.py:416
msgid "Expiration date successfully changed"
msgstr "Срок истечения успешно изменён"

#: app/accounts/components/subscriptions.py:539
msgid "Error saving purchases for accounts"
msgstr "Ошибка сохранения покупок дла аккаунтов"

#: app/accounts/components/subscriptions.py:544
msgid "Purchases saved for accounts"
msgstr "Покупки сохранены для аккаунтов"

#: app/accounts/components/upsale.py:95 app/media_assets_manager/views.py:59
#: app/media_assets_manager/views.py:210
msgid "Service error"
msgstr "Ошибка сервиса"

#: app/accounts/components/upsale.py:106
msgid "Error sending request"
msgstr "Ошибка отправки запроса"

#: app/accounts/components/upsale.py:108
msgid "Retry request has been sent"
msgstr "Повторный запрос отправлен"

#: app/accounts/components/upsale.py:122
msgid "Error archiving request"
msgstr "Ошибка архивации запроса"

#: app/accounts/components/upsale.py:124
msgid "The request was successfully archived"
msgstr "Запрос успешно архивирован"

#: app/accounts/components/yakassa.py:96
msgid "Error deleting payment method"
msgstr "Ошибка удаления метода платежа"

#: app/accounts/components/yakassa.py:101
msgid "Payment method deleted successfully"
msgstr "Метода платежа удалён"

#: app/accounts/components/yakassa.py:203
msgid "Subscription termination error"
msgstr "Ошибка завершения подписки"

#: app/accounts/components/yakassa.py:208
msgid "Subscription terminated"
msgstr "Подписка завершена"

#: app/accounts/components/yakassa.py:220
msgid "Subscription refund error"
msgstr "Ошибка возврата средств за подписку"

#: app/accounts/components/yakassa.py:222
msgid "The refund was successful"
msgstr "Возврат средств прошел успешно"

#: app/accounts/components/yakassa.py:237
msgid "Error cancelling subscription"
msgstr "Ошибка отключения подписки"

#: app/accounts/components/yakassa.py:244
msgid "Subscription cancelled"
msgstr "Подписка отключена"

#: app/accounts/components/yakassa.py:263
msgid "Subscription activation error"
msgstr "Ошибка активации подписки"

#: app/accounts/components/yakassa.py:268
msgid "Subscription activated"
msgstr "Подписка активирована"

#: app/accounts/components/yakassa.py:312
msgid "Payment synchronization error"
msgstr "Ошибка синхронизации платежа"

#: app/accounts/components/yakassa.py:321
msgid "Payment synchronized"
msgstr "Платёж синхронизирован"

#: app/accounts/components/yakassa.py:347
msgid "Subscription archiving error"
msgstr "Ошибка архивации подписки"

#: app/accounts/components/yakassa.py:352
msgid "Subscription archived"
msgstr "Подписка архивирована"

#: app/accounts/templates/accounts/account_authhistory.html:8
#: app/accounts/templates/accounts/account_sessions.html:117
msgid "Session history"
msgstr "История сессий"

#: app/accounts/templates/accounts/account_authhistory.html:12
msgid "Unable to retrieve saved session information"
msgstr "Невозможно получить сведения о сохранённых сессиях"

#: app/accounts/templates/accounts/account_authhistory.html:17
#: app/accounts/templates/accounts/account_purchases.html:46
#: app/accounts/templates/accounts/subscription_details.html:57
#: app/accounts/templates/accounts/subscriptions_history.html:48
msgid "Active"
msgstr "Активна"

#: app/accounts/templates/accounts/account_authhistory.html:18
#: app/accounts/templates/accounts/account_sessions.html:58
#: app/accounts/templates/accounts/account_yakassa.html:87
#: app/accounts/templates/accounts/blocks/refund_history_table.html:61
#: app/accounts/templates/accounts/yakassa_payment_details.html:17
#: app/accounts/templates/accounts/yakassa_subscription_details.html:29
#: app/accounts/templates/accounts/yakassa_subscription_details.html:63
#: app/accounts/templates/accounts/yakassa_subscription_details.html:92
msgid "Creation date"
msgstr "Дата создания"

#: app/accounts/templates/accounts/account_authhistory.html:19
#: app/accounts/templates/accounts/account_sessions.html:60
#: app/accounts/templates/accounts/upsale_request_details.html:53
#: app/accounts/templates/accounts/yakassa_subscription_details.html:41
msgid "Device"
msgstr "Устройство"

#: app/accounts/templates/accounts/account_authhistory.html:20
#: app/accounts/templates/accounts/account_sessions.html:61
#: app/accounts/templates/accounts/upsale_request_details.html:63
#: app/accounts/templates/accounts/yakassa_subscription_details.html:38
msgid "Application"
msgstr "Приложение"

#: app/accounts/templates/accounts/account_authhistory.html:22
msgid "Authentication method"
msgstr "Метод аутентификации"

#: app/accounts/templates/accounts/account_authhistory.html:23
#: app/accounts/templates/accounts/account_sessions.html:62
msgid "IP address"
msgstr "IP адрес"

#: app/accounts/templates/accounts/account_authhistory.html:24
msgid "Credentials"
msgstr "Учётные данные"

#: app/accounts/templates/accounts/account_authhistory.html:33
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:79
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:80
msgid "yes"
msgstr "да"

#: app/accounts/templates/accounts/account_authhistory.html:35
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:79
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:80
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:112
msgid "no"
msgstr "нет"

#: app/accounts/templates/accounts/account_authhistory.html:64
msgid "There are no saved user sessions"
msgstr "Нет сохранённых сессий пользователя"

#: app/accounts/templates/accounts/account_authhistory.html:68
#: app/accounts/templates/accounts/account_yakassa.html:189
#: app/accounts/templates/accounts/batch_subscriptions.html:45
#: app/accounts/templates/accounts/blocks/refund_history_table.html:80
#: app/accounts/templates/accounts/components/stores/account_stores_event.html:19
#: app/accounts/templates/accounts/components/stores/subscription/subscription_page.html:29
#: app/accounts/templates/accounts/show_pin.html:46
#: app/accounts/templates/accounts/subscription_details.html:156
#: app/accounts/templates/accounts/subscription_disable.html:17
#: app/accounts/templates/accounts/subscriptions_history.html:75
#: app/accounts/templates/accounts/upsale_request_details.html:159
#: app/accounts/templates/accounts/yakassa_payment_details.html:23
#: app/accounts/templates/accounts/yakassa_payment_method.html:13
#: app/accounts/templates/accounts/yakassa_subscription_details.html:136
msgid "Back"
msgstr "Назад"

#: app/accounts/templates/accounts/account_blocking_history.html:7
msgid "Blocking history"
msgstr "История блокировки"

#: app/accounts/templates/accounts/account_delete.html:8
msgid "Account unlinking"
msgstr "Отвязывание аккаунта"

#: app/accounts/templates/accounts/account_delete.html:8
msgid "Account deletion"
msgstr "Удаление аккаунта"

#: app/accounts/templates/accounts/account_delete.html:13
#, python-format
msgid "Are you sure you want to unlink %(login)s and all its subscriptions?"
msgstr "Вы уверены, что хотите отвязать аккаунт %(login)s и все его подписки?"

#: app/accounts/templates/accounts/account_delete.html:15
#, python-format
msgid "Are you sure you want to delete %(login)s account and all its subscriptions?"
msgstr "Вы уверены, что хотите удалить аккаунт %(login)s и все его подписки?"

#: app/accounts/templates/accounts/account_delete.html:33
msgid "Unlink"
msgstr "Отвязать"

#: app/accounts/templates/accounts/account_delete.html:33
#: app/accounts/templates/accounts/account_devices.html:42
#: app/accounts/templates/accounts/account_sessions.html:100
#: app/media_assets_manager/templates/media_assets_manager/assets.html:84
#: app/media_assets_manager/templates/media_assets_manager/utils.html:4
msgid "Delete"
msgstr "Удалить"

#: app/accounts/templates/accounts/account_delete.html:35
#: app/accounts/templates/accounts/account_phone.html:32
#: app/accounts/templates/accounts/account_set_phone.html:17
#: app/accounts/templates/accounts/contacts.html:17
#: app/accounts/templates/accounts/device_manage.html:19
#: app/accounts/templates/accounts/provider_info.html:17
#: app/accounts/templates/accounts/region_manage.html:28
#: app/accounts/templates/accounts/reset_password.html:22
#: app/accounts/templates/accounts/subscription_manage.html:23
#: app/accounts/templates/accounts/subscription_set_expiration_time.html:17
msgid "Cancel"
msgstr "Отмена"

#: app/accounts/templates/accounts/account_devices.html:3
msgid "Devices"
msgstr "Устройства"

#: app/accounts/templates/accounts/account_devices.html:13
#: app/accounts/templates/accounts/account_purchases.html:13
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:104
#: app/accounts/templates/accounts/subscription_details.html:86
#: app/accounts/templates/accounts/subscriptions_history.html:18
msgid "Created at"
msgstr "Время создания"

#: app/accounts/templates/accounts/account_devices.html:14
msgctxt "devices table"
msgid "Updated at"
msgstr "Время обновления"

#: app/accounts/templates/accounts/account_devices.html:39
msgid "deleted"
msgstr "удалено"

#: app/accounts/templates/accounts/account_devices.html:51
msgid "There are no devices at the moment"
msgstr "Устройств на данный момент нет"

#: app/accounts/templates/accounts/account_devices.html:56
msgid "Add device"
msgstr "Добавить устройство"

#: app/accounts/templates/accounts/account_extra.html:3
msgid "Extra info"
msgstr "Доп. инфо"

#: app/accounts/templates/accounts/account_extra.html:8
#: app/accounts/templates/accounts/account_region.html:6
#: app/accounts/templates/accounts/device_manage.html:8
#: app/comments/templates/comments/widget.html:22
#: app/media_assets_manager/templates/media_assets_manager/assets.html:72
msgid "Edit"
msgstr "Редактировать"

#: app/accounts/templates/accounts/account_extra.html:30
msgid "No information"
msgstr "Информации нет"

#: app/accounts/templates/accounts/account_extra.html:33
#: app/accounts/templates/accounts/migration.html:21
#: app/accounts/templates/accounts/new.html:21
#: app/comments/templates/comments/widget.html:11
msgid "Add"
msgstr "Добавить"

#: app/accounts/templates/accounts/account_info.html:11
#: app/accounts/templates/accounts/account_promo_resources.html:17
#: app/accounts/templates/accounts/components/stores/account_stores_event.html:14
#: app/accounts/templates/accounts/upsale_request_details.html:57
#: app/media_assets_manager/templates/media_assets_manager/jobs.html:28
msgid "Type"
msgstr "Тип"

#: app/accounts/templates/accounts/account_info.html:19
msgid "not set"
msgstr "не задан"

#: app/accounts/templates/accounts/account_info.html:21
msgid "Verified"
msgstr "Подтверждён"

#: app/accounts/templates/accounts/account_info.html:29
msgid "City"
msgstr "Город"

#: app/accounts/templates/accounts/account_info.html:48
msgid "First login time"
msgstr "Первый логин"

#: app/accounts/templates/accounts/account_info.html:52
msgid "Last play time"
msgstr "Последний просмотр"

#: app/accounts/templates/accounts/account_info.html:57
#: app/accounts/templates/accounts/account_info.html:62
msgid "Registration"
msgstr "Регистрация"

#: app/accounts/templates/accounts/account_info.html:68
#: app/media_assets_manager/templates/media_assets_manager/assets.html:61
#: app/media_assets_manager/templates/media_assets_manager/jobs.html:30
msgid "Updated"
msgstr "Обновлен"

#: app/accounts/templates/accounts/account_info.html:74
msgid "Remote ID"
msgstr "Идентификатор"

#: app/accounts/templates/accounts/account_info.html:81
msgid "Blocked"
msgstr "Заблокирован"

#: app/accounts/templates/accounts/account_info.html:83
#: app/accounts/templates/accounts/components/stores/subscription/subscription_page.html:34
msgid "History"
msgstr "История"

#: app/accounts/templates/accounts/account_info.html:94
#: app/accounts/templates/accounts/account_yakassa.html:18
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:25
msgid "Actions"
msgstr "Действия"

#: app/accounts/templates/accounts/account_info.html:99
#: app/accounts/templates/accounts/show_pin.html:15
msgid "PIN"
msgstr "ПИН"

#: app/accounts/templates/accounts/account_info.html:102
msgid "New password"
msgstr "Новый пароль"

#: app/accounts/templates/accounts/account_info.html:112
msgid "Change number and send password"
msgstr "Изменить номер и отправить пароль"

#: app/accounts/templates/accounts/account_info.html:118
msgid "Delete account"
msgstr "Удалить аккаунт"

#: app/accounts/templates/accounts/account_info.html:121
msgid "Unlink account"
msgstr "Отвязать аккаунт"

#: app/accounts/templates/accounts/account_phone.html:8
msgid "Edit phone number"
msgstr "Редактирование телефона"

#: app/accounts/templates/accounts/account_phone.html:13
msgid "verified"
msgstr "подтверждённый"

#: app/accounts/templates/accounts/account_phone.html:13
msgid "not verified"
msgstr "неподтверждённый"

#: app/accounts/templates/accounts/account_phone.html:19
msgid "Unlink and delete"
msgstr "Отвязать и удалить"

#: app/accounts/templates/accounts/account_phone.html:31
#: app/accounts/templates/accounts/account_set_phone.html:16
#: app/accounts/templates/accounts/contacts.html:16
#: app/accounts/templates/accounts/device_manage.html:18
#: app/accounts/templates/accounts/provider_info.html:16
#: app/accounts/templates/accounts/region_manage.html:27
#: app/accounts/templates/accounts/subscription_manage.html:22
#: app/accounts/templates/accounts/subscription_set_expiration_time.html:16
msgid "Save"
msgstr "Сохранить"

#: app/accounts/templates/accounts/account_promo_resources.html:18
msgid "Granted at"
msgstr "Предоставлен"

#: app/accounts/templates/accounts/account_promo_resources.html:19
#: app/accounts/templates/accounts/blocks/refund_history_table.html:63
#: app/accounts/templates/accounts/components/stores/account_stores_event.html:73
#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:22
#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:61
msgid "Initiator"
msgstr "Инициатор"

#: app/accounts/templates/accounts/account_promo_resources.html:21
msgid "Exhausted"
msgstr "Израсходован"

#: app/accounts/templates/accounts/account_promo_resources.html:50
msgid "Are you sure you want to roll back the exhausted resource?"
msgstr "Вы уверены, что хотите откатить израсходованный ресурс?"

#: app/accounts/templates/accounts/account_promo_resources.html:51
msgid "Roll back"
msgstr "Откатить"

#: app/accounts/templates/accounts/account_promo_resources.html:58
#: app/accounts/templates/accounts/account_promo_resources.html:117
msgid "Are you sure you want to exhaust the resource?"
msgstr "Вы уверены, что хотите израсходовать ресурс?"

#: app/accounts/templates/accounts/account_promo_resources.html:59
#: app/accounts/templates/accounts/account_promo_resources.html:118
#: app/accounts/templates/accounts/account_promo_resources.html:119
msgid "Exhaust"
msgstr "Израсходовать"

#: app/accounts/templates/accounts/account_promo_resources.html:97
msgid "Promo resources"
msgstr "Промо ресурсы"

#: app/accounts/templates/accounts/account_promo_resources.html:105
msgid "Are you sure you want to grant the resource?"
msgstr "Вы уверены, что хотите предоставить ресурс?"

#: app/accounts/templates/accounts/account_promo_resources.html:106
#: app/accounts/templates/accounts/account_promo_resources.html:107
msgid "Grant"
msgstr "Предоставить"

#: app/accounts/templates/accounts/account_promo_resources.html:130
#: app/accounts/templates/accounts/accounts.html:38
#: app/accounts/templates/accounts/accounts.html:40
#: app/media_assets_manager/templates/media_assets_manager/assets.html:30
#: app/media_assets_manager/templates/media_assets_manager/packaging_results.html:19
msgid "Search"
msgstr "Поиск"

#: app/accounts/templates/accounts/account_promo_resources.html:145
msgid "Unable to retrieve information about promo resources"
msgstr "Невозможно получить сведения о промо ресурсах"

#: app/accounts/templates/accounts/account_promo_resources.html:148
msgid "Available resources"
msgstr "Доступные ресурсы"

#: app/accounts/templates/accounts/account_promo_resources.html:149
msgid "Exhausted resources"
msgstr "Израсходованные ресурсы"

#: app/accounts/templates/accounts/account_promo_resources.html:152
msgid "There are no resources at the moment"
msgstr "Ресурсов на данный момент нет"

#: app/accounts/templates/accounts/account_purchases.html:3
msgid "Purchases"
msgstr "Покупки"

#: app/accounts/templates/accounts/account_purchases.html:11
#: app/accounts/templates/accounts/account_upsale_requests.html:31
#: app/accounts/templates/accounts/account_yakassa.html:86
#: app/accounts/templates/accounts/blocks/refund_history_table.html:64
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:102
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:30
#: app/accounts/templates/accounts/components/stores/subscription/history_table.html:27
#: app/accounts/templates/accounts/subscriptions_history.html:16
#: app/accounts/templates/accounts/upsale_request_details.html:86
#: app/accounts/templates/accounts/yakassa_payment_details.html:12
#: app/accounts/templates/accounts/yakassa_subscription_details.html:58
#: app/accounts/templates/accounts/yakassa_subscription_details.html:90
#: app/media_assets_manager/templates/media_assets_manager/jobs.html:27
#: app/promo/templates/promo/cancel_exhaust_resources.html:47
msgid "Status"
msgstr "Статус"

#: app/accounts/templates/accounts/account_purchases.html:12
#: app/accounts/templates/accounts/subscription_details.html:84
#: app/accounts/templates/accounts/subscriptions_history.html:17
msgid "Offer type"
msgstr "Тип оффера"

#: app/accounts/templates/accounts/account_purchases.html:25
#: app/accounts/templates/accounts/subscription_details.html:66
#: app/accounts/templates/accounts/subscriptions_history.html:27
msgctxt "purchase"
msgid "Not confirmed"
msgstr "Не подтверждена"

#: app/accounts/templates/accounts/account_purchases.html:25
#: app/accounts/templates/accounts/subscription_details.html:66
#: app/accounts/templates/accounts/subscriptions_history.html:27
msgctxt "purchase"
msgid "Confirmed"
msgstr "Подтверждена"

#: app/accounts/templates/accounts/account_purchases.html:48
#: app/accounts/templates/accounts/subscription_details.html:59
#: app/accounts/templates/accounts/subscriptions_history.html:50
msgid "Suspended"
msgstr "Приостановлена"

#: app/accounts/templates/accounts/account_purchases.html:50
#: app/accounts/templates/accounts/subscription_details.html:61
#: app/accounts/templates/accounts/subscriptions_history.html:52
msgid "Disabled"
msgstr "Отключена"

#: app/accounts/templates/accounts/account_purchases.html:54
msgid "Will be cancelled"
msgstr "Будет отменена"

#: app/accounts/templates/accounts/account_purchases.html:73
#: app/accounts/templates/accounts/accounts.html:222
msgid "Disable"
msgstr "Отключить"

#: app/accounts/templates/accounts/account_purchases.html:81
#: app/accounts/templates/accounts/subscriptions_history.html:71
msgid "There are no purchases right now"
msgstr "Покупок на данный момент нет"

#: app/accounts/templates/accounts/account_purchases.html:86
msgid "Add purchase"
msgstr "Добавить покупку"

#: app/accounts/templates/accounts/account_purchases.html:89
#: app/accounts/templates/accounts/subscriptions_history.html:8
msgid "Purchase history"
msgstr "История покупок"

#: app/accounts/templates/accounts/account_region.html:16
msgid "Region is not set"
msgstr "Регион не установлен"

#: app/accounts/templates/accounts/account_sessions.html:26
#: app/accounts/templates/accounts/account_yakassa.html:98
#: app/accounts/templates/accounts/account_yakassa.html:117
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:68
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:39
#: app/accounts/templates/accounts/components/stores/subscription/history_table.html:7
#: app/accounts/templates/accounts/components/stores/subscription/notifications_table.html:7
#: app/accounts/templates/accounts/upsale_request_details.html:93
#: app/accounts/templates/accounts/yakassa_subscription_details.html:49
#: app/accounts/templates/accounts/yakassa_subscription_details.html:101
#: app/media_assets_manager/templates/media_assets_manager/utils.html:69
msgid "Details"
msgstr "Подробности"

#: app/accounts/templates/accounts/account_sessions.html:48
msgid "Active sessions"
msgstr "Активные сессии"

#: app/accounts/templates/accounts/account_sessions.html:52
msgid "Cannot retrieve information about sessions"
msgstr "Невозможно получить сведения о сессиях"

#: app/accounts/templates/accounts/account_sessions.html:59
msgid "Last used date"
msgstr "Дата последнего использования"

#: app/accounts/templates/accounts/account_sessions.html:68
msgid "Delete all"
msgstr "Удалить все"

#: app/accounts/templates/accounts/account_sessions.html:69
msgid "Are you sure you want to delete ALL sessions?"
msgstr "Вы уверены, что хотите удалить ВСЕ сессии?"

#: app/accounts/templates/accounts/account_sessions.html:101
msgid "Are you sure you want to delete the session?"
msgstr "Вы уверены, что хотите удалить сессию?"

#: app/accounts/templates/accounts/account_sessions.html:112
msgid "There are no active sessions at the moment."
msgstr "Активных сессий на данный момент нет"

#: app/accounts/templates/accounts/account_set_phone.html:8
msgid "Setup phone number"
msgstr "Установка телефона"

#: app/accounts/templates/accounts/account_upsale_requests.html:2
#: app/accounts/templates/accounts/upsale_request_details.html:3
msgid "Unknown"
msgstr "Неизвестен"

#: app/accounts/templates/accounts/account_upsale_requests.html:3
#: app/accounts/templates/accounts/upsale_request_details.html:3
msgid "Processing"
msgstr "Обработка"

#: app/accounts/templates/accounts/account_upsale_requests.html:4
#: app/accounts/templates/accounts/upsale_request_details.html:3
msgid "Purchase successful"
msgstr "Успешная покупка"

#: app/accounts/templates/accounts/account_upsale_requests.html:5
#: app/accounts/templates/accounts/upsale_request_details.html:3
msgid "Purchase error"
msgstr "Ошибка покупки"

#: app/accounts/templates/accounts/account_upsale_requests.html:6
#: app/accounts/templates/accounts/upsale_request_details.html:3
msgid "Synchronization error"
msgstr "Ошибка синхронизации"

#: app/accounts/templates/accounts/account_upsale_requests.html:7
#: app/accounts/templates/accounts/upsale_request_details.html:3
msgid "Synchronization successful"
msgstr "Успешная синхронизация"

#: app/accounts/templates/accounts/account_upsale_requests.html:20
msgid "Upsale requests"
msgstr "Запросы Upsale"

#: app/accounts/templates/accounts/account_upsale_requests.html:24
msgid "Unable to retrieve information about Upsale service requests"
msgstr "Невозможно получить сведения о запросах к сервису Upsale"

#: app/accounts/templates/accounts/account_upsale_requests.html:29
msgctxt "upsale-requests"
msgid "Request"
msgstr "Заявка"

#: app/accounts/templates/accounts/account_upsale_requests.html:32
msgid "Attempt"
msgstr "Попытка"

#: app/accounts/templates/accounts/account_upsale_requests.html:63
#: app/accounts/templates/accounts/account_yakassa.html:63
#: app/accounts/templates/accounts/upsale_request_details.html:16
msgid "Archive"
msgstr "Архивировать"

#: app/accounts/templates/accounts/account_upsale_requests.html:64
#: app/accounts/templates/accounts/upsale_request_details.html:17
msgid "Are you sure you want to archive the request?"
msgstr "Вы действительно хотите архивировать запрос?"

#: app/accounts/templates/accounts/account_upsale_requests.html:68
#: app/accounts/templates/accounts/upsale_request_details.html:21
msgid "Retry"
msgstr "Повторить"

#: app/accounts/templates/accounts/account_upsale_requests.html:69
#: app/accounts/templates/accounts/upsale_request_details.html:22
msgid "Are you sure you want to repeat your request?"
msgstr "Вы действительно хотите повторить запрос?"

#: app/accounts/templates/accounts/account_upsale_requests.html:80
msgid "There are currently no active requests for the Upsale service"
msgstr "Активных запросов к сервису Upsale на данный момент нет"

#: app/accounts/templates/accounts/account_utm_info.html:4
msgid "UTM tags"
msgstr "UTM-метки"

#: app/accounts/templates/accounts/account_yakassa.html:25
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:11
msgid "Are you sure you want to pause auto-renewal for the subscription?"
msgstr "Вы действительно хотите приостановить автопродление подписки?"

#: app/accounts/templates/accounts/account_yakassa.html:26
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:37
msgid "Pause auto-renewal"
msgstr "Приостановить автопродление"

#: app/accounts/templates/accounts/account_yakassa.html:33
msgid "Are you sure you want to activate the subscription?"
msgstr "Вы действительно хотите активировать подписку?"

#: app/accounts/templates/accounts/account_yakassa.html:34
msgid "Activate"
msgstr "Активировать"

#: app/accounts/templates/accounts/account_yakassa.html:41
msgid "Are you sure you want to terminate the subscription early?"
msgstr "Вы действительно хотите завершить подписку досрочно?"

#: app/accounts/templates/accounts/account_yakassa.html:43
msgid "Terminate early"
msgstr "Завершить досрочно"

#: app/accounts/templates/accounts/account_yakassa.html:52
msgid "Are you sure you want to refund?"
msgstr "Вы действительно хотите осуществить возврат средств?"

#: app/accounts/templates/accounts/account_yakassa.html:54
msgid "Refund"
msgstr "Возврат средств"

#: app/accounts/templates/accounts/account_yakassa.html:62
msgid "Are you sure you want to archive this subscription? Any operations with it will be unavailable."
msgstr "Вы действительно хотите архивировать подписку? Любые операции с ней будут недоступны."

#: app/accounts/templates/accounts/account_yakassa.html:70
#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:61
msgid "Actions are not available"
msgstr "Действия недоступны"

#: app/accounts/templates/accounts/account_yakassa.html:76
msgid "Archived subscriptions"
msgstr "Архивные подписки"

#: app/accounts/templates/accounts/account_yakassa.html:76
msgid "YooKassa"
msgstr "Яндекс.Касса"

#: app/accounts/templates/accounts/account_yakassa.html:80
msgid "Unable to retrieve information about subscriptions"
msgstr "Невозможно получить сведения о подписках"

#: app/accounts/templates/accounts/account_yakassa.html:88
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:78
#: app/accounts/templates/accounts/yakassa_payment_details.html:18
#: app/accounts/templates/accounts/yakassa_subscription_details.html:30
#: app/accounts/templates/accounts/yakassa_subscription_details.html:64
#: app/accounts/templates/accounts/yakassa_subscription_details.html:93
msgid "Expiration date"
msgstr "Дата истечения"

#: app/accounts/templates/accounts/account_yakassa.html:89
#: app/accounts/templates/accounts/yakassa_subscription_details.html:31
msgctxt "yakassa subs"
msgid "Updated at"
msgstr "Дата обновления"

#: app/accounts/templates/accounts/account_yakassa.html:90
#: app/accounts/templates/accounts/yakassa_subscription_details.html:32
msgid "Next charge attempt at"
msgstr "Дата следующего списания"

#: app/accounts/templates/accounts/account_yakassa.html:125
msgid "Error details"
msgstr "Подробности ошибки"

#: app/accounts/templates/accounts/account_yakassa.html:161
msgid "There are no archived subscriptions right now"
msgstr "Архивных подписок на данный момент нет"

#: app/accounts/templates/accounts/account_yakassa.html:163
msgid "There are no active subscriptions right now"
msgstr "Активных подписок на данный момент нет"

#: app/accounts/templates/accounts/account_yakassa.html:193
#: app/accounts/templates/accounts/yakassa_payment_details.html:20
#: app/accounts/templates/accounts/yakassa_payment_method.html:7
#: app/accounts/templates/accounts/yakassa_subscription_details.html:68
#: app/accounts/templates/accounts/yakassa_subscription_details.html:95
msgid "Payment method"
msgstr "Способ платежа"

#: app/accounts/templates/accounts/account_yakassa.html:196
msgid "Are you sure you want to unlink the bank card?"
msgstr "Вы действительно хотите отвязать банковскую карту?"

#: app/accounts/templates/accounts/account_yakassa.html:203
#: app/accounts/templates/accounts/blocks/refund_history_table.html:48
msgid "Refund history"
msgstr "История возвратов"

#: app/accounts/templates/accounts/account_yakassa.html:204
msgid "Subscriptions archive"
msgstr "Архив подписок"

#: app/accounts/templates/accounts/account_yakassa_lib.html:9
msgid "card"
msgstr "карта"

#: app/accounts/templates/accounts/accounts.html:37
msgid "Precise search"
msgstr "Точный поиск"

#: app/accounts/templates/accounts/accounts.html:68
msgid "records per page"
msgstr "записей на странице"

#: app/accounts/templates/accounts/accounts.html:87
msgid "Registration date"
msgstr "Дата регистрации"

#: app/accounts/templates/accounts/accounts.html:210
msgid "Found"
msgstr "Найдено"

#: app/accounts/templates/accounts/accounts.html:214
msgid "With selected"
msgstr "С выбранными"

#: app/accounts/templates/accounts/accounts.html:218
msgid "Enable"
msgstr "Подключить"

#: app/accounts/templates/accounts/accounts.html:246
msgid "Today"
msgstr "Сегодня"

#: app/accounts/templates/accounts/accounts.html:247
msgid "Yesterday"
msgstr "Вчера"

#: app/accounts/templates/accounts/accounts.html:248
msgid "Last 7 days"
msgstr "Последние 7 дней"

#: app/accounts/templates/accounts/accounts.html:249
msgid "Last 30 days"
msgstr "Последние 30 дней"

#: app/accounts/templates/accounts/accounts.html:250
msgid "This month"
msgstr "Этот месяц"

#: app/accounts/templates/accounts/accounts.html:251
msgid "Last month"
msgstr "Последний месяц"

#: app/accounts/templates/accounts/accounts.html:259
#: app/accounts/templates/accounts/batch_subscriptions.html:70
#: app/accounts/templates/accounts/subscription_manage.html:44
#: app/accounts/templates/accounts/subscription_set_expiration_time.html:39
msgid "Select manually"
msgstr "Выбрать вручную"

#: app/accounts/templates/accounts/accounts.html:260
#: app/accounts/templates/accounts/batch_subscriptions.html:71
#: app/accounts/templates/accounts/subscription_manage.html:45
#: app/accounts/templates/accounts/subscription_set_expiration_time.html:40
#: app/vod/homescreen_collections/form.py:55
msgid "Clear"
msgstr "Очистить"

#: app/accounts/templates/accounts/accounts.html:261
#: app/accounts/templates/accounts/batch_subscriptions.html:72
#: app/accounts/templates/accounts/subscription_manage.html:46
#: app/accounts/templates/accounts/subscription_set_expiration_time.html:41
#: app/vod/homescreen_collections/form.py:47
msgid "Choose"
msgstr "Выбрать"

#: app/accounts/templates/accounts/batch_subscriptions.html:19
msgctxt "box title"
msgid "Create purchase"
msgstr "Создание покупки"

#: app/accounts/templates/accounts/batch_subscriptions.html:21
msgctxt "box title"
msgid "Cancel purchase"
msgstr "Отмена покупки"

#: app/accounts/templates/accounts/batch_subscriptions.html:26
#: app/accounts/templates/accounts/device_manage.html:8
#: app/accounts/templates/accounts/device_manage.html:18
#: app/media_assets_manager/templates/media_assets_manager/assets.html:20
#: app/media_assets_manager/templates/media_assets_manager/assets.html:22
#: app/promo/templates/promo/pools/create_range_mini_form.html:20
#: app/user_utils/templates/user_utils/linkshortener/linkshortener_form.html:21
msgid "Create"
msgstr "Создать"

#: app/accounts/templates/accounts/batch_subscriptions.html:26
#: app/accounts/templates/accounts/subscription_disable.html:16
msgctxt "action button"
msgid "Cancel"
msgstr "Отменить"

#: app/accounts/templates/accounts/batch_subscriptions.html:29
msgid "{action_text} subscription {offer} for {count} account?"
msgid_plural "{action_text} subscription {offer} for {count} accounts?"
msgstr[0] "{action_text} подписку {offer} для {count} аккаунта?"
msgstr[1] "{action_text} подписку {offer} для {count} аккаунтов?"
msgstr[2] "{action_text} подписку {offer} для {count} аккаунтов?"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:6
msgid "Accepted"
msgstr "Принят"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:10
#: app/media_assets_manager/templates/media_assets_manager/utils.html:1
msgid "Fail"
msgstr "Ошибка"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:37
msgid "See error details"
msgstr "Смотреть ошибку"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:39
msgid "No errors"
msgstr "Ошибок нет"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:53
msgid "Refund history is not available."
msgstr "История возвратов недоступна."

#: app/accounts/templates/accounts/blocks/refund_history_table.html:58
#: app/accounts/templates/accounts/components/stores/account_stores_event.html:25
#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:32
#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:63
msgid "Subscription"
msgstr "Подписка"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:59
msgid "Payment ID"
msgstr "ID платежа"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:62
msgid "Succeeded at"
msgstr "Дата исполнения"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:65
msgid "Error text"
msgstr "Текст ошибки"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:75
msgid "No refund history."
msgstr "История возвратов отсутствует."

#: app/accounts/templates/accounts/components/boxes/fail.html:1
msgid "Unable to retrieve information."
msgstr "Невозможно получить сведения"

#: app/accounts/templates/accounts/components/boxes/no_data.html:1
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:59
msgid "No data"
msgstr "Нет данных"

#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_box.html:3
msgid "Stores subscriptions"
msgstr "Подписки Stores"

#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:9
msgid "This action is irreversible!"
msgstr "Это действие необратимо!"

#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:46
msgid "Are you sure you want to enable auto-renewal for the subscription?"
msgstr "Вы действительно хотите возобновить автопродление подписки?"

#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:49
msgid "Enable auto-renewal"
msgstr "Возобновить автопродление"

#: app/accounts/templates/accounts/components/stores/account_store_subscriptions_table.html:103
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:77
msgid "Payment system"
msgstr "Платёжная система"

#: app/accounts/templates/accounts/components/stores/account_stores_event.html:8
msgid "Event"
msgstr "Событие"

#: app/accounts/templates/accounts/components/stores/account_stores_event.html:12
msgctxt "event"
msgid "Added"
msgstr "Добавлено"

#: app/accounts/templates/accounts/components/stores/account_stores_event.html:41
#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:39
#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:64
msgid "Notification"
msgstr "Уведомление"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:2
msgctxt "auto renewal"
msgid "unknown"
msgstr "неизвестен"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:3
msgctxt "auto renewal"
msgid "active"
msgstr "активно"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:4
msgctxt "auto renewal"
msgid "not active"
msgstr "не активно"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:8
msgctxt "subscription status"
msgid "Unknown"
msgstr "Неизвестен"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:9
msgctxt "subscription status"
msgid "Active"
msgstr "Активна"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:10
msgctxt "subscription status"
msgid "Not active"
msgstr "Не активна"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:31
msgid "Counter"
msgstr "Счётчик"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:32
msgid "Last failure reason"
msgstr "Причина последней неудачи"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:33
msgid "Next attempt"
msgstr "Следующая попытка"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:34
msgid "Updated at"
msgstr "Обновлено"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:42
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:83
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:87
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:96
msgid "Data from source"
msgstr "Данные источника"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:63
#: cmf/templates/admin/select_utils.html:7
msgid "Close"
msgstr "Закрыть"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:73
msgid "Transaction ID"
msgstr "ID транзакции"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:74
msgid "Product ID"
msgstr "ID продукта"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:75
msgid "Purchase ID"
msgstr "ID покупки"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:76
msgid "Offer ID"
msgstr "ID оффера"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:79
msgid "Retry period"
msgstr "Ретрай-период"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:80
msgid "Grace period"
msgstr "Грейс-период"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:92
msgid "Origin state data"
msgstr "Данные состояния источника"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:100
msgid "Auto renew status"
msgstr "Статус автопродления"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:104
msgid "Cleaning status"
msgstr "Статус клининга"

#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:108
#: app/accounts/templates/accounts/components/stores/account_stores_lib.html:112
msgid "Cleaning info"
msgstr "Информация о клининге"

#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:19
#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:46
msgid "State details"
msgstr "Детали состояния"

#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:29
msgid "Subscription details"
msgstr "Детали подписки"

#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:36
msgid "Notification details"
msgstr "Детали уведомления"

#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:60
msgid "Added"
msgstr "Добавлен"

#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:62
msgid "Initiator data"
msgstr "Данные инициатора"

#: app/accounts/templates/accounts/components/stores/subscription/history_table.html:12
msgid "Subscription state at"
msgstr "Состояние подписки на"

#: app/accounts/templates/accounts/components/stores/subscription/history_table.html:26
msgid "Changed at"
msgstr "Время изменения"

#: app/accounts/templates/accounts/components/stores/subscription/notifications_table.html:12
msgid "Notified at"
msgstr "Нотификация от"

#: app/accounts/templates/accounts/components/stores/subscription/notifications_table.html:20
msgid "Notification time"
msgstr "Время получения"

#: app/accounts/templates/accounts/components/stores/subscription/subscription_page.html:20
msgid "Update state"
msgstr "Обновить состояние"

#: app/accounts/templates/accounts/components/stores/subscription/subscription_page.html:33
msgid "Notifications"
msgstr "Нотификации"

#: app/accounts/templates/accounts/components/stores/subscription/subscription_page.html:35
msgid "Event log"
msgstr "Журнал событий"

#: app/accounts/templates/accounts/contacts.html:8
msgid "Contacts"
msgstr "Контакты"

#: app/accounts/templates/accounts/device_manage.html:8
msgid "device"
msgstr "устройство"

#: app/accounts/templates/accounts/migration.html:7
msgid "Users migration"
msgstr "Миграция пользователей"

#: app/accounts/templates/accounts/new.html:7
msgid "New user"
msgstr "Новый пользователь"

#: app/accounts/templates/accounts/provider_info.html:8
msgid "Additional information"
msgstr "Дополнительная информация"

#: app/accounts/templates/accounts/region_manage.html:19
msgid "Edit region"
msgstr "Редактирование региона"

#: app/accounts/templates/accounts/reset_password.html:8
#: app/accounts/templates/accounts/reset_password.html:21
msgid "Reset password"
msgstr "Сбросить пароль"

#: app/accounts/templates/accounts/reset_password.html:11
msgid "Are you sure you want to reset password for"
msgstr "Вы действительно хотите сброcить пароль для"

#: app/accounts/templates/accounts/reset_password.html:12
msgid "Message for user"
msgstr "Сообщение, которое будет отправлено пользователю"

#: app/accounts/templates/accounts/show_pin.html:8
msgid "Show PIN"
msgstr "Просмотр PIN"

#: app/accounts/templates/accounts/show_pin.html:14
msgctxt "account info"
msgid "Name"
msgstr "Имя"

#: app/accounts/templates/accounts/show_pin.html:40
msgid "PIN is undefined"
msgstr "PIN не определён"

#: app/accounts/templates/accounts/subscription_details.html:6
msgctxt "offer state"
msgid "created"
msgstr "создан"

#: app/accounts/templates/accounts/subscription_details.html:8
msgctxt "offer state"
msgid "enabled"
msgstr "включен"

#: app/accounts/templates/accounts/subscription_details.html:10
msgctxt "offer state"
msgid "changed"
msgstr "изменён"

#: app/accounts/templates/accounts/subscription_details.html:12
msgctxt "offer state"
msgid "error"
msgstr "ошибка"

#: app/accounts/templates/accounts/subscription_details.html:14
msgctxt "offer state"
msgid "cancelled"
msgstr "отменён"

#: app/accounts/templates/accounts/subscription_details.html:74
msgid "Re-creating VOD subscriptions"
msgstr "Повторное создание подписок VOD"

#: app/accounts/templates/accounts/subscription_details.html:91
msgid "Change expiration date"
msgstr "Изменить время истечения"

#: app/accounts/templates/accounts/subscription_details.html:97
msgid "Unsubscribed"
msgstr "Прекращена"

#: app/accounts/templates/accounts/subscription_details.html:113
msgid "In the process of change"
msgstr "В процессе изменения"

#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:49
#: app/accounts/templates/accounts/components/stores/subscription/events_table.html:65
#: app/accounts/templates/accounts/subscription_details.html:117
msgid "State"
msgstr "Состояние"

#: app/accounts/templates/accounts/subscription_details.html:122
msgid "Date"
msgstr "Дата"

#: app/accounts/templates/accounts/subscription_details.html:131
msgid "Channel list"
msgstr "Список каналов"

#: app/accounts/templates/accounts/subscription_details.html:141
msgid "Number"
msgstr "Номер"

#: app/accounts/templates/accounts/subscription_disable.html:8
msgid "Cancel purchase"
msgstr "Отменить покупку"

#: app/accounts/templates/accounts/subscription_manage.html:10
msgid "Create purchase"
msgstr "Создать покупку"

#: app/accounts/templates/accounts/subscription_manage.html:12
msgid "Edit purchase"
msgstr "Редактировать покупку"

#: app/accounts/templates/accounts/subscription_set_expiration_time.html:8
msgid "Change expiration time for purchase"
msgstr "Изменить время истечения покупки"

#: app/accounts/templates/accounts/upsale_request_details.html:28
msgid "Purchase"
msgstr "Покупка"

#: app/accounts/templates/accounts/upsale_request_details.html:37
msgid "Trial"
msgstr "Триал"

#: app/accounts/templates/accounts/upsale_request_details.html:48
msgid "Device ID"
msgstr "ID устройства"

#: app/accounts/templates/accounts/upsale_request_details.html:49
msgid "User ID"
msgstr "ID юзера"

#: app/accounts/templates/accounts/upsale_request_details.html:50
msgid "Profile ID"
msgstr "ID профиля"

#: app/accounts/templates/accounts/upsale_request_details.html:58
msgid "User Agent"
msgstr "Агент"

#: app/accounts/templates/accounts/upsale_request_details.html:59
msgid "Manufacturer"
msgstr "Изготовитель"

#: app/accounts/templates/accounts/upsale_request_details.html:60
msgid "Model"
msgstr "Модель"

#: app/accounts/templates/accounts/upsale_request_details.html:66
msgid "Version"
msgstr "Версия"

#: app/accounts/templates/accounts/upsale_request_details.html:67
msgid "Check sum"
msgstr "Контрольная сумма"

#: app/accounts/templates/accounts/upsale_request_details.html:68
msgid "Assembly type"
msgstr "Тип сборки"

#: app/accounts/templates/accounts/components/stores/account_stores_event.html:57
#: app/accounts/templates/accounts/upsale_request_details.html:69
#: app/media_assets_manager/templates/media_assets_manager/assets.html:58
#: app/media_assets_manager/templates/media_assets_manager/utils.html:49
#: app/media_assets_manager/templates/media_assets_manager/utils.html:158
msgid "Origin"
msgstr "Источник"

#: app/accounts/templates/accounts/upsale_request_details.html:75
msgid "Number of attempts"
msgstr "Количество попыток"

#: app/accounts/templates/accounts/upsale_request_details.html:80
msgid "Communication"
msgstr "Коммуникация"

#: app/accounts/templates/accounts/upsale_request_details.html:85
msgid "Time"
msgstr "Время"

#: app/accounts/templates/accounts/upsale_request_details.html:87
msgid "Method"
msgstr "Метод"

#: app/accounts/templates/accounts/upsale_request_details.html:127
msgid "Request"
msgstr "Запрос"

#: app/accounts/templates/accounts/upsale_request_details.html:134
msgid "Response"
msgstr "Ответ"

#: app/accounts/templates/accounts/upsale_request_details.html:141
msgid "Errors"
msgstr "Ошибки"

#: app/accounts/templates/accounts/yakassa_payment_details.html:9
msgid "Payment"
msgstr "Платёж"

#: app/accounts/templates/accounts/yakassa_payment_details.html:14
#: app/accounts/templates/accounts/yakassa_subscription_details.html:60
msgid "Cancellation details"
msgstr "Причина отмены"

#: app/accounts/templates/accounts/blocks/refund_history_table.html:60
#: app/accounts/templates/accounts/yakassa_payment_details.html:16
#: app/accounts/templates/accounts/yakassa_subscription_details.html:62
#: app/accounts/templates/accounts/yakassa_subscription_details.html:91
msgid "Amount"
msgstr "Сумма"

#: app/accounts/templates/accounts/yakassa_payment_details.html:19
#: app/accounts/templates/accounts/yakassa_subscription_details.html:65
#: app/accounts/templates/accounts/yakassa_subscription_details.html:94
msgid "Captured at"
msgstr "Дата снятия"

#: app/accounts/templates/accounts/yakassa_payment_details.html:29
msgid "Data from service"
msgstr "Данные сервиса"

#: app/accounts/templates/accounts/yakassa_payment_details.html:37
msgid "Saved data"
msgstr "Cохранённые данные"

#: app/accounts/templates/accounts/yakassa_subscription_details.html:50
msgid "Last payment"
msgstr "Последний платёж"

#: app/accounts/templates/accounts/yakassa_subscription_details.html:52
msgid "Synchronize"
msgstr "Синхронизировать"

#: app/accounts/templates/accounts/yakassa_subscription_details.html:56
msgid "Missing data about last payment"
msgstr "Информация о последнем платеже отсутствует"

#: app/accounts/templates/accounts/yakassa_subscription_details.html:79
msgid "Payment history"
msgstr "История платежей"

#: app/accounts/templates/accounts/yakassa_subscription_details.html:83
msgid "Unable to retrieve payment information"
msgstr "Невозможно получить сведения о платежах"

#: app/accounts/templates/accounts/yakassa_subscription_details.html:132
msgid "No payment records"
msgstr "Информация о платежах отсутствует"

#: app/accounts/views.py:243 app/ocp/views.py:292
msgid "Account created successfully"
msgstr "Аккаунт успешно создан"

#: app/accounts/views.py:245 app/accounts/views.py:249 app/ocp/views.py:297
msgid "Account creation error"
msgstr "Ошибка создания аккаунта"

#: app/activity/templates/activity/base_widget.html:9
msgid "Check activity"
msgstr "Активность"

#: app/ads/models.py:327
msgid "Should only contain latin letters, numbers or underscore"
msgstr "Может содержать только латинские буквы, цифры или нижнее подчёркивание"

#: app/audiences/models.py:35
msgid "Enabled"
msgstr "Включено"

#: app/audiences/models.py:36
msgid "Offers"
msgstr "Офферы"

#: app/audiences/models.py:68
msgid "Show for subset of offers"
msgstr "Показать для выборки офферов"

#: app/cms/views.py:52
msgid "Record was successfully saved."
msgstr "Запись успешно сохранена"

#: app/comments/templates/comments/create.html:26
msgid "Add comment"
msgstr "Добавление комментария"

#: app/comments/templates/comments/edit.html:24
msgid "Edit comment"
msgstr "Редактирование комментария"

#: app/comments/templates/comments/list.html:8
msgid "Create new record"
msgstr "Создать новую запись"

#: app/comments/templates/comments/list.html:18
msgid "To Commented Entity"
msgstr "К прокоментированным записям"

#: app/comments/templates/comments/list.html:24
msgid "Edit record"
msgstr "Редактировать запись"

#: app/comments/templates/comments/list.html:38
msgid "Are you sure you want to delete this record?"
msgstr "Вы уверены, что хотите удалить эту запись?"

#: app/comments/templates/comments/list.html:38
msgid "Delete comment"
msgstr "Удалить комментарий"

#: app/comments/templates/comments/widget.html:7
msgid "Comments"
msgstr "Комментарии"

#: app/comments/templates/comments/widget.html:30
msgctxt "comment"
msgid "modified"
msgstr "отредактировано"

#: app/comments/templates/comments/widget.html:37
msgid "There is no comments right now"
msgstr "Комментариев на данный момент нет"

#: app/comments/templates/comments/widget.html:42
msgid "All comments"
msgstr "Все комментарии"

#: app/common/fields.py:451 app/common/fields.py:527
msgid "Not a valid choice"
msgstr "Некорректный вариант"

#: app/common/form.py:104
msgid "Invalid URL."
msgstr "Некорректный URL"

#: app/live/validators.py:29
msgid "Media is already used"
msgstr "Медия уже использована"

#: app/live/validators.py:91
msgid "Value from 'Media Default' should not be used in 'Media To Region' or 'Media To Timezone' fields."
msgstr "Значение из «Media Default» не следует использовать в полях «Media To Region» или «Media To Timezone»."

#: app/live/validators.py:100
msgid "Media can't be used both in media to region and media to timezone:"
msgstr "Медия не может быть использована одновременно в «Media To Region» и «Media To Timezone»"

#: app/media_assets_manager/forms.py:69
msgid "Enable Adaptive Bitrate Streaming"
msgstr "Включить стриминг с адаптивным битрейтом"

#: app/media_assets_manager/forms.py:70
msgid "Enable 50 Frames Per Second Streaming"
msgstr "Включить стриминг с 50 фреймами в секунду"

#: app/media_assets_manager/templates/media_assets_manager/asset.html:19
#: app/media_assets_manager/templates/media_assets_manager/packaging_results.html:8
msgid "Packaging Results"
msgstr "Результаты пакетирования"

#: app/media_assets_manager/templates/media_assets_manager/asset.html:35
msgid "Playback URL"
msgstr "URL воспроизведения"

#: app/media_assets_manager/templates/media_assets_manager/asset.html:53
msgid "Generate URL"
msgstr "Генерировать URL"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:11
msgid "Media Assets"
msgstr "Медиа ассеты"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:44
#: app/media_assets_manager/templates/media_assets_manager/jobs.html:17
msgid "Failed to access media assets controller service"
msgstr "Не удалось получить доступ к сервису контроллера медиа ассетов"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:50
#: app/seller/templates/seller/index.html:42
msgid "Check All"
msgstr "Отметить все"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:54
#: app/media_assets_manager/templates/media_assets_manager/utils.html:162
msgid "Deleted"
msgstr "Удалённые"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:56
#: app/media_assets_manager/templates/media_assets_manager/jobs.html:23
msgid "Id"
msgstr "ID"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:59
msgid "Last Job"
msgstr "Последнее задание"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:60
#: app/media_assets_manager/templates/media_assets_manager/jobs.html:29
#: app/media_assets_manager/templates/media_assets_manager/utils.html:160
#: app/promo/templates/promo/cancel_exhaust_resources.html:48
msgid "Created"
msgstr "Создан"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:68
#: app/seller/templates/seller/index.html:55
msgid "Select"
msgstr "Выбрать"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:77
msgid "Retranscode"
msgstr "Ретранскодировать"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:111
msgid "All Jobs"
msgstr "Все задания"

#: app/media_assets_manager/templates/media_assets_manager/assets.html:127
msgid "No media assets found"
msgstr "Медиа ассеты не найдены"

#: app/media_assets_manager/templates/media_assets_manager/jobs.html:8
msgid "Jobs"
msgstr "Задания"

#: app/media_assets_manager/templates/media_assets_manager/jobs.html:8
#: app/media_assets_manager/templates/media_assets_manager/jobs.html:25
#: app/media_assets_manager/templates/media_assets_manager/utils.html:153
msgid "Media Asset"
msgstr "Медиа ассет"

#: app/media_assets_manager/templates/media_assets_manager/jobs.html:64
msgid "No jobs found"
msgstr "Задания не найдены"

#: app/media_assets_manager/templates/media_assets_manager/packaging_results.html:33
msgid "Failed to access media assets provider service"
msgstr "Не удалось получить доступ к сервису провайдера медиа ассетов"

#: app/media_assets_manager/templates/media_assets_manager/packaging_results.html:37
msgid "No packagin results found"
msgstr "Не найдены результаты пакетирования"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:1
msgid "Pending"
msgstr "Ожидается"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:1
msgid "In Progress"
msgstr "В процессе"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:1
msgid "Success"
msgstr "Успешно"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:1
#: app/media_assets_manager/templates/media_assets_manager/utils.html:4
#: app/media_assets_manager/templates/media_assets_manager/utils.html:7
msgid "Unspecified"
msgstr "Не установлено"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:4
msgid "Transcode"
msgstr "Транскодировать"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:7
msgid "Local Storage"
msgstr "Локальное хранилище"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:7
msgid "External Storage"
msgstr "Внешнее хранилище"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:14
msgid "Job Status"
msgstr "Статус задания"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:25
msgid "Job Type"
msgstr "Тип задания"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:36
#: app/media_assets_manager/templates/media_assets_manager/utils.html:213
msgid "Origin Url"
msgstr "URL источника"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:47
msgid "Transcode Data"
msgstr "Дата транскодирования"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:50
msgid "Profile"
msgstr "Профиль"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:51
msgid "Download Path"
msgstr "Путь скачивания"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:52
msgid "Result Path"
msgstr "Путь результатов"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:58
msgid "Logs"
msgstr "Логи"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:69
#: app/media_assets_manager/templates/media_assets_manager/utils.html:155
msgid "Job"
msgstr "Задание"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:103
msgid "Transcoding Results"
msgstr "Результаты танскодирования"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:122
msgid "Width"
msgstr "Ширина"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:123
msgid "Hight"
msgstr "Высота"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:124
msgid "URLs"
msgstr "Список URL"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:156
msgid "Path"
msgstr "Путь"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:157
msgid "Transcoding"
msgstr "Транскодируется"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:159
msgid "Results"
msgstr "Результаты"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:161
msgid "Expires"
msgstr "Истекает"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:184
msgid "No results found"
msgstr "Результаты не найдены"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:191
msgid "Origin Details"
msgstr "Подробности источника"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:212
msgid "Url"
msgstr "URL"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:216
msgid "Start Pos"
msgstr "Начальная позиция"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:217
msgid "End Pos"
msgstr "Конечная Позиция"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:233
msgid "Showing"
msgstr "Показаны с"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:233
msgid "to"
msgstr "до"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:233
msgid "of"
msgstr "из"

#: app/media_assets_manager/templates/media_assets_manager/utils.html:233
msgid "entires"
msgstr "записей"

#: app/media_assets_manager/views.py:120
msgid "Media asset successfully created"
msgstr "Медиа ассет успешно создан"

#: app/media_assets_manager/views.py:125
msgid "Create Media Asset"
msgstr "Создать медиа ассет"

#: app/media_assets_manager/views.py:155
msgid "Media asset successfully updated"
msgstr "Медиа ассет успешно обновлён"

#: app/media_assets_manager/views.py:180
msgid "Update Media Asset"
msgstr "Обновить медиа ассет"

#: app/media_assets_manager/views.py:221
msgid "Media assets successfully set to retranscode"
msgstr "Медиа ассет успешно отправлен на ретранскодирование"

#: app/media_assets_manager/views.py:235
msgid "Media assets successfully set to delete"
msgstr "Медиа ассет успешно удалён"

#: app/ocp/views.py:265
msgid "Account not found"
msgstr "Аккаунт не найден"

#: app/ocp/views.py:284
msgid "The account is linked to another provider"
msgstr "Аккаунт привязан к другому провайдеру"

#: app/ocp/views.py:413
msgid "Provider data updated"
msgstr "Данные провайдера изменены"

#: app/promo/templates/promo/cancel_exhaust_resources.html:44
msgid "Promo Resources Exhaustion Status"
msgstr "Статус истощения промо ресурсов"

#: app/promo/templates/promo/cancel_exhaust_resources.html:47
msgid "in progress"
msgstr "В процессе"

#: app/promo/templates/promo/cancel_exhaust_resources.html:47
msgid "finished"
msgstr "завершено"

#: app/promo/templates/promo/cancel_exhaust_resources.html:47
msgid "error"
msgstr "ошибка"

#: app/promo/templates/promo/cancel_exhaust_resources.html:49
msgid "Finished"
msgstr "Завершено"

#: app/promo/templates/promo/cancel_exhaust_resources.html:50
msgid "Error"
msgstr "Ошибка"

#: app/promo/templates/promo/cancel_exhaust_resources.html:59
msgid "Cancel Promo Resources Exhaustion"
msgstr "Отменить исчерпание промо-ресурсов"

#: app/promo/templates/promo/cancel_exhaust_resources.html:63
msgid "This will cancel the currently running promo resources exhaustion process."
msgstr "Это отменит текущий процесс исчерпания промо-ресурсов."

#: app/promo/templates/promo/cancel_exhaust_resources.html:64
msgid "Are you sure you want to proceed?"
msgstr "Вы уверены, что хотите продолжить?"

#: app/promo/templates/promo/cancel_exhaust_resources.html:66
msgid "The promo resources exhaustion process has already finished."
msgstr "Процесс исчерпания промо-ресурсов уже завершен."

#: app/promo/templates/promo/cancel_exhaust_resources.html:68
msgid "The promo resources exhaustion process finished with an error."
msgstr "Процесс исчерпания промо-ресурсов завершился с ошибкой."

#: app/promo/templates/promo/cancel_exhaust_resources.html:70
msgid "No active promo resources exhaustion process found."
msgstr "Процесс исчерпания активных промо-ресурсов не обнаружен."

#: app/promo/templates/promo/cancel_exhaust_resources.html:89
msgid "Cancel Exhaustion"
msgstr "Отменить исчерпание"

#: app/promo/templates/promo/cancel_exhaust_resources.html:92
msgid "Are you sure you want to cancel the promo resources exhaustion process?"
msgstr "Вы уверены, что хотите отменить процесс исчерпания промо-ресурсов?"

#: app/promo/templates/promo/promo_resources/promo_resources_list_items.html:21
msgid "Statistics"
msgstr "Статистика"

#: app/promo/views.py:623
msgid "Check value"
msgstr "Проверить значение"

#: app/purchases/forms.py:57
msgid "Select all"
msgstr "Выбрать все"

#: app/purchases/templates/purchases/provider/edit.html:56
msgid "Export"
msgstr "Экспорт"

#: app/purchases/templates/purchases/provider/networks_edit.html:22
msgid "Add from File"
msgstr "Добавить из файла"

#: app/security/forms.py:28
msgid "Not a valid dictionary."
msgstr "Некорректный словарь."

#: app/security/forms.py:37
msgid "Invalid JSON data."
msgstr "Некорректный JSON."

#: app/settings/offer/models.py:68
msgid "Price must be a valid number with two decimal places"
msgstr "Цена должна быть числом с двумя десятичными знаками."

#: app/vod/homescreen_collections/form.py:60
msgid "Move to top"
msgstr "Переместиться наверх"

#: app/vod/homescreen_collections/views.py:357
msgid "Collection not found"
msgstr "Коллекция не найдена"

#: app/vod/homescreen_collections/views.py:362
msgid "Collection update is started."
msgstr "Обновление коллекции начато"

#: cmf/templates/admin/select_utils.html:8
msgid "Preview selected items"
msgstr "Просмотреть выбранные элементы"

#: cmf/templates/admin/select_utils.html:9
msgid "Reset"
msgstr "Сбросить"

