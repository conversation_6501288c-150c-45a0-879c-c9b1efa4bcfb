{% extends 'admin/model/cms_edit.html' if model is defined else 'admin/model/cms_create.html' %}


{% block head %}
    {{ super() }}
    <style>
        .perm-editor {
            right: 0;
            margin: 20px;
        }
        .perm-editor label {
            display: inline-block;
        }
        .perm-editor li {
            line-height: 2em;
        }
        .perm-editor span.title {
            min-width: 13em;
            display: inline-block;
        }
        .perm-editor span.label {
            margin-right: 5px;
            cursor: pointer;
        }
    </style>
{% endblock %}

{% block tail_js %}
    {{ super() }}

    <script type="text/javascript">
            var Roles = App['roles'].default;
            var allViews = {{ admin_view.get_options() | tojson }}

            function wrap($field) {
                var $form = $field.parents('form')
                $form.find('input[type="submit"]').first().before($('<div class="box box-primary"><div class="perm-editor"/></div>'))
                if ($field.val() !== '') {
                    var data = JSON.parse($field.val())
                    if (Array.isArray(data)) data = {}
                } else {
                    var data = {}
                }

                var roles = Roles($('.perm-editor').get(0), allViews, data);

                $field.hide();
                $form.submit(function () {
                    if (roles.isChanged()) {
                        $field.val(JSON.stringify(roles.getValue()));
                    }
                });
            }

            wrap($('#permissions'))
    </script>
{% endblock %}
