{% import 'common/utils.html' as utils %}
{% extends 'admin/master.html' %}

{% block head_css %}
    {{ super () }}
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">

    <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
{% endblock %}

{% block tail_js %}
    {{ super() }}
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
    <script src="//cdn.jsdelivr.net/jquery.ui-contextmenu/1/jquery.ui-contextmenu.min.js"></script>
    <script>
        $(function() {
            $("#regions").fancytree({
                source: {{ model.render_for_tree()|tojson }},
                extensions: ["glyph", "table", "edit", "gridnav"],
                table: {
                    indentation: 20,
                },
                glyph: {
                    map: {
                        doc: "fa fa-globe",
                        docOpen: "fa fa-globe",
                        checkbox: "fa fa-square-o",
                        checkboxSelected: "fa fa-check-square-o",
                        checkboxUnknown: "fa fa-square",
                        dragHelper: "fa fa-arrow-right",
                        dropMarker: "fa fa-long-arrow-right",
                        error: "fa fa-warning",
                        expanderClosed: "fa fa-caret-right",
                        expanderLazy: "fa fa-angle-right",
                        expanderOpen: "fa fa-caret-down",
                        folder: "fa fa-globe",
                        folderOpen: "fa fa-globe",
                        loading: "fa fa-spinner fa-pulse"
                    }
			    },
                createNode: function(event, data) {
                    var node = data.node;
                },
                renderColumns: function(event, data) {
                    var node = data.node;
                    console.log(node)
                    $tdList = $(node.tr).find(">td");
                    $tdList.eq(1).find("input").val(node.data.timezone);
                    $tdList.eq(2).find("textarea").val(node.data.description);
                    $tdList.find(".editnode").on("input", function(e){
                        var $this = $(this),
                            name = $this.attr("name"),
                            val = $this.val();
                        node.data[name] = val;
                    })
                }
            });
        }).on("nodeCommand", function(event, data){
            var refNode, moveMode,
                tree = $("#regions").fancytree("getTree"),
                node = tree.getActiveNode();

            switch( data.cmd ) {
            case "moveUp":
                refNode = node.getPrevSibling();
                if( refNode ) {
                    node.moveTo(refNode, "before");
                    node.setActive();
                }
                break;
            case "moveDown":
                refNode = node.getNextSibling();
                if( refNode ) {
                    node.moveTo(refNode, "after");
                    node.setActive();
                }
                break;
            case "indent":
                refNode = node.getPrevSibling();
                if( refNode ) {
                    node.moveTo(refNode, "child");
                    refNode.setExpanded();
                    node.setActive();
                }
                break;
            case "outdent":
                if( !node.isTopLevel() ) {
                    node.moveTo(node.getParent(), "after");
                    node.setActive();
                }
                break;
            case "rename":
                node.editStart();
                break;
            case "remove":
                refNode = node.getNextSibling() || node.getPrevSibling() || node.getParent();
                node.remove();
                if( refNode ) {
                    refNode.setActive();
                }
                break;
            case "addChild":
                node.editCreateNode("child", "");
                break;
            case "addSibling":
                node.editCreateNode("after", "");
                break;
            case "cut":
                CLIPBOARD = {mode: data.cmd, data: node};
                break;
            case "copy":
                CLIPBOARD = {
                    mode: data.cmd,
                    data: node.toDict(function(n){
                        delete n.key;
                    })
                };
                break;
            case "clear":
                CLIPBOARD = null;
                break;
            case "paste":
                if( CLIPBOARD.mode === "cut" ) {
                    // refNode = node.getPrevSibling();
                    CLIPBOARD.data.moveTo(node, "child");
                    CLIPBOARD.data.setActive();
                } else if( CLIPBOARD.mode === "copy" ) {
                    node.addChildren(CLIPBOARD.data).setActive();
                }
                break;
            default:
                alert("Unhandled command: " + data.cmd);
                return;
            }
        })

        $("#regions").contextmenu({
            delegate: "span.fancytree-node",
            menu: [
                {title: "Edit", cmd: "rename", faIcon: "fa-pencil" },
                {title: "Delete", cmd: "remove", uiIcon: "ui-icon-trash" },
                {title: "----"},
                {title: "New sibling", cmd: "addSibling", uiIcon: "ui-icon-plus" },
                {title: "New child", cmd: "addChild", uiIcon: "ui-icon-arrowreturn-1-e" },
                {title: "----"},
                ],
            beforeOpen: function(event, ui) {
                var node = $.ui.fancytree.getNode(ui.target);
                //$("#regions").contextmenu("enableEntry", "paste", !!CLIPBOARD);
                node.setActive();
            },
            select: function(event, ui) {
                var that = this;
                // delay the event, so the menu can close and the click event does
                // not interfere with the edit control
                setTimeout(function(){
                    $(that).trigger("nodeCommand", {cmd: ui.cmd});
                }, 100);
            }
	    });

        $("#save").on("click", function(e) {
            tree = $("#regions").fancytree("getTree");

            node = function(region) {
            var data = {
                    'id': region.title
            }
            
            if (undefined != region.data.timezone) {
                data['timezone'] = region.data.timezone
            }

            if (undefined != region.data.description) {
                data['description'] = region.data.description
            }

            if (null != region.children) {
                var children = []
                
                for (var i = 0; i < region.children.length; i++) {
                    childData = node(region.children[i]);
                    children.push(childData);
                }

                data['children'] = children
            }

            return data;
            }

            var data = node(tree.rootNode);
            $.ajax({
                type: 'POST',
                url: '{{ url_for(request.endpoint) }}',
                data: JSON.stringify(data),
                contentType: "application/json",
                success: function() {
                    window.location.reload()
                }
            })
        })

    </script>
{% endblock %}


{% block body %}
    
    <section class="content-header">
        <h1>Regions</h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-xs-12">
                <div class="box box-primary">
                    <div class="box-body">
                        <table id="regions" class="table">

                            <thead>
                                <th></th>
                                <th>Timezone</th>
                                <th>Description</th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td></td>
                                    <td>
                                        <input name="timezone"  class="editnode form-control" type="number">
                                    </td>
                                    <td><textarea name="description" class="editnode form-control"></textarea></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="box-footer">
                        <button id="save" class="btn btn-primary">Save</button>
                        <button id="save" class="btn btn-default pull-right">Download JSON</button>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}
