{% extends 'admin/model/cms_list.html' %}


{% set disable_group_operations = True %}


{% block head_css %}
    {{ super() }}

    <style type="text/css">

        input.action-checkbox, input.action-rowtoggle {
            cursor: pointer;
        }

        .box-header .box-title .btn-primary {
            display: none;
        }

        .box-tools .btn[title="Export"] {
            display: none;
        }

        button#with-selected, button#with-selected+button, button#with-selected+ul.dropdown-menu {
            display: none;
        }

        section.content-header {
            display: none;
        }

        .box {
            box-shadow: none;
        }

        div[id$="_preview"] .box-body {
            overflow: hidden !important;
        }

        .model-list th:nth-child(2), .model-list td:nth-child(2) {
            display: none;
        }

    </style>
{% endblock %}


{% block header %}
{% endblock %}


{% block breadcrumb %}
{% endblock %}


{% block page_body %}

    {% block body %}
        <div class="row">
            <div class="col-xs-6">
                {{ super() }}
            </div>
            
            <div class="col-xs-6">
                <section class="content">
                    {% with name = 'multiple_models', prefix = 'select_multiple_models', model_title = admin_view.name %}
                        <div id="multiple_models_preview">{% include 'common/preview_table.html' %}</div>
                    {% endwith %}
                </section>
            </div>
        </div>
    {% endblock %}

{% endblock %}


{% block tail_js %}
    {{ super() }}

    <script src="https://cdn.rawgit.com/mgalante/jquery.redirect/master/jquery.redirect.js"></script>

    <script>
        (function($) {
            let get_models = function() {
                return viewStorage.getItem('multiple_models', 'select_multiple_models') ? viewStorage.getItem('multiple_models', 'select_multiple_models').split(',') : [];
            }

            let set_models = function(models) {
                viewStorage.setItem('multiple_models', [...new Set([...models, ...get_models()])], false, 'select_multiple_models');

            }

            addEventListener('storage', function (e) {
                if (e.key === '{{admin_view.endpoint}}-multiple_models') {
                    if (!viewStorage.getItem('multiple_models', 'select_multiple_models')) {
                        $('input.action-checkbox, input.action-rowtoggle').prop('checked', false);
                    }
                }
            });

            $("input.action-checkbox[value='" + get_models().join("'],[value='") + "']").prop( "checked", true);

            if ($("input.action-checkbox:checked").length == $("input.action-checkbox").length) {
                $('input.action-rowtoggle').prop("checked", true);
            }

            $('#filter_form>div a').replaceTag('<button>', true);
            $('#filter_form').attr('action', '{{ url_for(".index_view") }}?select-multiple-models=1');
            $('#filter_form').append('<input type="hidden" name="search" value="{{search or ''}}" />');
            $('#filter_form').append('<input type="hidden" name="select-multiple-models" value=1 />');

            $('form[role="search"] a').replaceTag('<button>', true);
            $('form[role="search"]').attr('action', '{{ url_for(".index_view") }}?select-multiple-models=1');
            $('form[role="search"] div').append('<input type="hidden" name="select-multiple-models" value=1 />');

            $('#filter_form').submit(function(event) {
                if ($(this).data('clicked').text() === 'Reset Filters') {
                    $(this).find('table.filters').html('');
                    return true;
                }
            });

            $('form[role="search"]').submit(function(event) {
                $(this).append($('table.filters').clone().hide());

                if ($(this).data('clicked').prop('tagName') === 'I') {
                    $(this).find('input[name="search"]').val('');
                    return true;
                }
            });

            $('input.action-rowtoggle').change(function(event) {
                let selected_models = $('input.action-checkbox:not(.dummy)', $('table.model-list')).map(function() {
                    return $(this).val();
                }).get();

                if (event.target.checked) {
                    set_models(selected_models);
                } else {
                    viewStorage.setItem('multiple_models', get_models().filter(function(value) {
                        return !selected_models.includes(value)
                    }), false, 'select_multiple_models');
                }
            });

            $('input.action-checkbox').change(function(event) {
                let selected_models = $('input.action-checkbox:not(.dummy):checked', $('table.model-list')).map(function() {
                    return $(this).val();
                }).get();

                if (!event.target.checked) {
                    viewStorage.setItem('multiple_models', get_models().filter(function(value) {
                        return value !== event.target.value
                    }), false, 'select_multiple_models');
                }

                set_models(selected_models);
            });
        })(jQuery);
    </script>

    {% with name = 'multiple_models', prefix = 'select_multiple_models', model_title = admin_view.name %}
        {% include 'common/preview_js.html' %}
    {% endwith %}
    
{% endblock %}
