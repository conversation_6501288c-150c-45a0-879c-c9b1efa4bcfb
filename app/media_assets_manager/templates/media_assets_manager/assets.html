{% extends 'admin/model/cms_edit.html' %}

{% import 'admin/lib.html' as lib with context %}
{% import 'admin/actions.html' as action_lib with context %}
{% import 'admin/select_utils.html' as select_utils with context %}
{% import 'media_assets_manager/utils.html' as utils with context %}


{% block  body %}
    <section class="content-header">
        <h1>{{ _("Media Assets") }}</h1>
    </section>

    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header" style="height: 40px">
                        <div class="box-title">
                            <a class="btn btn-primary" href="{{ url_for(".create") }}" title="{{ _("Create") }}">
                                <i class="fa fa-plus-circle"></i>
                                {{ _("Create") }}
                            </a>
                        </div> 
                        <div class="box-tools">
                            {{ action_lib.dropdown(actions) }}

                            <form method="GET" action="{{url_for(".index_view")}}" class="btn-group" role="search" novalidate="1">
                                <div class="input-group pull-right" style="width: 150px">
                                    <input type="text" name="search" value="{{search}}" class="col-md-2 form-control input-xs pull-right" placeholder={{ _("Search") }}>
                                    {% if search %}
                                        <div class="input-group-btn">
                                            <a href="{{url_for(".index_view")}}" class="btn btn-default">
                                                <i class="fa fa-times glyphicon glyphicon-remove"></i>
                                            </a>
                                        </div>
                                    {% endif %}
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="box-body">
                        {% if service_error %}
                            <p class="text-muted text-center" style="color: red">{{ _("Failed to access media assets controller service") }}</p>
                        {% elif media_assets|length > 0 %}
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th class="list-checkbox-column">
                                            <input type="checkbox" name="rowtoggle" class="action-rowtoggle" title="{{ _("Check All") }}">
                                        </th>
                                        <th></th>
                                        <th>
                                            <span class="fa fa-trash" style="padding: 1px" title="{{ _("Deleted") }}"></span>
                                        </th>
                                        <th>{{ _("Id") }}</th>
                                        <th>{{ _("Description") }}</th>
                                        <th>{{ _("Origin") }}</th>
                                        <th>{{ _("Last Job") }}</th>
                                        <th>{{ _("Created") }}</th>
                                        <th>{{ _("Updated") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for a in media_assets %}
                                        <tr>
                                            <td style="width: 30px">
                                                <input type="checkbox" name="rowid" class="action-checkbox" value="{{ a.id }}" title="{{ _("Select") }}">
                                            </td>
                                            <td style="width: 95px">
                                                {% if not a.is_deleted %}
                                                    <a class="btn btn-primary btn-xs" style="float:left; margin-top: 1px; margin-right: 5px;" href="{{ url_for(".update", id=a.id) }}" title="{{ _("Edit") }}">
                                                        <span class="fa fa-pencil"></span>
                                                    </a>
                                                    <form class="icon" method="POST" action="{{ url_for(".retranscode") }}" novalidate="1" style="padding: 0; display: inline; margin-right: 3px;">
                                                        <input id="ids" name="ids" type="hidden" onclick="return confirm('Are you sure you want to retranscode the entry?');" value="{{ a.id }}">
                                                        <button class="btn btn-warning btn-xs" title="{{ _("Retranscode") }}">
                                                            <span class="fa fa-refresh"></span>
                                                        </button>
                                                    </form>
                                                {% endif %}
                                                <form class="icon" method="POST" action="{{ url_for(".delete") }}" novalidate="1" style="padding: 0; display: inline">
                                                    <input id="ids" name="ids" type="hidden" value="{{ a.id }}">
                                                    <button class="btn btn-danger btn-xs" onclick="return confirm('Are you sure you want to delete the entry?');" title="{{ _("Delete") }}">
                                                        <span class="fa fa-trash"></span>
                                                    </button>
                                                </form>
                                            </td>
                                            <td style="width: 30px">
                                                {% if a.is_deleted %}
                                                    <span class="fa fa-check-circle"></span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{a.id}}
                                            </td>
                                            <td>
                                                {{a.description}}
                                            </td>
                                            <td>
                                                {{ utils.render_origin_type(a.origin) }}
                                                <div class="pull-right">
                                                    {{ utils.render_origin(a.origin) }}
                                                </div>
                                            </td>
                                            <td>
                                                {{ utils.render_job_type(a.last_job) }}
                                                {{ utils.render_job_status(a.last_job) }}
                                                <div class="pull-right" style="display: flex; align-items: center;">
                                                    {{ utils.render_job(a.last_job) }}
                                                    <a href="{{ url_for("media_asset_job.index_view", media_asset_id=a.id) }}" title="{{ _("All Jobs") }}" style="margin-left: 5px; margin-top: 2px">
                                                        <i class="fa fa-list"></i>
                                                    </a>
                                                </div>
                                            </td>
                                            <td>
                                                {{ a.created_at|localize_datetime }}
                                            </td>
                                            <td>
                                                {{ a.updated_at|localize_datetime }}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        {% else %}
                            <p class="text-muted text-center">{{ _("No media assets found") }}</p>
                        {% endif %}
                    </div>
                    {{ utils.pager(lib.pager, pager_url, page_token, pagination, media_assets) }}
                </div>
            </div>
        </div>

        {{ action_lib.form(actions, get_url('.action_view', url=get_url('.index_view'))) }}
        {{ select_utils.select_preview_modal() }}
    </section>
{% endblock %}


{% block tail_js %}

    {{ super() }}

    {{ action_lib.script(_gettext('Please select at least one record.'), actions, actions_confirmation) }}

    {{ select_utils.shift_select_js() }}
    {{ select_utils.select_preview_js(admin_view.endpoint, url_for(".preview_selected_items", total_count=pagination.total), enabled=True) }}

    <script>

        addEventListener('storage', function (e) {
            if (e.key === '{{admin_view.endpoint}}-media-assets') {
                viewStorage.setItem('selected_items', viewStorage.getItem("media-assets"))
            }
        });

    </script>

{% endblock %}

