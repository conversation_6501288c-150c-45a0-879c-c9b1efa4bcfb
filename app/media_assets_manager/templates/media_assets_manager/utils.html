{% set JOB_STATUS_TEXT = {0: _("Unspecified"), 1: _("Pending"), 2: _("In Progress"), 3: _("Success"), 4: _("Fail")} %}
{% set JOB_STATUS_COLOR = {0: "bg-default", 1: "bg-yellow", 1: "bg-yellow", 3: "bg-green", 4: "bg-red"} %}

{% set JOB_TYPE_TEXT = {0: _("Unspecified"), 1: _("Transcode"), 2: _("Delete")} %}
{% set JOB_TYPE_COLOR = {0: "bg-default", 1: "bg-default", 4: "bg-default"} %}

{% set ORIGIN_TYPE_TEXT = {0: _("Unspecified"), 1: _("Local Storage"), 2: _("External Storage")} %}
{% set ORIGIN_TYPE_COLOR = {0: "bg-default", 1: "bg-default", 2: "bg-default"} %}


{% macro render_job_status(job) %}
    {% if job.status %}
        <div style="display: inline">
            <span class="badge {{JOB_STATUS_COLOR.get(job.status)}}" title="{{ _("Job Status") }}">
                {{JOB_STATUS_TEXT.get(job.status)}}
            </span>
        </div>
    {% endif %}
{% endmacro %}


{% macro render_job_type(job) %}
    {% if job.type %}
        <div style="display: inline">
            <span class="badge {{JOB_TYPE_COLOR.get(job.type)}}" title="{{ _("Job Type") }}">
                {{JOB_TYPE_TEXT.get(job.type)}}
            </span>
        </div>
    {% endif %}
{% endmacro %}


{% macro render_origin_type(origin) %}
    {% if origin.type %}
        <div style="display: inline">
            <span class="badge {{ORIGIN_TYPE_COLOR.get(origin.type)}}" title="{{ _("Origin Url") }}">
                {{ORIGIN_TYPE_TEXT.get(origin.type)}}
            </span>
        </div>
    {% endif %}
{% endmacro %}


{% macro render_transcode_data(job) %}
    {% set d = job.transcode_data %}

    <h4>{{ _("Transcode Data") }}:</h4>
    <ul class="list-unstyled">
        <li><b>{{ _("Origin") }}</b>: {{ render_origin(d.origin) }}</li>
        <li><b>{{ _("Profile") }}</b>: {{ d.profile }}</li>
        <li><b>{{ _("Download Path") }}</b>: {{ d.download_path }}</li>
        <li><b>{{ _("Result Path") }}</b>: {{ d.result_path }}</li>
    </ul>
{% endmacro %}


{% macro render_logs(job) %}
    <h4>{{ _("Logs") }}:</h4>
    <ul class="list-unstyled">
        {% for l in job.logs %}
            <li><b>{{l.created_at|localize_datetime }}</b>: {{ l.message }}</li>
        {% endfor %}
    </ul>
{% endmacro %}


{% macro render_job(job) %}
    {% set dlg_id = "dlg-job-" + job.id %}
    {% set dlg_title = [_("Job"), job.id, _("Details")]|join(" ") %}

    <a href="#" data-toggle="modal" data-target="#{{dlg_id}}" title="{{dlg_title}}">
        <i class="fa fa-eye"></i>
    </a>
    <div id="{{dlg_id}}" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" style="display: inline;">
                        {{ dlg_title }}
                    </h4>
                    <div class="pull-right" style="display: inline; margin-right: 10px">
                        {{ render_job_type(job) }} {{ render_job_status(job) }}
                    </div>
                </div>
                <div class="modal-body">
                    {{ render_transcode_data(job) }}
                    {{ render_logs(job) }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}


{% macro render_packaging_results(id, r) %}
    {% set dlg_id = "dlg-job-" + id %}
    {% set dlg_title = [_("Transcoding Results"), id]|join(" ") %}

    <a href="#" data-toggle="modal" data-target="#{{dlg_id}}" title="{{dlg_title}}">
        <i class="fa fa-eye"></i>
    </a>
    <div id="{{dlg_id}}" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" style="display: inline;">
                        {{ dlg_title }}
                    </h4>
                </div>
                <div class="modal-body">
                    <table class="table table-bordered table-hover dataTable small-font" role="grid">
                        <tr>
                            <th>{{ _("Width") }}</th>
                            <th>{{ _("Hight") }}</th>
                            <th>{{ _("URLs") }}</th>
                        </tr>
                        {% for i in r.playback_sizes %}
                            <tr role="row">
                                <td>{{ i.width }}</td>
                                <td>{{ i.height }}</td>
                                <td>
                                    <div>{{i.url_map.abr}}</div>
                                    <div>{{i.url_map.sbr}}</div>
                                </td>
                            </tr>
                        {% endfor %}
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}


{% macro render_packaging_results_table(packaging_results, media_asset_id=None) %}
    {% if packaging_results %}
        <table class="table table-bordered table-hover" role="grid">
            <tr>
                <th>ID</th>
                {% if not media_asset_id %}
                    <th>{{ _("Media Asset") }}</th>
                {% endif %}
                <th>{{ _("Job") }}</th>
                <th>{{ _("Path") }}</th>
                <th>{{ _("Transcoding") }}</th>
                <th>{{ _("Origin") }}</th>
                <th>{{ _("Results") }}</th>
                <th>{{ _("Created") }}</th>
                <th>{{ _("Expires") }}</th>
                <th>{{ _("Deleted") }}</th>
            </tr>
            {% for r in packaging_results %}
                <tr role="row">
                    <td>{{ r.id }}</td>
                    {% if not media_asset_id %}
                        <td>
                            <a href="{{ url_for("media_asset.update", id=r.media_asset_id) }}">{{ r.media_asset_id }}</a>
                        </td>
                    {% endif %}
                    <td>{{ r.job_id }}</td>
                    <td>{{ r.download_path }}</td>
                    <td>{{ r.transcoding_params }}</td>
                    <td>{{ render_origin(r.origin_params) }}</td>
                    <td>{{ render_packaging_results(r.id, r.results) }}</td>
                    <td>{{ r.created_at|localize_datetime }}</td>
                    <td>{{ r.expires_in.seconds }}</td>
                    <td>{{ r.deleted }}</td>
                </tr>
            {% endfor %}
        </table>
    {% else %}
        <p class="text-muted text-center">{{ _("No results found") }}</p>
    {% endif %}
{% endmacro %}


{% macro render_origin(origin) %}
    {% set dlg_id = "dlg-asset-" + range(1, 1000)|random|string %}
    {% set dlg_title = _("Origin Details") %}

    <a href="#" data-toggle="modal" data-target="#{{dlg_id}}" title="{{dlg_title}}">
        <i class="fa fa-eye"></i>
    </a>
    <div id="{{dlg_id}}" class="modal fade" tabindex="-1" role="dialog" style="z-index: 10001">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" style="display: inline;">{{ dlg_title }}</h4>
                    <div class="pull-right" style="display: inline; margin-right: 10px">
                        {{ render_origin_type(origin) }}
                    </div>
                </div>
                <div class="modal-body">
                    <ul class="list-unstyled">
                        {% if origin.url %}
                            <li>
                                <b>{{ _("Url") }}</b>:
                                <a href="{{ origin.url }}" title="{{ _("Origin Url") }}">{{ origin.url }}</a>
                            </li>
                        {% endif %}
                        <li><b>{{ _("Start Pos") }}</b>: {{ origin.start_pos|format_duration }}</li>
                        <li><b>{{ _("End Pos") }}</b>: {{ origin.end_pos|format_duration }}</li>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}


{% macro pager(pager_func, pager_url, page_token, pagination, items) %}
    {% if pagination.next_page_token %}
        {% set p = pagination %}

        <div class="box-footer">
            {{ _("Showing") }} {{ page_token * p.page_size + 1 }} {{ _("to") }} {{ page_token * p.page_size + items|length }} {{ _("of") }} {{ p.total }} {{ _("entires") }} 

            {{ pager_func(page_token or 0, p.total // p.page_size + ((p.total % p.page_size) > 0)|int, pager_url) }}
        </div>
    {% endif %}
{% endmacro %}
