{% extends 'admin/model/cms_edit.html' %}
{% import 'admin/lib.html' as lib with context %}
{% import 'media_assets_manager/utils.html' as utils with context %}


{% block  body %}
    <section class="content-header">
        <h1>{% if media_asset_id %}{{ _("Media Asset") }} {{media_asset_id}} {% endif %}{{ _("Jobs") }}</h1>
    </section>

    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-body">
                        {% if service_error %}
                            <p class="text-muted text-center" style="color: red">{{ _("Failed to access media assets controller service") }}</p>
                        {% elif jobs|length > 0 %}
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>{{ _("Id") }}</th>
                                        {% if not media_asset_id %}
                                            <th>{{ _("Media Asset") }}</th>
                                        {% endif %}
                                        <th>{{ _("Status") }}</th>
                                        <th>{{ _("Type") }}</th>
                                        <th>{{ _("Created") }}</th>
                                         <th>{{ _("Updated") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for j in jobs %}
                                        <tr>
                                            <td style="width: 30px">
                                                {{ utils.render_job(j) }}
                                            </td>
                                            <td>
                                                {{ j.id }}
                                            </td>
                                            {% if not media_asset_id %}
                                                <td>
                                                    <a href="{{ url_for("media_asset.update", id=j.media_asset_id) }}">{{ j.media_asset_id }}</a>
                                                </td>
                                            {% endif %}
                                            <td>
                                                {{ utils.render_job_status(j) }}
                                            </td>
                                            <td>
                                                {{ utils.render_job_type(j) }}
                                            </td>
                                            <td>
                                                {{ j.created_at|localize_datetime }}
                                            </td>
                                            <td>
                                                {{ j.updated_at|localize_datetime }}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        {% else %}
                            <p class="text-muted text-center">{{ _("No jobs found") }}</p>
                        {% endif %}
                    </div>
                    {{ utils.pager(lib.pager, pager_url, page_token, pagination, jobs) }}
                </div>
            </div>
        </div>
    </section>
{% endblock %}
