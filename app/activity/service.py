from datetime import datetime
from typing import Optional

from flask import (
    g,
    request,
)
from flask_security import current_user
from mongoengine import Document
from mongoengine.base import BaseDocument
from werkzeug.local import LocalProxy

from .models import (
    BaseUserAction,
    ModelChangedUserAction,
    ModelDeletedUserAction,
)
from .signals import user_did_action

_CREATED_MODEL_KEY = "activity-created-model"
_USER_ACTION_EVENT_KEY = "activity-user-action-event"


def _get_created_model() -> Optional[BaseDocument]:
    return getattr(g, _CREATED_MODEL_KEY, None)


def set_created_model(model: BaseDocument):
    setattr(g, _CREATED_MODEL_KEY, model)


created_model = LocalProxy(_get_created_model)


class BaseUserActionEvent:
    """Represents a basic action a user did."""

    model = BaseUserAction

    def __init__(self, description: str = None, user=None):
        from app.security.models import ProtoUser

        self.description = description
        self.time = datetime.utcnow()

        user = current_user or user
        if isinstance(user, ProtoUser):
            self.user = user

        self.request = request
        self.remote_ip = request.headers.get("X-Remote-Addr") or request.headers.get("X-Real-IP") or request.remote_addr
        self.endpoint = request.endpoint
        self.request_args = dict(request.args)

    def create_document(self) -> Document:
        document = self.model(
            remote_ip=self.remote_ip,
            endpoint=self.endpoint,
            request_args=self.request_args,
            description=self.description,
        )

        if getattr(self, "user", None):
            document.user = self.user

        parts = self.endpoint.split(".")
        if len(parts) > 1:
            document.view = parts[0]
            document.view_method = parts[1]

        return document

    def save_model(self) -> None:
        self.create_document().save()


class ModelUserActionEvent(BaseUserActionEvent):
    """Represents and action on a model."""

    model = ModelChangedUserAction

    def __init__(self, document: Document, is_created: bool) -> None:
        super().__init__()

        self.document = document
        self.is_created = is_created

    def create_document(self) -> Document:
        doc = super().create_document()
        doc.document = self.document
        doc.is_created = self.is_created

        return doc


class ModelDeletedActionEvent(BaseUserActionEvent):
    """Represents delete action on a model."""

    model = ModelDeletedUserAction

    def __init__(self, document: Document):
        super().__init__()

        self.document = document

    def create_document(self) -> Document:
        doc: ModelDeletedUserAction = super().create_document()
        doc.document_class = self.document.__class__.__name__
        doc.document_id = str(self.document.id)

        return doc


def _get_user_action_event() -> Optional[BaseUserActionEvent]:
    return getattr(g, _USER_ACTION_EVENT_KEY, None)


def set_user_action_event(event: BaseUserActionEvent) -> None:
    if not g:
        return
    setattr(g, _USER_ACTION_EVENT_KEY, event)


user_action_event = LocalProxy(_get_user_action_event)


class UserActivityService:
    """Sends events on user activity."""

    def register(self, event: BaseUserActionEvent, **kwargs):
        """Send signal on `user_did_action`."""
        user_did_action.send(event, **kwargs)

    def set_created_model(self, model: BaseDocument) -> None:
        """Sets created model in flask globals."""
        set_created_model(model)

    def set_user_action_event(self, event: BaseUserActionEvent) -> None:
        """Sets user action event in flask globals."""
        set_user_action_event(event)


@user_did_action.connect
def user_did_action_handler(event: BaseUserActionEvent):
    """Handles user_did_action` signal."""
    event.save_model()


def init_app(app):
    app.user_activity = UserActivityService()
