import logging
from datetime import (
    datetime,
    timedelta,
)

from flask import current_app

from .tasks import clear_actions as clear_actions_func

celery = current_app.extensions["celery"]


logger = logging.getLogger(__name__)


@celery.task(bind=True)
def clear_actions(self):
    ttl = timedelta(**current_app.config["ACTIVITY_ACTIONS_TTL"])
    older_than = datetime.now() - ttl
    deleted_count = clear_actions_func(older_than)
    logger.debug(f"Deleted {deleted_count} documents")
