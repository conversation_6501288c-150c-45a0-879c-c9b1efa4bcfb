<span class="time"><i class="fa fa-clock-o"></i> {{ action.time|localize_time }}</span>

<h3 class="timeline-header">
    {% block header %}
        {{action.endpoint}} from {{action.remote_ip}}
    {% endblock %}
    {% if request and request.args.any_user and action.user.id %}
        by
        <a href="{{ url_for("user_activity.timeline", user_id=action.user.id) }}" title="{{ _("Check activity") }}">
            {{ action.user.name or action.user.login or action.user }}
        </a>
    {% endif %}
</h3>

<div class="timeline-body">
    {% block body %}
        {{ action.description }}
    {% endblock %}
</div>

<div class="timeline-footer">
    {% block footer %}
        <a class="btn btn-primary btn-xs" target="_blank" href="{{ url_for(action.endpoint, **(action.args or {})) }}">View</a>
    {% endblock %}

    {% block footer_raw %}
    <a type="button" class="btn btn-default btn-xs" data-toggle="modal" data-target="#modal-{{action.pk}}">
        View raw action data
    </a>

    <div class="modal fade in" id="modal-{{ action.pk }}">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span></button>
                    </div>
                <div class="modal-body">
                    <pre>
                    {{-action.to_json(sort_keys=True, indent=4, separators=(",", ": "))}}
                    </pre>
                </div>
            </div>
        </div>
        
    </div>
    {% endblock %}
</div>
