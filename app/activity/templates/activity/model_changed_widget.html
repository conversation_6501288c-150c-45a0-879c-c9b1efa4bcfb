{% extends 'activity/base_widget.html' %}

{% set document = action.get_document() %}

{% if document %}
    {% set document_url = url_for(action.get_endpoint_for_document(), id=action.document.id) %}
    {% set is_ref = document|lazy_reference %}
{% endif %}

{% block header %}
    {% if action.is_created %}
        Document has been created
    {% else %}
        Document has been changed
    {% endif %}
    from {{action.remote_ip}}
{% endblock %}

{% block body %}
    {% if document and not is_ref %}
        <a href="{{ document_url }}">
            {{ document }}
        </a>
    {% elif document %}
        Deleted {{ document.document_type.__name__ }} {{ document.id }}
    {% else %}
        Document model is non-existent anymore
    {% endif %}
{% endblock %}

