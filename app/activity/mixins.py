from .service import (
    UserActivityService,
    user_action_event,
)


class UserActivityMixin:
    """Mixing for registering POST request as a user activity."""

    def create_blueprint(self, admin):
        blueprint = super().create_blueprint(admin)
        admin.app.after_request_funcs.setdefault(blueprint.name, []).append(self.register_user_action)
        return blueprint

    def make_user_action_event(self, response):
        return None
        # if request.method in "POST":
        #     return BaseUserActionEvent()

    def register_user_action(self, response):
        user_action_service: UserActivityService = getattr(self.admin.app, "user_activity", None)
        if not user_action_service:
            return response

        if response.status_code in (200, 302):
            event = user_action_event or self.make_user_action_event(response)

            if event:
                user_action_service.register(event)

        return response
