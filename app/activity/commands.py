from datetime import (
    datetime,
    timedelta,
)
from typing import Optional

import click
from flask import current_app
from flask.cli import AppGroup

from .tasks import clear_actions as clear_actions_func

activity_commands = AppGroup("activity")


@activity_commands.command("clear-actions")
@click.option("--older-than", type=click.DateTime(formats=["%Y-%m-%d"]), default=None)
def clear_actions(older_than: Optional[datetime]):
    """Clears activity actions older that the given time."""
    if not older_than:
        ttl = timedelta(**current_app.config["ACTIVITY_ACTIONS_TTL"])
        older_than = datetime.now() - ttl

    print(older_than)

    deleted_count = clear_actions_func(older_than)

    click.echo(f"Deleted {deleted_count} docuemnts")
