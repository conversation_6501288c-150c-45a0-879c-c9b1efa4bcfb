from typing import (
    Any,
    Callable,
    Type,
)

from flask import render_template
from mongoengine import Document

from .models import (
    BaseUserAction,
    ModelChangedUserAction,
    ModelDeletedUserAction,
)


class BaseActionWidget:
    template = "activity/base_widget.html"

    def __call__(self, model, *args: Any, **kwds: Any) -> str:
        return render_template(self.template, action=model)


_widgets = {"__base_widget__": BaseActionWidget}

widget_type = Callable[[BaseUserAction, Any, Any], str]


def add_widget_for_model(model: Type[Document], widget: widget_type) -> None:
    _widgets[model.__qualname__] = widget


def get_widget_for_document(doc: Document) -> widget_type:
    widget = _widgets.get(type(doc).__qualname__)
    if not widget:
        widget = _widgets["__base_widget__"]

    return widget()


def action_widget(model: Type[Document]):
    def inner(func):
        add_widget_for_model(model, func)
        return func

    return inner


@action_widget(ModelChangedUserAction)
class ModelChangedActionWidget(BaseActionWidget):
    template = "activity/model_changed_widget.html"


@action_widget(ModelDeletedUserAction)
class ModelDeletedActionWidget(BaseActionWidget):
    template = "activity/model_deleted_widget.html"
