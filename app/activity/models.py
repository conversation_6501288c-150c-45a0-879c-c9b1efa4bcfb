from datetime import datetime

from flask_mongoengine import Document
from mongoengine.errors import (
    DoesNotExist,
    NotRegistered,
)
from mongoengine.fields import (
    <PERSON><PERSON>anField,
    DateTimeField,
    DictField,
    GenericLazyReferenceField,
    ReferenceField,
    StringField,
)

from app.security.models import ProtoUser


class BaseUserAction(Document):
    """Represents a user action in the database."""

    meta = {
        "db_alias": "cms",
        "allow_inheritance": True,
        "strict": False,
        "collection": "user_actions",
        "indexes": [{"fields": ["user", "-time"]}, "-time"],
    }

    user = ReferenceField(ProtoUser, dbref=True, help_text="User that made an action")
    remote_ip = StringField(max_length=15, help_text="Remote IP from which an action was made")
    time = DateTimeField(default=datetime.utcnow, help_text="Time of an action")
    request_args = DictField(help_text="Arguments of request an action was made with")
    endpoint = StringField(help_text="Endpoint and action comes from")
    description = StringField(help_text="Custom description of an action")
    view = StringField(help_text="View name action was triggered from")
    view_method = StringField(help_text="Method an resulted to an action")


class ModelChangedUserAction(BaseUserAction):
    """Represents a regular action on models."""

    document = GenericLazyReferenceField(help="Reference to document that was changed")
    is_created = BooleanField(help="True if a document was created as a result of an action")

    def get_endpoint_for_document(self) -> str:
        """Returns endpoint to changed document."""
        endpoint = self.endpoint.replace("create_view", "edit_view")
        return endpoint

    def get_document(self):
        try:
            doc = self.document.fetch()
        except DoesNotExist:
            doc = self.document
        except NotRegistered:
            return
        return doc


class ModelDeletedUserAction(BaseUserAction):
    """Represents delete action."""

    document_class = StringField(help="Class of deleted document")
    document_id = StringField(help="Id of deleted document")
