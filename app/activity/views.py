import re
from datetime import datetime
from typing import OrderedDict

from flask import (
    abort,
    current_app,
    flash,
    jsonify,
    request,
)
from flask_admin.form.widgets import Select2Widget
from flask_admin.model.fields import AjaxSelect<PERSON><PERSON>iple<PERSON>ield
from flask_security import current_user
from markupsafe import Markup
from wtforms import Form
from wtforms.fields import Select<PERSON><PERSON><PERSON><PERSON><PERSON>ield
from wtforms.widgets import html_params

from app.activity.models import BaseUserAction
from app.auth import expose
from app.common.fields import TZ<PERSON><PERSON>Date<PERSON><PERSON><PERSON>ield
from app.common.widgets import AjaxSelect2Widget
from app.utils import (
    get_view,
    sanitize_date,
)
from cmf.core.view import BaseView

from .widgets import get_widget_for_document

user_datastore = current_app.extensions["user_datastore"]

user_loader = user_datastore.user_model.get_ajax_loader()

app_views = current_app.extensions["views"]


class UserActionView(BaseView):

    timeline_template = "security/user_actions_timeline.html"

    extra_permissions = {"all_users_activity"}

    column_default_sort = "-time"

    can_create = False
    can_delete = False
    can_edit = False

    can_view_details = False

    form_ajax_refs = {"user": user_loader}

    page_size = 10

    def make_user_action_event(self, response):
        return

    def filters_form(self, user, start, end, views, endpoints):
        def make_choice(view):
            category = re.compile(r"<.*?>").sub("", str(view.category or "Other")).strip()
            return view.endpoint, f"{category}$${view.name}"

        class ViewsWidget(Select2Widget):
            def __call__(self, field, **kwargs):
                kwargs.setdefault("id", field.id)
                kwargs["multiple"] = True

                html = ["<select %s>" % html_params(name=field.name, **kwargs)]

                cat = None
                for endpoint, label, selected in field.iter_choices():
                    category, name = label.split("$$")

                    if category != cat:
                        if not cat:
                            html.append("</optgroup>")

                        html.append(f'<optgroup label="{category}">')
                        cat = category

                    html.append(self.render_option(endpoint, name, selected))

                html.append("</select>")
                return Markup("".join(html))

        class FiltersForm(Form):
            start = TZAwareDateTimeField()
            end = TZAwareDateTimeField()

            user = AjaxSelectMultipleField(widget=AjaxSelect2Widget(multiple=False), loader=user_loader)

            views = SelectMultipleField(
                choices=sorted([make_choice(v) for v in app_views if v.is_visible], key=lambda c: c[1]),
                widget=ViewsWidget(multiple=True),
                render_kw={"data-placeholder": "Views"},
            )

            endpoints = SelectMultipleField(
                choices=[], widget=Select2Widget(multiple=True), render_kw={"data-placeholder": "Endpoints"}
            )

        return FiltersForm(user=user.id if user else None, views=views, endpoints=endpoints, start=start, end=end)

    @expose("/")
    def index_view(self):
        return self.timeline()

    @expose("/timeline", perm="timeline", methods=("GET", "POST"))
    def timeline(self):
        any_user = False
        user_id = None
        user = None

        if current_user.is_allowed("user_activity", "all_users_activity"):
            user_id = request.values.get("user")

            if not user_id:
                any_user = request.values.get("any_user")

        if user_id:
            user = user_datastore.user_model.objects(id=user_id).first()
        elif not any_user:
            user = current_user

        if not any_user and not user:
            abort(404)

        start = request.values.get("start")
        end = request.values.get("end")

        views = request.values.getlist("views")
        endpoints = request.values.getlist("endpoints")

        request_args_id = request.values.get("request_args_id")

        query = {"user": user} if user else {}

        if request_args_id:
            query["request_args__id"] = request_args_id

        if start or end:
            if start:
                start = sanitize_date(start)
                query["time__gte"] = start

            if end:
                end = sanitize_date(end)
                query["time__lte"] = end

        if start and end and start > end:
            flash("Start date is greater than the end one", "warning")

        if views:
            query["view__in"] = views

        if endpoints:
            query["view_method__in"] = endpoints

        more = False
        page = request.values.get("page", 0, int)

        if "prev" in request.values:
            page -= 1

        if "next" in request.values:
            page += 1

        frm = page * self.page_size

        actions = BaseUserAction.objects(**query).order_by("-time").skip(frm).limit(self.page_size + 1)

        actions = list(actions)
        more = len(actions) > self.page_size

        timeline = OrderedDict()
        for action in actions:
            day_actions = timeline.setdefault(
                datetime(action.time.year, action.time.month, action.time.day, tzinfo=action.time.tzinfo), []
            )
            day_actions.append(action)

        def handle_catch(caller, on_exception):
            try:
                return caller()
            except Exception:
                return on_exception

        filter_params = dict(user=user, start=start, end=end, views=views, endpoints=endpoints)

        return self.render(
            self.timeline_template,
            timeline=timeline,
            page=page,
            more=more,
            frm=frm,
            dt=datetime,
            user_id=user_id,
            filtered=request.method == "POST",
            filters_form=self.filters_form(**filter_params),
            handle_catch=handle_catch,
            get_view=get_view,
            get_widget_for_document=get_widget_for_document,
            **filter_params,
        )

    @expose("/view_endpoints", perm="timeline", methods=("GET", "POST"))
    def view_endpoints(self):
        search = request.args.get("search") or ""
        views = request.args.getlist("views[]")

        endpoints = set()

        for view in app_views:
            if view.endpoint in views:
                for rule in current_app.url_map.iter_rules():
                    bp = rule.endpoint.split(".")[0]

                    if bp == view.blueprint.name:
                        ep = rule.endpoint.split(".")[1]

                        if search.lower() in ep and not ep.startswith("ajax_") and ep != "index":
                            if getattr(current_app.view_functions[rule.endpoint], "_urls", False):
                                endpoints.add(ep)

        return jsonify({"results": [{"id": e, "text": e} for e in sorted(endpoints)]})
