{% extends 'admin/model/cms_list.html' %}


{% block head_css %}
    {{ super() }}

    <style>
        a[title='{{ _("Create new record") }}'] {
            display: none;
        }
    </style>
{% endblock %}


{% block list_row_actions scoped %}
    {% set endpoint = row.endpoint or "" %}
    {% if endpoint %}
        <a class="btn btn-success btn-xs" href="{{ url_for(endpoint + '.edit_view', id=row.entity_id, url=return_url) }}" title="{{ _('To Commented Entity') }}" style="float:left; margin-right:5px;">
           <span class="fa fa-external-link glyphicon glyphicon-external-link"></span>
        </a>
    {% endif %}

    {% if row.author == current_user %}
        <a class="btn btn-primary btn-xs" href="{{ get_url('.edit_view', id=get_pk_value(row), url=return_url) }}" title="{{ _('Edit record') }}" style="float:left; margin-right:5px;">
            <span class="fa fa-pencil glyphicon glyphicon-pencil"></span>
        </a>

        <form class="icon" method="POST" action="{{ get_url('.delete_view', own=True) }}">
            {{ delete_form.id(value=get_pk_value(row)) }}
            {{ delete_form.url(value=return_url) }}

            {% if delete_form.csrf_token %}
                {{ delete_form.csrf_token }}
            {% elif csrf_token %}
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            {% endif %}

            <button class="btn btn-danger btn-xs" style="float:left" onclick="return confirm('{{ _('Are you sure you want to delete this record?') }}');" title={{ _('Delete comment') }}>
                <span class="fa fa-trash glyphicon glyphicon-trash"></span>
            </button>
        </form>
    {% endif %}

{% endblock %}
