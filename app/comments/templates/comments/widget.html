{% set endpoint = endpoint or "" %}


<div class="box box-primary {% if comments_collapsed %} collapsed-box {% endif %}">
    <div class="box-header with-border">
        <div data-widget="collapse" style="cursor: pointer">
            <h3 class="box-title">{{ _("Comments") }}</h3>
        </div>
        {% if current_user.is_allowed(admin_view.endpoint, 'comments_edit') and not view_only %}
            <div class="box-tools pull-right">
                <a href="{{ url_for(endpoint + '.add_comment', entity_id=model.id, url=return_url) }}" class="btn btn-box-tool btn-sm" data-toggle="tooltip" title="{{ _("Add") }}">
                    <i class="fa fa-plus"></i>
                </a>
            </div>
        {% endif %}
    </div>
    <div class="box-body">
        {% if comments %}
            {% for c in comments %}
                {% if current_user.is_allowed(admin_view.endpoint, 'comments_edit') and not view_only and current_user == c.author %}
                    <div class="box-tools pull-right">
                        <a href="{{ url_for(endpoint + '.edit_comment', comment_id=c.id, url=return_url) }}" class="btn btn-box-tool btn-sm" data-toggle="tooltip" title="{{ _("Edit") }}">
                            <i class="fa fa-edit"></i>
                        </a>
                    </div>
                {% endif %}
                <div style="overflow: overlay; max-height: 300px">
                    <small style="color: grey">
                        {{ c.created|localize_datetime }} от {{ c.author }}
                        {% if c.modified %}({{ pgettext("comment", "modified")}} {{ c.modified|localize_datetime }}){% endif %}:
                    </small>
                    <p>{{ c.content|safe }}</p>
                </div>
                {% if not loop.last %}<hr/>{% endif %}
            {% endfor %}
        {% else %}
            <p class="text-muted text-center">{{ _("There is no comments right now") }}</p>
        {% endif %}
    </div>
    {% if comments %}
        <div class="box-footer">
            <a href="{{ url_for(endpoint + '.list_comments', entity_id=model.id, url=return_url, flt_view=admin_view.endpoint) }}" class="btn btn-default pull-right">{{ _("All comments") }}</a>
        </div>
    {% endif %}
</div>
