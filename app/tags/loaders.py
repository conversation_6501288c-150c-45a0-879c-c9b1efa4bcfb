"""Tag-specific Ajax loaders for filtering entities by tag presence."""

import logging

from cmf.core.loaders import CoreAjaxModelLoader

logger = logging.getLogger(__name__)


class TagFilteredAjaxModelLoader(CoreAjaxModelLoader):
    """Ajax loader that filters entities by a specific tag."""

    def __init__(self, name, model, tag, **kwargs):
        self.tag = tag
        super().__init__(name, model, **kwargs)

    def get_list_filter(self):
        """Override to add tag filtering."""
        flt = super().get_list_filter()
        flt["tags"] = self.tag
        return flt


class TagExcludedAjaxModelLoader(CoreAjaxModelLoader):
    """Ajax loader that excludes entities that have a specific tag."""

    def __init__(self, name, model, tag, **kwargs):
        self.tag = tag
        super().__init__(name, model, **kwargs)

    def get_list_filter(self):
        """Override to add tag exclusion filtering."""
        flt = super().get_list_filter()
        flt["tags__ne"] = self.tag
        return flt


class BulkTagFilteredAjaxModelLoader(CoreAjaxModelLoader):
    """Ajax loader that filters entities having at least one of the specified tags."""

    def __init__(self, name, model, tags, **kwargs):
        self.tags = tags
        super().__init__(name, model, **kwargs)

    def get_list_filter(self):
        """Override to add bulk tag filtering."""
        flt = super().get_list_filter()
        flt["tags__in"] = self.tags
        return flt


class BulkTagExcludedAjaxModelLoader(CoreAjaxModelLoader):
    """Ajax loader that excludes entities having any of the specified tags."""

    def __init__(self, name, model, tags, **kwargs):
        self.tags = tags
        super().__init__(name, model, **kwargs)

    def get_list_filter(self):
        """Override to add bulk tag exclusion filtering."""
        flt = super().get_list_filter()
        flt["tags__nin"] = self.tags
        return flt
