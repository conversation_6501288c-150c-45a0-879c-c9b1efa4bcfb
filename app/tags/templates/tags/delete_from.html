{% extends 'admin/model/cms_edit.html' %}

{% import 'admin/model/cms_layout.html' as cms_model_layout with context %}


{% block header %}
    <h1>Delete tag {{ model }} from</h1>
{% endblock %}


{% block form %}
    {# [PROBABLY] purpose of this code - get rid of 'Save and continue' button. #}
    {{ lib.render_form(form, has_menu=True) }}
{% endblock %}


{% block tail_js %}

    {{ super() }}

    <script>

        (function($) {
            viewStorage.removeItem('selected_items', 'select_providers');
            viewStorage.removeItem('selected_items', 'select_offers');
        })(jQuery)

    </script>

{% endblock %}
