{% extends 'admin/model/cms_edit.html' %}

{% import 'admin/model/cms_layout.html' as cms_model_layout with context %}

{% set has_menu = False %}


{% block header %}
    <h1>
        Bulk remove 
        {% for tag in tags %}
            <a href="{{ url_for('tag.details_view', id=tag.id) }}" style="color: {{ tag.color }}; text-decoration: none;">{{ tag.name }}</a>{% if not loop.last %}, {% endif %}
        {% endfor %}
        from
    </h1>
{% endblock %}


{% block form %}
    {{ lib.render_form(form, has_menu=has_menu) }}
{% endblock %}



