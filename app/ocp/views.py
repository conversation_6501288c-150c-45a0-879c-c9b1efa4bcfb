from datetime import datetime
from io import Bytes<PERSON>
from typing import Generator

from flask import (
    Response,
    current_app,
    flash,
    redirect,
    request,
    send_file,
)
from flask_babelex import lazy_gettext as _
from flask_security import current_user
from prebilling.providers import EMPTY_PROVIDER
from prebilling.rest.accounts import AccountRequest
from werkzeug.wsgi import FileWrapper

from app.accounts.components import (
    Account,
    AccountInfo,
    AccountsList,
    BatchSubscriptions,
    Component,
    Devices,
    Extra,
    Region,
    Sessions,
    Subscriptions,
    Yakassa,
)
from app.accounts.views import AccountView as CMSAccountView
from app.accounts.views import AddAccountView as CMSAddAccountView
from app.auth import expose
from app.cms.models import TaskStatus
from app.common.views import (
    ProviderMixin,
    get_channels_for_offer,
)
from app.purchases.forms import AcquiringReportForm
from app.purchases.report.models import ProviderReport
from app.purchases.report.utils import ReportAcquiring
from app.utils import get_month_start_end
from cmf.core.view import (
    BasicView,
    IndexView,
)

from .forms import (
    MigrationForm,
    NewUserSettings,
)


class OCPView(ProviderMixin, BasicView):
    def __init__(self, *args, **kwargs):
        self._prebilling_service = current_app.extensions["prebilling"]
        super().__init__(*args, **kwargs)

    def make_user_action_event(self, response):
        event = super().make_user_action_event(response)

        if event:
            event.type = "ocp"

        return event


class OCPIndexView(IndexView):

    provider = None

    def is_visible(self):
        return False


class AccountsView(CMSAccountView):
    def list_columns(self):
        if current_user.is_allowed("accounts", "user_activity"):
            return (
                ("login", "Login"),
                ("email", "E-mail"),
                ("purchases", "Покупки"),
                ("last_play_time", "Дата последнего использования"),
                ("created", "Дата регистрации"),
            )

        return (
            ("login", "Login"),
            ("email", "E-mail"),
            ("purchases", "Покупки"),
            ("created", "Дата регистрации"),
        )

    def build_components(self) -> Generator[Component, None, None]:
        prebilling_service = current_app.extensions["prebilling"]

        self.list_component = AccountsList(
            method_name_prefix="",
            prebilling_service=prebilling_service,
            disable_cancel_subscription=False,
            disable_group_operations=False,
            list_query={"provider_state": 2, "include_purchases": True},
            list_columns=self.list_columns,
        )
        yield self.list_component

        self.info_component = AccountInfo(method_name_prefix="", prebilling_service=prebilling_service)
        yield self.info_component

        self.account_block += [
            Account(
                prebilling_service=prebilling_service,
                disable_user_deletion=False,
                hide_provider=True,
                web_url=current_app.config["WEB_URL"],
                notification_email=current_app.config["MAIL_USERNAME"],
                phone_regexp=current_app.config["ACCOUNT_PHONE_REGEXP"],
                password_length=current_app.config["AUTO_GENERATE_PASSWORD_LENGTH"],
                password_alphabet=current_app.config["AUTO_GENERATE_PASSWORD_ALPHABET"],
            ),
            Extra(prebilling_service=prebilling_service),
        ]
        yield from self.account_block

        self.content_block += [
            Subscriptions(
                prebilling_service=prebilling_service,
                purchases_service=current_app.extensions["purchases"],
                disable_cancel_subscription=False,
                hide_subscription_details=True,
                offer_types=current_app.config["OFFER_TYPE_CHOICES"],
                show_integration=current_app.config["PURCHASES_INTEGRATION"],
                show_notification=current_app.config["PURCHASES_NOTIFICATION"],
                timezone=current_app.config["DEFAULT_TIMEZONE"],
            ),
            Yakassa(
                prebilling_service=prebilling_service,
                yakassa_service=current_app.extensions["yakassa"],
                view_only=False,
            ),
            BatchSubscriptions(
                method_name_prefix="subscriptions",
                prebilling_service=prebilling_service,
                disable_cancel_subscription=False,
                show_integration=current_app.config["PURCHASES_INTEGRATION"],
                show_notification=current_app.config["PURCHASES_NOTIFICATION"],
                timezone=current_app.config["DEFAULT_TIMEZONE"],
            ),
            Devices(prebilling_service=prebilling_service),
            Region(prebilling_service=prebilling_service),
            Sessions(
                prebilling_service=prebilling_service,
            ),
        ]
        yield from self.content_block


class CandidatesView(AccountsView):

    list_columns = (
        ("login", "Login"),
        ("email", "E-mail"),
        ("purchases", "Покупки"),
        ("created", "Дата подачи заявки"),
    )

    def build_components(self) -> Generator[Component, None, None]:
        prebilling_service = current_app.extensions["prebilling"]

        self.list_component = AccountsList(
            method_name_prefix="",
            prebilling_service=prebilling_service,
            disable_cancel_subscription=True,
            disable_group_operations=True,
            hide_provider_columns=True,
            view_only=True,
            list_query={"provider_state": 0, "include_purchases": True},
            list_columns=self.list_columns,
        )
        yield self.list_component

        self.info_component = AccountInfo(method_name_prefix="", prebilling_service=prebilling_service)
        yield self.info_component

        self.account_block += [
            Account(
                prebilling_service=prebilling_service,
                disable_user_deletion=True,
                hide_provider=True,
                view_only=True,
                web_url=current_app.config["WEB_URL"],
                notification_email=current_app.config["MAIL_USERNAME"],
            )
        ]
        yield from self.account_block

        self.content_block += [
            Subscriptions(
                prebilling_service=prebilling_service,
                disable_cancel_subscription=True,
                hide_subscription_details=True,
                view_only=True,
                offer_types=current_app.config["OFFER_TYPE_CHOICES"],
                show_integration=current_app.config["PURCHASES_INTEGRATION"],
                show_notification=current_app.config["PURCHASES_NOTIFICATION"],
            ),
            BatchSubscriptions(
                method_name_prefix="subscriptions",
                prebilling_service=prebilling_service,
                disable_cancel_subscription=True,
                view_only=True,
                show_integration=current_app.config["PURCHASES_INTEGRATION"],
                show_notification=current_app.config["PURCHASES_NOTIFICATION"],
            ),
            Devices(prebilling_service=prebilling_service, view_only=True),
            Region(prebilling_service=prebilling_service, view_only=True),
            Sessions(
                prebilling_service=prebilling_service,
                view_only=True,
            ),
        ]
        yield from self.content_block


class AddAccountView(OCPView, CMSAddAccountView):

    account_endpoint = "accounts"
    template = "accounts/new.html"

    extra_permissions = {"set_password", "set_phone", "set_phone_confirmation"}

    show_provider = False

    @property
    def show_password(self):
        return current_user.is_allowed(self.endpoint, "set_password")

    @property
    def show_phone(self):
        return current_user.is_allowed(self.endpoint, "set_phone")

    @property
    def show_phone_confirmation(self):
        return current_user.is_allowed(self.endpoint, "set_phone_confirmation")


class MigrationView(OCPView):

    template = "accounts/migration.html"

    @expose("/", methods=("GET", "POST"))
    def migrate(self):
        form = MigrationForm(request.form)

        if request.method == "POST":
            if form.validate():
                try:
                    req = AccountRequest()
                    form.populate_obj(req)

                    accounts = self._prebilling_service.accounts.get_by(query={"users.logins": form.data["login"]})

                    if accounts.items is None or len(accounts.items) == 0:
                        flash(_("Account not found"), "error")  # Account is not found
                    else:
                        account = accounts.items[0]

                        if account.provider is None or account.provider["id"] in [EMPTY_PROVIDER]:
                            account, error = self._prebilling_service.accounts.update_provider(
                                account, self.provider, None
                            )
                        elif account.provider["id"] == self.provider.id:
                            return redirect(self.get_url("accounts.info", id=account.id))
                        else:
                            provider_id = account.provider.get("id")

                            provider = None
                            if provider_id:
                                provider = self._prebilling_service.providers.get(provider_id)

                            flash(
                                "{}: {}".format(
                                    _("The account is linked to another provider"),
                                    provider.name if provider else provider_id,
                                ),
                                "error",
                            )
                            return redirect(self.get_url(".migrate"))

                        if account:
                            flash(_("Account created successfully"), "success")  # Account is created

                        return redirect(self.get_url("accounts.info", id=account.id))
                except Exception as e:
                    raise
                    flash("{}: {}".format(_("Account creation error"), e), "error")  # Error creating account: {}

        return self.render(self.template, form=form)


class OffersView(OCPView):

    template = "purchases/content/offers.html"

    @expose("/", methods=("GET",))
    def index(self):
        offer_id = request.args.get("id")

        offers = self._prebilling_service.offers.list(self.provider.id)
        groups = sorted(set(map(lambda o: o.typ.value, offers or [])))
        grouped_offers = {
            current_app.config["OFFER_TYPE_CHOICES"].get(g): [o for o in offers if o.typ.value == g] for g in groups
        }

        channels = []

        if offer_id:
            offer = self._prebilling_service.offers.one(offer_id)
            channels = get_channels_for_offer(offer)

        return self.render(
            self.template,
            grouped_offers=grouped_offers,
            offer_provider_id=self.offer_provider_id,
            offer_id=offer_id,
            channels=channels,
        )

    def offer_provider_id(self, offer_id):
        return self.provider.inverted_subscriptions.get(offer_id)


class ReportsView(OCPView):

    template = "purchases/reports/index.html"

    @expose("/")
    def index(self):
        reports_qs = ProviderReport.objects(provider=self.provider.id, published=True, status=TaskStatus.DONE)
        reports_qs = reports_qs.order_by("-finished_at")
        return self.render(self.template, reports=reports_qs)

    @expose("/report", perm="download")
    def report(self):
        report = ProviderReport.objects(
            id=request.args.get("id"), provider=self.provider.id, published=True, status=TaskStatus.DONE
        ).first()

        return send_file(report.path, as_attachment=True, mimetype="application/excel")


class AcquiringReportsView(OCPView):
    template = "purchases/provider/acquiring_reports.html"

    @expose("/", methods=("GET", "POST"))
    def create_report(self):

        form = AcquiringReportForm(request.form)
        if request.method == "POST" and form.validate():
            start, end = get_month_start_end(year=form.year.data, month=form.month.data)

            report = ReportAcquiring(
                out=BytesIO(),
                provider_id=self.provider.id,
                start_date=start,
                end_date=end,
            )

            out = report.generate(only_rows=True)
            out.seek(0)

            out_fn = "{}.acquiring.{}".format(datetime.now().strftime("%d.%m.%Y_%H%M%S"), "xls")

            w = FileWrapper(out)
            resp = Response(w, mimetype="application/excel", direct_passthrough=True)
            resp.headers["Content-Disposition"] = "attachment; filename=" + out_fn
            resp.headers["Content-type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

            return resp

        return self.render("purchases/provider/acquiring_reports.html", form=form)


class TemplatesView(OCPView):

    template = "purchases/provider/notifications_settings.html"

    @expose("/", methods=["GET", "POST"])
    def index(self):
        provider = self.provider

        if request.method == "POST":
            form = NewUserSettings(request.form)

            if not form.validate():
                return self.render(self.template, form=form)

            data = form.data

            provider = self.provider
            provider.generate_password = data["auto"]
            provider.new_account_subject = data["subject_new"]
            provider.new_account_text = data["message_new"]
            provider.reset_password_subject = data["subject_reset"]
            provider.reset_password_text = data["message_reset"]

            try:
                provider = self._prebilling_service.providers.update_provider(provider)
            except Exception as e:
                flash(str(e), "error")
            else:
                flash(_("Provider data updated"), "success")

            return redirect(self.get_url(".index"))

        form = NewUserSettings(
            **{
                "auto": provider.generate_password,
                "subject_new": provider.new_account_subject,
                "message_new": provider.new_account_text,
                "subject_reset": provider.reset_password_subject,
                "message_reset": provider.reset_password_text,
            }
        )

        return self.render(self.template, form=form)
