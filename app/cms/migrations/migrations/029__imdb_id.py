from flask import current_app
from pymongo.collection import Collection
from pymongo.cursor import Cursor

from app.abcvod.core.models.abcvod import AbcvodTitle
from app.abcvod.providers.amedia2.models import Amedia2Title
from app.abcvod.providers.basevod.models import BaseVODTitle
from app.abcvod.providers.basevod.models_factory import BaseVODModelsFactory
from app.abcvod.providers.etnomedia.models import EtnomediaTitle
from app.abcvod.providers.premier.cardgroup.models import PremierCardgroupTitle
from app.abcvod.providers.premier.showcase.models import PremierShowcaseTitle
from app.abcvod.providers.start2.models import Start2Title
from app.abcvod.providers.vipplay.models import VipPlayTitle
from app.abcvod.providers.wink.models import WinkTitle
from app.cms.migrations.base import BaseCommand
from app.common.utils import BulkUpdate


class Command(BaseCommand):
    """#121016 - it appears that IMDB ids may contain letters, so we have to change field type from `int` to `str`."""

    models = [BaseVODTitle]  # will be populated dynamically in __init__

    def __init__(self, save=False):
        self.models = self.models[:]

        config = current_app.config
        if config["AMEDIA2"]:
            self.models.append(Amedia2Title)
        if config["ETNOMEDIA"]:
            self.models.append(EtnomediaTitle)
        if config["PREMIER_SHOWCASE"]:
            self.models.append(PremierShowcaseTitle)
        if config["PREMIER"]:
            self.models.append(PremierCardgroupTitle)
        if config["START2"]:
            self.models.append(Start2Title)
        if config["VIPPLAY"]:
            self.models.append(VipPlayTitle)
        if config["WINK"]:
            self.models.append(WinkTitle)

        # Add extra BaseVODs if they defined.
        for extra_basevod_config in config.get("BASEVOD_EXTRA_INSTANCES") or []:
            db_alias = extra_basevod_config["db_alias"]
            repo = BaseVODModelsFactory.get_models_for_alias(db_alias)
            title_model = repo.Title
            self.models.append(title_model)

        super().__init__(save=save)

    def migrate_model(self, model: type[AbcvodTitle]):
        collection: Collection = model._get_collection()
        bulk_updater = BulkUpdate(model, save=self.save)

        cursor: Cursor = collection.find(
            {"imdb_id": {"$exists": True, "$ne": None, "$type": "int"}},
            {"_id": 1, "imdb_id": 1},
        )

        for doc in cursor:
            if doc["imdb_id"]:
                bulk_updater.update_one(
                    filter={"_id": doc["_id"]},
                    update={"$set": {"imdb_id": str(doc["imdb_id"])}},
                )

        bulk_updater.flush()

    def main(self):
        for model in self.models:
            self.migrate_model(model)
