from app.clickhouse.export.exporters import MediaEpgItemsExporter
from app.clickhouse.export.models import ClickhouseExportSettings
from app.cms.migrations.base import BaseClickhouseCommand
from app.cms.models import ClickhouseSettings


class Command(BaseClickhouseCommand):
    """Add bounds_received_at field to EPG events table.

    Field: `bounds_received_at` - timestamp when precise EPG boundaries were received via API.
    """

    affected_tables = (MediaEpgItemsExporter.CH_TABLE_NAME,)

    def __init__(self, save=False):
        settings = ClickhouseExportSettings.get()

        if settings.export_media_epg_items and settings.export_media_epg_items.data_source:
            clickhouse_settings: ClickhouseSettings = settings.export_media_epg_items.data_source
            self.CH_DB = clickhouse_settings.db_name
            self.CH_URL = clickhouse_settings.uri
        else:
            self.skip_backup_requirements = True
            self.skip_migration = True
            self.skip_message = "Settings for 'Export Media EPG Items' does not exist in database."
            self.CH_DB = "SKIP"
            self.CH_URL = "SKIP"
        super().__init__(save=save)

    def main(self):
        table_name = MediaEpgItemsExporter.CH_TABLE_NAME
        if not self.table_exists(table_name):
            print(f"Table {table_name} not found in ClickHouse, creating it with the new schema.")
            self.execute_ddl_query(MediaEpgItemsExporter.CH_CREATE_TABLE)
            return

        self.execute_ddl_query(
            f"ALTER TABLE {table_name} "
            "ADD COLUMN IF NOT EXISTS bounds_received_at DateTime COMMENT 'timestamp получения точных границ события через API' CODEC(Delta, ZSTD);"  # noqa
        )
        print(f"Table {table_name} has been updated with the bounds_received_at field.")
