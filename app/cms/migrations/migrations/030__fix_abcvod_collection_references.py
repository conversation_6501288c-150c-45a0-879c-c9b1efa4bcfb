from __future__ import annotations

from bson import (
    DBRef,
    ObjectId,
)
from flask import current_app
from pymongo.collection import Collection

from app.abcvod.core.models.abcvod import (
    AbcvodCollection,
    AbcvodTitle,
)
from app.abcvod.providers.amedia2.models import Amedia2Title
from app.abcvod.providers.basevod.models import BaseVODTitle
from app.abcvod.providers.basevod.models_factory import BaseVODModelsFactory
from app.abcvod.providers.etnomedia.models import EtnomediaTitle
from app.abcvod.providers.megogo.models import MegogoTitle
from app.abcvod.providers.premier.cardgroup.models import PremierCardgroupTitle
from app.abcvod.providers.premier.showcase.models import PremierShowcaseTitle
from app.abcvod.providers.start2.models import Start2Title
from app.abcvod.providers.vipplay.models import VipPlayTitle
from app.abcvod.providers.wink.models import WinkTitle
from app.cms.migrations.base import BaseCommand


class Command(BaseCommand):
    """#120718 - delete absent collections from 'collections' field in Title models in ABCVOD."""

    models = [BaseVODTitle]  # will be populated dynamically in __init__

    def __init__(self, save=False):
        self.models = self.models[:]

        config = current_app.config
        if config["AMEDIA2"]:
            self.models.append(Amedia2Title)
        if config["ETNOMEDIA"]:
            self.models.append(EtnomediaTitle)
        if config["PREMIER_SHOWCASE"]:
            self.models.append(PremierShowcaseTitle)
        if config["PREMIER"]:
            self.models.append(PremierCardgroupTitle)
        if config["START2"]:
            self.models.append(Start2Title)
        if config["VIPPLAY"]:
            self.models.append(VipPlayTitle)
        if config["WINK"]:
            self.models.append(WinkTitle)
        if config["MEGOGO"]:
            self.models.append(MegogoTitle)

        # Add extra BaseVODs if they defined.
        for extra_basevod_config in config.get("BASEVOD_EXTRA_INSTANCES") or []:
            db_alias = extra_basevod_config["db_alias"]
            repo = BaseVODModelsFactory.get_models_for_alias(db_alias)
            title_model = repo.Title
            self.models.append(title_model)

        super().__init__(save=save)

    def get_ids(self, title_model: type[AbcvodTitle]) -> set[ObjectId | DBRef]:
        pipeline = [
            {"$unwind": "$collections"},
            {"$group": {"_id": None, "all_ids": {"$addToSet": "$collections"}}},
        ]

        collection: Collection = title_model._get_collection()
        result = list(collection.aggregate(pipeline))

        if result:
            all_ids: list[ObjectId | DBRef] = result[0]["all_ids"]
            return {ref for ref in all_ids}
        return set()

    def _clean_collections_field(self, title_model: type[AbcvodTitle]):
        titles_queryset = title_model.objects.filter(
            collections__exists=True,
            collections__ne=None,
            collections__not__size=0,
        )
        if not (titles_count := titles_queryset.count()):
            print("There is no titles with any content in `collections` field, skipping.")
            return

        for title in titles_queryset:
            title.collections = []
            if self.save:
                title.save()
        print(f"Cleaned {titles_count} titles.")

    def migrate_model(self, title_model: type[AbcvodTitle]):
        print("=" * 80)
        print(f"Migrating {title_model.__name__}...")
        CollectionModel: type[AbcvodCollection] = title_model.collections.field.document_type

        collections_cache: dict[ObjectId, CollectionModel] = {c.id: c for c in CollectionModel.objects()}
        existing_ids = set(collections_cache.keys())

        if not existing_ids:
            print(f"There is no existing collections for {title_model.__name__}. Cleaning `collections` field...")
            self._clean_collections_field(title_model)
            return

        used_ids = self.get_ids(title_model)
        broken_ids_or_refs = used_ids - set(existing_ids)
        if not broken_ids_or_refs:
            print(f"OK! {title_model.__name__} contains 0 broken references. Nothing to fix.")
            return

        broken_ids = {ref.id if isinstance(ref, DBRef) else ref for ref in broken_ids_or_refs}
        print(f"Processing {title_model.__name__} - has {len(broken_ids_or_refs)} broken references.")
        titles_queryset = title_model.objects.filter(
            __raw__={"collections": {"$in": list(broken_ids_or_refs)}}
        ).no_dereference()

        print(f"Fixing {titles_queryset.count()} titles from {title_model.__name__}.")

        collections_cache: dict[ObjectId, CollectionModel] = {c.id: c for c in CollectionModel.objects()}

        for title in titles_queryset:
            current_ids: set[ObjectId] = set(c.id for c in title.collections)
            allowed_ids = current_ids - broken_ids
            title.collections = [collections_cache[_id] for _id in allowed_ids]
            if self.save:
                title.save()

    def main(self):
        for model in self.models:
            self.migrate_model(model)
