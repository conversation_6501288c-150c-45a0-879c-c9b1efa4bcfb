const webpack = require('webpack');
const path = require('path');

const CopyPlugin = require('copy-webpack-plugin');
const TerserPlugin = require("terser-webpack-plugin");

const srcPath = path.join(__dirname, 'app/web');
const vendorPath = path.join(__dirname, 'app/static/vendor');
const compiledPath = path.join(__dirname, 'app/static/js/compiled');

const entries = ['roles', 'purchase_updater', 'timeline_editor'];

const vendorModules = [
    'inputmask',
    'jquery-sortable',
    'html5sortable',
    'ace-builds',
    'croppie',
    'bootstrap-multiselect',
    'bootstrap-duration-picker',
    'vis',
    'urijs',
    'bson',
    'chart.js',
    'chartjs-plugin-annotation',
    'chartjs-plugin-zoom',
    'daterangepicker',
    'xlsx',
    'canvas2svg',
    'bootstrap-datetimepicker.js',
    'moment-timezone',
    'jsoneditor',
    '@pnotify',
    'bootbox',
];


module.exports = {
    target: 'web',
    cache: true,
    devtool: 'source-map',
    entry: Object.assign(...entries.map(
        entry => ({ [entry]: path.join(srcPath, `${entry}.js`) })
    )),
    resolve: {
        extensions: ['*', '.js'],
        modules: ['node_modules', srcPath]
    },
    output: {
        path: compiledPath,
        publicPath: 'http://localhost:5000/js',
        library: ['App', '[name]'],
        filename: '[name].js'
    },
    devServer: {
        outputPath: path.join(__dirname, 'build')
    },
    module: {
        rules: [
            {
                test: /\.js?$/,
                include: srcPath,
                loader: 'babel-loader',
                exclude: /vendor/,
                options: {
                    presets: ['@babel/preset-env'],
                    plugins: ['@babel/plugin-transform-runtime', '@babel/plugin-proposal-class-properties']
                }
            }
        ]
    },
    plugins: [
        new webpack.EnvironmentPlugin(['NODE_ENV']),
        new webpack.NoEmitOnErrorsPlugin(),
        new CopyPlugin({
            patterns: vendorModules.map(
                module => ({
                    noErrorOnMissing: true,
                    context: 'node_modules',
                    from: `${module}/\*\*/\*`,
                    to: vendorPath
                })
            )
        }),
    ],
    optimization: {
        minimize: false,
    },
};
