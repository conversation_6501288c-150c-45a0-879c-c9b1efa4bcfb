"""This script may help extract test data from big XML (for me, original XML is 50M+ and contains 6000+ titles).

Edit `xml_path` and run it manually.
"""
from __future__ import annotations

import shutil
from collections import defaultdict
from itertools import product
from pathlib import Path

from lxml import etree

xml_path = Path("/home/<USER>/tmp/megogo/all_mgg.xml")  # change to whatever place you've downloaded the file.
data_directory = Path(__file__).parent / "data"


def get_filename(
    prefix: str,
    element_is_series: bool,
    element_protection: str,
    element_distributions: list[str],
    element_contract_id: str,
    obj_id: str,
) -> str:
    """Generate filename for an object based on parameters."""
    type_str = "series" if element_is_series else "movie"
    dist_str = "-".join(element_distributions)

    return f"{prefix}-{type_str}_p-{element_protection}_d-{dist_str}_c-{element_contract_id}_{obj_id}.xml"


def is_series(element: etree.Element) -> bool:
    return element.get("serial", "") == "true"


def get_distributions(element: etree.Element) -> list[str]:
    distributions = element.find("distributions")
    if distributions is None:
        return []
    return [dist.get("vod_type") for dist in distributions.findall("distribution")]


def get_protection(element: etree.Element) -> str:
    protection_tag = element.find("protection")
    return protection_tag.text  # required to be there


def save_object(element: etree.Element, file_path: Path):
    """Save the pretty-printed XML element to a file if it has a valid ID."""
    xml_string = etree.tostring(element, encoding="utf-8", pretty_print=True)

    with open(file_path, "wb") as f:
        f.write(xml_string)


def get_contract_id(element: etree.Element) -> str | None:
    tag = element.find("contract_id")
    if tag is None:
        return None

    return tag.text


def get_titles(
    *,
    prefix: str,
    exclude_ids: list[str],
    required_series: bool,
    amount: int,
    required_distribution: str = "",
    excluded_distribution: str = "",
    required_protection: str = "",
    excluded_protection: str = "",
    excluded_contract_ids: list[str] | None = None,
) -> list[str]:
    """Parse XML file and extract objects matching given parameters."""
    data_directory.mkdir(exist_ok=True)

    count = 0
    context = etree.iterparse(str(xml_path), events=("end",), tag="object")

    saved_title_info_ids = []  # list of title ids, which have the file.

    for event, element in context:
        obj_id = element.get("id")

        if obj_id in exclude_ids:
            continue

        element_protection = get_protection(element)
        element_distributions = get_distributions(element)
        element_contract_id = get_contract_id(element)
        element_is_series = is_series(element)

        if element_is_series == required_series:
            continue
        if required_distribution and required_distribution not in element_distributions:
            continue
        if excluded_distribution and excluded_distribution in element_distributions:
            continue
        if required_protection and required_protection != element_protection:
            continue
        if excluded_protection and excluded_protection == element_protection:
            continue
        if excluded_contract_ids and element_contract_id in excluded_contract_ids:
            continue

        file_path = data_directory / get_filename(
            prefix, element_is_series, element_protection, element_distributions, element_contract_id, obj_id
        )

        save_object(element, file_path)
        saved_title_info_ids.append(obj_id)

        count += 1
        if count >= amount:
            break

        element.clear()  # Clear element to free memory
        while element.getprevious() is not None:
            del element.getparent()[0]

    del context  # Stack overflow told me to do so.
    return saved_title_info_ids


def collect_importable_titles(collected_ids: list | None = None):
    """Collect some titles that are suitable for import."""
    params = {
        "required_series": [False, True],
        "amount": [1],
        "required_distribution": ["svod"],
        "required_protection": ["0", ""],
        "excluded_protection": ["0", ""],
    }

    keys = list(params)
    collected_ids = collected_ids[:] if collected_ids else []
    for values in product(*(params[key] for key in keys)):
        kwargs = dict(zip(keys, values))
        print(f"searching {kwargs=}")

        new_ids = get_titles(
            prefix="good",
            exclude_ids=collected_ids,
            **kwargs,
        )
        if new_ids:
            print("Found!")
            collected_ids += new_ids
        else:
            print("Not found :(")
    return collected_ids


def clear_directory(path: Path):
    for item in path.iterdir():
        if item.is_dir():
            shutil.rmtree(item)
        else:
            item.unlink()


def _get_distributions(element: etree.Element) -> list[str]:
    """Will return list of available distributions.

    I.e. for
        <distributions>
            <distribution vod_type="advod"/>
            <distribution vod_type="svod"/>
        </distributions>
    result will be: ["advod", "svod"]
    """
    return [
        dist.get("vod_type")
        for dist in element.xpath(".//distributions/distribution")
        if dist.get("vod_type") is not None
    ]


def print_all_contracts_info():
    """Show all contract ids, sorted by popularity."""
    data_directory.mkdir(exist_ok=True)

    context = etree.iterparse(str(xml_path), events=("end",), tag="object")
    contracts_info = defaultdict(lambda: defaultdict(lambda: 0))

    for event, element in context:
        contract_id_tag = element.find("contract_id")
        contract_id = contract_id_tag.text
        distributions = _get_distributions(element)
        for dist in distributions:
            contracts_info[contract_id]["total"] = contracts_info[contract_id]["total"] + 1
            contracts_info[contract_id][dist] = contracts_info[contract_id][dist] + 1

        element.clear()  # Clear element to free memory
        while element.getprevious() is not None:
            del element.getparent()[0]

    del context  # Stack overflow told me to do so.
    for contract_id, value in sorted(contracts_info.items(), key=lambda item: item[1]["total"], reverse=True):
        info = f"total: {value['total']}|"
        for k in sorted(value.keys()):
            if k == "total":
                continue
            info += f"{k}: {value[k]}|"
        print(f"{contract_id}: {info}")


if __name__ == "__main__":
    print_all_contracts_info()
    # clear_directory(data_directory)
    # collect_importable_titles()
    # collect_titles_not_for_import()
