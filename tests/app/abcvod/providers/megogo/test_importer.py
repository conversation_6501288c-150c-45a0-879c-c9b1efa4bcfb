import sys
import tempfile
from pathlib import Path
from unittest.mock import Mock

from app.abcvod.models import Abcvod<PERSON>erson
from app.abcvod.providers.megogo.importer import MegogoImporter
from app.abcvod.providers.megogo.models import (
    MegogoEpisode,
    MegogoGenre,
    MegogoImportTracker,
    MegogoSeason,
    MegogoTitle,
)
from app.cms.models import (
    LogEntry,
    TaskTrigger,
)
from tests.base import CmsTestCase


def merge_xml_files_simple() -> Path:
    """Merge all XML files from data_directory into a single XML file wrapped in <objects> tags.

    Files are read as text and concatenated without parsing.
    Returns:
        Path: Path to the temporary file containing the merged XML.
    """
    data_directory = Path(__file__).parent / "data"

    # Create a named temporary file with a fixed name
    temp_file = tempfile.NamedTemporaryFile(
        mode="w", encoding="utf-8", suffix=".xml", prefix="cms_test_merged_xml_", delete=False
    )
    temp_file_path = Path(temp_file.name)

    # Write opening <objects> tag
    temp_file.write('<?xml version="1.0" encoding="UTF-8"?>\n<objects>\n')

    # Read and concatenate contents of all .xml files
    for xml_file in data_directory.glob("*.xml"):
        with open(xml_file, "r", encoding="utf-8") as f:
            content = f.read()
            temp_file.write(content + "\n")

    temp_file.write("</objects>\n")
    temp_file.close()
    return temp_file_path


class TestMegogoImporter(CmsTestCase):
    xml_path: Path

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.xml_path: Path = merge_xml_files_simple()

    @classmethod
    def tearDownClass(cls) -> None:
        super().tearDownClass()
        cls.xml_path.unlink(missing_ok=True)

    def _run_importer(self):
        importer = MegogoImporter(self.app, task_trigger=TaskTrigger.MANUAL)
        importer.skip_download_images = True
        self.patch_object(importer, "_download_xml_file", Mock(return_value=self.xml_path))
        self.patch_object(importer, "_remove_temp_file", Mock())
        importer.temp_xml_file_path = self.xml_path
        importer.run()

    def run_importer(self):
        try:
            self._run_importer()
        except Exception:
            # This is not actually part of the test, but this may help with debug. Improve/remove as you please.
            tracker: MegogoImportTracker = MegogoImportTracker.objects.first()
            if tracker and tracker.import_errors:
                print("\n" + "=" * 80)
                print("DEBUG INFO ABOUT THE LAST IMPORT ERROR:")
                error = tracker.import_errors[-1]
                print(error)
                print("=====ERROR TYPE ====")
                print(error.error_type)
                print("=====ERROR MESSAGE ====")
                print(error.error_message)
                print("=====EXCEPTION DETAILS ====")
                print(error.exception_details)
                print("=" * 80)
            raise

    def test_import(self):
        """General importer test with no restrictions."""
        self.run_importer()

        print(f"{MegogoTitle.objects(is_published=True).count()=}")
        print(f"{MegogoSeason.objects(is_published=True).count()=}")
        print(f"{MegogoEpisode.objects(is_published=True).count()=}")
        print(f"{MegogoGenre.objects(is_published=True).count()=}")
        print(f"{AbcvodPerson.objects.count()=}")

        assert MegogoTitle.objects(is_published=True).count() == 6
        assert MegogoSeason.objects(is_published=True).count() == 3
        assert MegogoEpisode.objects(is_published=True).count() == 16
        assert MegogoGenre.objects(is_published=True).count() == 9
        assert AbcvodPerson.objects.count() == 67

        # Check 'drm_required' is checked for titles with protection != "0"
        assert MegogoTitle.objects.get(remote_id="2409731").drm_required
        assert not MegogoTitle.objects.get(remote_id="253").drm_required

        tracker: MegogoImportTracker = MegogoImportTracker.objects.get()
        assert len(tracker.import_errors) == 0

    def test_allowed_contracts(self):
        """Make sure titles with only allowed contracts are imported."""
        self.app.config["MEGOGO_ALLOWED_CONTRACTS"] = [101896, 92006]
        self.run_importer()
        assert MegogoTitle.objects.count() == 3

    def test_allowed_distributions(self):
        """Make sure titles with only allowed distributions are imported."""
        self.app.config["MEGOGO_ALLOWED_DISTRIBUTIONS"] = ["notexist"]
        self.run_importer()
        assert MegogoTitle.objects.count() == 0

        self.app.config["MEGOGO_ALLOWED_DISTRIBUTIONS"] = ["svod"]
        self.run_importer()
        assert MegogoTitle.objects.count() == 6

    def test_import_nonmajor_content(self):
        self.app.config["MEGOGO_ALLOWED_DISTRIBUTIONS"] = []  # Allow all distributions.
        self.app.config["MEGOGO_ALLOWED_CONTRACTS"] = [11111111111]  # Cannot have empty value. Empty = allow all.
        self.run_importer()
        assert MegogoTitle.objects.count() == 2  # right now only 2 titles have protection = "0"


class _TestMegogoImportDebug(CmsTestCase):
    """Debug version of the test, imports content using actual XML."""

    def setUp(self) -> None:
        super().setUp()
        self.app.config["MEGOGO_XML_URL"] = "https://xml.megogo.net/assets/files/kz/3975/all_mgg.xml"
        self.app.config["MEGOGO_ALLOWED_CONTRACTS"] = [101206]
        self.app.config["MEGOGO_ALLOWED_DISTRIBUTIONS"] = ["advod", "dto", "tvod", "svod", "fvod"]

    def _run_importer(self):
        importer = MegogoImporter(self.app, task_trigger=TaskTrigger.MANUAL)
        importer.skip_download_images = True
        importer.run()

    def run_importer(self):
        try:
            self._run_importer()
        except Exception:
            # This is not actually part of the test, but this may help with debug. Improve/remove as you please.
            tracker: MegogoImportTracker = MegogoImportTracker.objects.first()
            if tracker and tracker.import_errors:
                print("\n" + "=" * 80)
                print("DEBUG INFO ABOUT THE LAST IMPORT ERROR:")
                error = tracker.import_errors[-1]
                print(error)
                print("=====ERROR TYPE ====")
                print(error.error_type)
                print("=====ERROR MESSAGE ====")
                print(error.error_message)
                print("=====EXCEPTION DETAILS ====")
                print(error.exception_details)
                print("=" * 80)
            raise

    def test_debug(self):
        """General importer test with no restrictions."""
        if "::test_debug" not in " ".join(sys.argv):
            self.skipTest("Skipping `test_debug` test - only for manual testing.")

        self.run_importer()

        print(f"{MegogoTitle.objects(is_published=True).count()=}")
        print(f"{MegogoSeason.objects(is_published=True).count()=}")
        print(f"{MegogoEpisode.objects(is_published=True).count()=}")
        print(f"{MegogoGenre.objects(is_published=True).count()=}")
        print(f"{AbcvodPerson.objects.count()=}")

        tracker: MegogoImportTracker = MegogoImportTracker.objects.get()

        print("=" * 80)
        print("Tracker log:")
        for entry in tracker.log:
            entry: LogEntry
            print(f"{entry.datetime}: {entry.message}")
        print("=" * 80)

        assert len(tracker.import_errors) == 0
