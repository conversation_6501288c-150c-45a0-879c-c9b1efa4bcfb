from __future__ import annotations

from bson import (
    DBRef,
    ObjectId,
)

from app.abcvod.providers.start2.models import (
    Start2Collection,
    Start2Title,
)
from tests.app.cms.migrations.base import TestMigrationMixin
from tests.base import CmsTestCase


class Test030(TestMigrationMixin, CmsTestCase):
    migration_name: str = "030__fix_abcvod_collection_references"

    def setUp(self):
        super().setUp()
        self.app.config["START2"] = True
        self.collection = Start2Collection(slug="what", caption="what").save()
        self.title = Start2Title(slug="what", caption="what").save()
        self.fake_id = ObjectId()
        self.fake_dbref = DBRef("start2_collection", ObjectId())
        self.title.update(add_to_set__collections=[self.collection.id, self.fake_id, self.fake_dbref])
        self.title.reload()

        title_collection = Start2Title._get_collection()
        title_collection.update_one(
            {"_id": self.title.id}, {"$set": {"collections": [self.collection.id, self.fake_id, self.fake_dbref]}}
        )

    def _get_raw_collections(self) -> list[ObjectId | DBRef]:
        return Start2Title._get_collection().find_one().get("collections")

    def test_save(self):
        self.run_migration(self.migration_name, save=True)
        raw_collections = self._get_raw_collections()
        assert self.fake_id not in raw_collections
        assert self.fake_dbref not in raw_collections
        assert self.collection.id in raw_collections

    def test_no_save(self):
        self.run_migration(self.migration_name)
        raw_collections = self._get_raw_collections()
        assert self.fake_id in raw_collections
        assert self.fake_dbref in raw_collections
        assert self.collection.id in raw_collections

    def test_run_twice(self):
        self.run_migration(self.migration_name, save=True)
        raw_collections = self._get_raw_collections()
        assert self.fake_id not in raw_collections
        assert self.fake_dbref not in raw_collections
        assert self.collection.id in raw_collections

        # Run again.
        self.mocker.patch("app.cms.migrations.base.confirm", return_value=True)
        self.run_migration(self.migration_name, save=True)

        raw_collections = self._get_raw_collections()
        assert self.fake_id not in raw_collections
        assert self.fake_dbref not in raw_collections
        assert self.collection.id in raw_collections
