from __future__ import annotations

import string
from random import (
    choices,
    randint,
)
from typing import TypeVar

from pymongo.collection import Collection

from app.abcvod.core.models.abcvod import AbcvodTitle
from app.abcvod.providers.amedia2.models import Amedia2Title
from app.abcvod.providers.vipplay.models import VipPlayTitle
from tests.app.cms.migrations.base import TestMigrationMixin
from tests.base import CmsTestCase

TitleModel = TypeVar("TitleModel", bound=AbcvodTitle)


class TestMigration029(TestMigrationMixin, CmsTestCase):
    migration_name: str = "029__imdb_id"

    def setUp(self) -> None:
        super().setUp()
        self.app.config["AMEDIA2"] = True
        self.app.config["VIPPLAY"] = True

    def _add_record(self, model: type[TitleModel]) -> TitleModel:
        collection: Collection = model._get_collection()

        slug = "".join(choices(string.ascii_lowercase, k=10))
        imdb_id = randint(1000, 10000)

        document = {"slug": slug, "imdb_id": imdb_id}

        result = collection.insert_one(document)
        return model.objects.get(id=result.inserted_id)

    def test_save(self):
        amedia_doc = self._add_record(Amedia2Title)
        vipplay_doc = self._add_record(VipPlayTitle)
        assert amedia_doc.imdb_id == int(amedia_doc.imdb_id)  # sanity check

        self.run_migration(self.migration_name, save=True)
        amedia_doc.reload()
        vipplay_doc.reload()
        assert amedia_doc.imdb_id == str(amedia_doc.imdb_id)
        assert vipplay_doc.imdb_id == str(vipplay_doc.imdb_id)

    def test_no_save(self):
        amedia_doc = self._add_record(Amedia2Title)
        vipplay_doc = self._add_record(VipPlayTitle)
        assert amedia_doc.imdb_id == int(amedia_doc.imdb_id)  # sanity check

        self.run_migration(self.migration_name)
        amedia_doc.reload()
        vipplay_doc.reload()
        assert amedia_doc.imdb_id == int(amedia_doc.imdb_id)
        assert vipplay_doc.imdb_id == int(vipplay_doc.imdb_id)

    def test_no_data(self):
        """Just make sure migration will not fail."""
        self.run_migration(self.migration_name, save=True)

    def test_run_twice(self):
        """Make sure migration may run twice."""
        amedia_doc = self._add_record(Amedia2Title)
        vipplay_doc = self._add_record(VipPlayTitle)

        # Run first time.
        self.run_migration(self.migration_name, save=True)
        amedia_doc.reload()
        vipplay_doc.reload()
        assert amedia_doc.imdb_id == str(amedia_doc.imdb_id)
        assert vipplay_doc.imdb_id == str(vipplay_doc.imdb_id)

        # Run again.
        self.mocker.patch("app.cms.migrations.base.confirm", return_value=True)
        self.run_migration(self.migration_name, save=True)
        amedia_doc.reload()
        vipplay_doc.reload()
        assert amedia_doc.imdb_id == str(amedia_doc.imdb_id)
        assert vipplay_doc.imdb_id == str(vipplay_doc.imdb_id)
