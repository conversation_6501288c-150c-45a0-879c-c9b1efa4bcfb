from app.clickhouse.export.exporters import MediaEpgItemsExporter
from app.clickhouse.export.models import (
    ClickhouseExportSettings,
    ExportEpgSettings,
)
from app.cms.models import ClickhouseSettings
from tests.app.cms.migrations.base import TestMigrationMixin
from tests.base import (
    ClickhouseTestCaseMixin,
    CmsTestCase,
)


class TestMigration031(TestMigrationMixin, ClickhouseTestCaseMixin, CmsTestCase):

    migration_name: str = "031__epg_export_add_bounds_received_at"
    new_fields: set[str] = {
        "bounds_received_at",
    }

    def setUp(self) -> None:
        super().setUp()
        self.table_name = MediaEpgItemsExporter.CH_TABLE_NAME
        self.data_source = ClickhouseSettings(
            did="did", uri=self.clickhouse_url, tcp_address="tcp_address", db_name=self.clickhouse_db_name
        ).save()
        self.export_settings = ClickhouseExportSettings.get()
        self.export_settings.export_media_epg_items = ExportEpgSettings(data_source=self.data_source)
        self.export_settings.save(validate=False)
        self._setup_old_clickhouse_table()

    def _setup_old_clickhouse_table(self):
        # Create the old version of the table without the bounds_received_at field
        CH_CREATE_TABLE = f"""
        CREATE TABLE IF NOT EXISTS {self.table_name} (
            --- information about EPG event
            `date` Date COMMENT 'Дата из scheduled_start' CODEC(Delta, ZSTD),
            `updated_at` DateTime COMMENT 'timestamp последнего обновления информации о событии' CODEC(Delta, ZSTD),
            `event_id` String COMMENT 'Идентификатор события',
            `event_title` String COMMENT 'Название события в дефолтной локализации',
            `dvr_disabled` Bool COMMENT 'true, если в CMS для события указана настройка запрета DVR',
            `scheduled_start` DateTime COMMENT 'timestamp планируемого времени начала вещания события' CODEC(Delta, ZSTD),
            `scheduled_end` DateTime COMMENT 'timestamp планируемого времени окончания вещания события' CODEC(Delta, ZSTD),
            `actually_start` DateTime COMMENT 'timestamp фактического времени начала события (toDateTime(0) если отсутствует)' CODEC(Delta, ZSTD),

            --- information about TV-channel and schedule
            `channel_id` LowCardinality(String) COMMENT 'идентификатор ТВ-канала',
            `channel_num` Int16 COMMENT 'номер кнопки ТВ-канала' CODEC(Delta, ZSTD),
            `channel_title` LowCardinality(String) COMMENT 'название ТВ-канала в дефолтной локализации',
            `media_id` LowCardinality(String) COMMENT 'идентификатор медии',
            `schedule_id` LowCardinality(String) COMMENT 'внутренний идентификатор расписания',
            `source_schedule_id` LowCardinality(String) COMMENT 'идентификатор расписания на стороне поставщика EPG',
            `media_cdn_id` LowCardinality(String) COMMENT 'идентификатор медии в CDN',

            --- additional content information
            `series_name` String COMMENT 'Название сериала в дефолтной локализации',
            `country` String COMMENT 'Страна производства',
            `age_rating` Int16 COMMENT 'Возрастной рейтинг' CODEC(Delta, ZSTD),
            `imdb_id` String COMMENT 'Идентификатор фильма на IMDb',
            `imdb_rating` Float32 COMMENT 'Рейтинг IMDb',
            `kp_id` String COMMENT 'Идентификатор фильма на Кинопоиске',
            `kp_rating` Float32 COMMENT 'Рейтинг Кинопоиска',
            `year` Int16 COMMENT 'Год выпуска' CODEC(Delta, ZSTD),

            --- for future iterations
            `vod_source_id` LowCardinality(String) COMMENT 'идентификатор vodSourceId каталога передач',
            `vod_title_id` String COMMENT 'идентификатор тайтла в каталоге передач',
            `vod_season_id` String COMMENT 'идентификатор сезона в каталоге передач',
            `vod_episode_id` String COMMENT 'идентификатор эпизода в каталоге передач',
            `vod_available_till` DateTime COMMENT 'момент времени, до которого событие будет доступно в каталоге передач' CODEC(Delta, ZSTD)
        ) ENGINE = ReplacingMergeTree(updated_at)
        order by (
            channel_num,
            channel_id,
            media_id,
            schedule_id,
            source_schedule_id,
            date,
            event_id
        )
        PARTITION BY toYYYYMM(date)
        primary key (
            channel_num,
            channel_id,
            media_id,
            schedule_id,
            source_schedule_id,
            date,
            event_id
        )
        """  # noqa
        self.execute_ddl_query(CH_CREATE_TABLE)

    def _get_fields(self, table_name: str) -> set[str]:
        """Helper to get a set of field names from a ClickHouse table using DESCRIBE TABLE."""
        response = self.query(f"DESCRIBE TABLE {table_name}")
        response.raise_for_status()

        return {line.split("\t")[0] for line in response.text.strip().split("\n") if line}

    def _assert_fields_should_exist(self):
        assert not self.new_fields - self._get_fields(self.table_name), f"Should have fields {self.new_fields}"

    def _assert_fields_should_not_exist(self):
        assert not self.new_fields & self._get_fields(self.table_name), f"Should not have fields {self.new_fields}"

    def test_no_save(self):
        """Test migration when save=False."""
        self.run_migration(self.migration_name)
        self._assert_fields_should_not_exist()

    def test_save(self):
        """Test migration when save=True."""
        self.run_migration(self.migration_name, save=True)
        self._assert_fields_should_exist()

    def test_no_table(self):
        """Test migration when table doesn't exist."""
        self.execute_ddl_query(f"DROP TABLE {self.table_name}")
        self.run_migration(self.migration_name, save=True)
        self._assert_fields_should_exist()

    def test_no_export_settings(self):
        """Test migration when there is no settings with data source."""
        self.export_settings.export_media_epg_items = None
        self.export_settings.save(validate=False)
        self.run_migration(self.migration_name, save=True)
        self._assert_fields_should_not_exist()

    def test_run_twice(self):
        """Make sure migration may run twice."""
        # Run first time
        self.run_migration(self.migration_name, save=True)
        self._assert_fields_should_exist()

        # Run again - should not fail
        self.mocker.patch("app.cms.migrations.base.confirm", return_value=True)
        self.run_migration(self.migration_name, save=True)
        self._assert_fields_should_exist()
