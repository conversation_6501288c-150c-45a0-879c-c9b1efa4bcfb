from typing import TYPE_CHECKING
from unittest.mock import Mock

from bs4 import BeautifulSoup
from bson import ObjectId
from flask import url_for
from prebilling.rest.accounts import Account as PrebillingRestAccount
from prebilling.rest.accounts import UpdateAccountRequest
from prebilling.rest.providers import Provider

from app.regions import Regions
from tests.app.accounts.base import ComponentTestCase

if TYPE_CHECKING:
    from app.accounts.components.region import Region as RegionComponent


class TestRegionComponent(ComponentTestCase):
    @classmethod
    def get_account_fixture(cls) -> PrebillingRestAccount:
        return PrebillingRestAccount.from_json(
            {
                "id": "account_id",
                "provider": {"id": str(ObjectId())},
                "region": "region1",
            }
        )

    def setUp(self) -> None:
        super().setUp()
        self.regions_list_mock = self.patch_object(Regions, "list", Mock(return_value=["region1", "region2"]))

    @classmethod
    def get_component_view(cls) -> "RegionComponent":
        with cls.app.app_context():
            from app.accounts.components.region import Region as RegionComponent

        RegionComponent.provider = Provider.from_json({"id": cls.account_fixture.provider["id"]})
        return RegionComponent(
            prebilling_service=cls.prebilling,
            default_view="region",
        )

    def test_render_widget(
        self,
    ):
        """Test widget on account's index page."""
        account = self.account_fixture
        self.patch_object(self.prebilling.accounts, "get", Mock(return_value=account))

        response = self.client.get(url_for("view.info", id=account.id))
        soup = BeautifulSoup(response.text)

        assert soup.select_one("a[title='Edit']") is not None
        assert account.region in soup.text
        assert account.region in Regions.list()

    def test_manage_region_get(self):
        """Test manage region page."""
        account = self.account_fixture
        self.patch_object(self.prebilling.accounts, "get", Mock(return_value=account))

        url = url_for("view.region_manage_region", id=account.id)
        response = self.client.get(url)

        # Validate prebilling requests.
        self.prebilling.accounts.get.assert_called_once_with(account.id)

        soup = BeautifulSoup(response.text)
        form = soup.find("form")
        assert form is not None

        select = form.find("select")
        assert select is not None

        options = select.find_all("option")
        assert [o["value"] for o in options] == [""] + Regions.list()

        submit = form.find("button", type="submit")
        assert submit is not None

    def test_manage_region_post(self):
        """Test update account."""
        # Prepare mocks & fixtures.
        account = self.account_fixture

        def update_account(account_id, request: UpdateAccountRequest):
            account._region = "region2"

        self.patch_object(self.prebilling.accounts, "get", Mock(return_value=account))
        self.patch_object(self.prebilling.accounts, "update_account", Mock(side_effect=update_account))

        regions_list = Regions.list()
        new_region = regions_list[1]
        form_data = {"region": new_region}

        # Make a post request in order to update region.
        url = url_for("view.region_manage_region", id=account.id)
        post_response = self.client.post(url, data=form_data, follow_redirects=False)

        # Validate response & prebilling requests.
        assert post_response.status_code == 302

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.prebilling.accounts.update_account.called_once_with(account.id, UpdateAccountRequest())

        # Make a get request & check if data is updated.
        get_response = self.client.get(url)
        soup = BeautifulSoup(get_response.text)

        form = soup.find("form")
        assert form is not None

        select = form.find("select")
        assert select is not None

        assert select.select_one("option[selected]").text == new_region
