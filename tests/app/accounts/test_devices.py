from datetime import datetime
from typing import TYPE_CHECKING
from unittest.mock import Mock

from bs4 import BeautifulSoup
from bson import ObjectId
from flask import url_for
from prebilling.devices import Device
from prebilling.rest.accounts import Account as PrebillingRestAccount
from prebilling.rest.devices import DeviceRequest
from prebilling.rest.providers import Provider

from tests.app.accounts.base import ComponentTestCase

if TYPE_CHECKING:
    from app.accounts.components.devices import Devices as DevicesComponent


def get_mac(num):
    mac = "00:00:00:"
    return "{}{}{}:{}{}:{}{}".format(mac, *hex(num)[2:].zfill(6))


def get_device_info_fixture(i) -> dict:
    return {
        "id": get_mac(i),
        "id_type": "mac",
        "name": f"device_{i}_id",
        "description": "desc",
        "user_id": "account_id",
        "created_at": str(datetime.utcnow()),
        "updated_at": str(datetime.utcnow()),
    }


def get_devices_info_list_fixture() -> list[dict]:
    return [get_device_info_fixture(i) for i in range(3)]


def get_devices_fixture(devices_info: list[dict]) -> list[Device]:
    return [Device.from_json(data) for data in devices_info]


class TestDevices(ComponentTestCase):
    # Type hints for fixtures.
    devices: list[Device]
    devices_info: list[dict]

    @classmethod
    def setUpClass(cls) -> None:
        cls.devices_info = get_devices_info_list_fixture()
        cls.devices = get_devices_fixture(cls.devices_info)
        super().setUpClass()

    @classmethod
    def get_component_view(cls) -> "DevicesComponent":
        with cls.app.app_context():
            from app.accounts.components.devices import Devices as DevicesComponent

        component = DevicesComponent(
            prebilling_service=cls.prebilling,
            default_view="devices",
        )
        component.__class__.provider = Provider.from_json({"id": cls.account_fixture.provider["id"]})
        return component

    @classmethod
    def get_account_fixture(cls) -> PrebillingRestAccount:
        master_user = Mock()
        master_user.id = "master_user_id"

        return PrebillingRestAccount.from_json(
            {
                "id": "account_id",
                "master_user": master_user,
                "provider": {"id": str(ObjectId())},
                "devices": cls.devices_info,
            }
        )

    def test_render_widget(self):
        """Test widget on account's index page."""
        self.patch_object(self.prebilling.accounts, "get", Mock(return_value=self.account_fixture))

        localize_datetime = self.app.jinja_env.filters["localize_datetime"]

        response = self.client.get(url_for("view.info", id=self.account_fixture.id))

        soup = BeautifulSoup(response.text)

        for device in self.devices:
            d_html = soup.find(attrs={"data-device-id": device.id})

            assert d_html is not None

            dt = d_html.text
            assert device.id in dt
            assert device.id_type in dt
            assert device.name in dt
            assert localize_datetime(device.created_at) in dt
            assert localize_datetime(device.updated_at) in dt
            assert "Delete" in dt

        assert "Add device" in soup.text

    def test_manage_device_get(self):
        """Test manage device page."""
        # Prepare fixtures.
        account = self.account_fixture
        self.patch_object(self.prebilling.accounts, "get", Mock(return_value=account))

        # Make request
        response = self.client.get(url_for("view.devices_manage_device", id=account.id))

        # Validate requests to prebilling.
        self.prebilling.accounts.get.assert_called_once_with(account.id)

        # Validate response content.
        soup = BeautifulSoup(response.text)
        form = soup.find("form")
        assert form is not None

        type_select = form.select_one("select#id_type")
        assert type_select is not None

        options = type_select.find_all("option")
        assert "mac" in [o["value"] for o in options]

        assert form.select_one("input#id") is not None
        assert form.select_one("input#name") is not None
        assert form.select_one("textarea#description") is not None
        assert form.find("button", type="submit") is not None

    def test_manage_device_post(self):
        account = self.account_fixture
        new_device_obj = get_device_info_fixture(10)
        new_device = Device.from_json(new_device_obj)

        form_data = {
            "description": "desc",
            "id": "00:00:00:00:00:0a",
            "id_type": "mac",
            "name": "device_10_id",
            "new": True,
        }

        def create_device_side_effect(account_id, user_id, request: DeviceRequest):
            account._devices.append(new_device_obj)
            return new_device, None

        self.patch_object(self.prebilling.accounts, "get", Mock(return_value=account))
        self.patch_object(self.prebilling.devices, "new", Mock(side_effect=create_device_side_effect))

        # Make request & validate response.
        url = url_for("view.devices_manage_device", id=account.id)
        response = self.client.post(url, data=form_data, follow_redirects=False)
        assert response.status_code == 302

        # Validate prebilling calls.
        self.prebilling.devices.new.called_once_with(account.id, account.master_user.id, DeviceRequest())
        self.prebilling.accounts.get.assert_called_once_with(account.id)

    def test_delete_device(self):
        """Test delete device page."""
        device = self.devices[0]
        account = self.account_fixture

        def delete_device_side_effect(account_id, user_id, device_id):
            account._devices = [d for d in account._devices if d["id"] != device_id]
            return 1

        delete_mock = self.patch_object(self.prebilling.devices, "delete", Mock(side_effect=delete_device_side_effect))
        get_mock = self.patch_object(self.prebilling.accounts, "get", Mock(return_value=account))

        url = url_for("view.devices_delete_device", id=account.id, device_id=device.id)
        delete_response = self.client.get(url, follow_redirects=False)
        assert delete_response.status_code == 302

        get_mock.assert_called_once_with(account.id)
        delete_mock.called_once_with(account.id, account.master_user.id, device.id)

        get_response = self.client.get(url)
        assert device.id not in BeautifulSoup(get_response.text).text
