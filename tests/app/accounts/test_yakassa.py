from datetime import datetime
from typing import TYPE_CHECKING
from unittest.mock import Mock

from bs4 import BeautifulSoup
from flask import url_for
from flask_login import current_user
from prebilling.rest.accounts import Provider
from prebilling.rest.offers import Offer as PrebillingRestOffer
from werkzeug.wrappers.response import Response

from tests.app.accounts.base import ComponentTestCase

if TYPE_CHECKING:
    from app.accounts.components.yakassa import <PERSON><PERSON><PERSON> as YakassaComponent


class TestYakassa(ComponentTestCase):
    component: "YakassaComponent"
    yakassa_service: Mock

    @classmethod
    def get_component_view(cls) -> "YakassaComponent":
        with cls.app.app_context():
            from app.accounts.components.yakassa import <PERSON><PERSON><PERSON> as YakassaComponent

        YakassaComponent.provider = Provider.from_json({"id": cls.account_fixture.provider["id"]})
        cls.yakassa_service = Mock()  # Will be actually set up during `setUp()`

        component = YakassaComponent(
            prebilling_service=cls.prebilling,
            yakassa_service=cls.yakassa_service,
            default_view="yakassa",
        )
        return component

    def setUp(self) -> None:
        super().setUp()
        self.setup_constant_fixtures()
        self.setup_yakassa_service_fixture()
        self.setup_prebilling_fixture()

    def setup_constant_fixtures(self):
        now_ts = int(datetime.utcnow().timestamp())
        self.offer: PrebillingRestOffer = PrebillingRestOffer.from_json({"id": "offer_id", "name": "offer_name"})
        self.card = {
            "issuer_country": "RU",
            "card_type": "MasterCard",
            "expiry_year": "2025",
            "expiry_month": "12",
            "first6": "111111",
            "last4": "1111",
        }
        self.create_request_info = {
            "ip": "***********",
            "req_info": {
                "app": {"build_type": "", "hash": "", "id": "", "version": "", "white_label": ""},
                "device": {"brand": "", "id": "", "model": "", "type": "", "year": ""},
            },
            "user_agent": "user_agent",
        }
        self.payment_method = {
            "id": "payment_method_id",
            "card": self.card,
            "saved": True,
            "title": "Bank card *1111",
            "type": "bank_card",
        }
        self.payment_info = {"payment_info": "payment_info"}

        self.payments: list[dict] = 2 * [
            {
                "id": "payment_id",
                "amount": {"currency": "RUB", "value": "300.00"},
                "cancellation_details": {"party": "*", "reason": "*"},
                "captured_at": now_ts,
                "created_at": now_ts,
                "expires_at": now_ts,
                "confirmation": {"confirmation_url": "url", "return_url": "url", "type": ""},
                "metadata": {
                    "account_id": "account_id",
                    "expires_at": 0,
                    "offer_id": "offer_id",
                    "subscription_id": "sub_id",
                },
                "paid": True,
                "payment_method": self.payment_method,
                "recipient": {"account_id": "account_id", "gateway_id": "gateway_id"},
                "refundable": False,
                "status": "succeeded",
                "test": False,
            }
        ]

        self.subscriptions_info_fixture: list[dict] = [
            {
                "id": "sub_id",
                "created_at": now_ts,
                "expires_at": now_ts,
                "updated_at": now_ts,
                "subscription_renew_status": "active",
                "create_request_info": self.create_request_info,
                "last_payment": self.payments[0],
                "offer": {"id": "offer_id", "description": "", "period": 10, "price": "100"},
            }
        ]

    def setup_yakassa_service_fixture(self):
        def get_payments(a_id, s_id, skip=0, limit=1):
            return [self.payments[0]] if limit == 1 else self.payments

        self.patch_object_multi(
            self.yakassa_service,
            {
                "subscriptions": Mock(return_value=self.subscriptions_info_fixture),
                "subscription": Mock(return_value=self.subscriptions_info_fixture[0]),
                "archived_subscriptions": Mock(return_value=self.subscriptions_info_fixture),
                "payment_method": Mock(return_value=self.payment_method),
                "payments": Mock(side_effect=get_payments),
                "payment": Mock(return_value=self.payments[0]),
                "payment_info": Mock(return_value=self.payment_info),
            },
        )

    def setup_prebilling_fixture(self):
        self.patch_object(self.prebilling.offers, "list", Mock(return_value=[self.offer]))
        self.patch_object(self.prebilling.accounts, "get", Mock(return_value=self.account_fixture))

    @staticmethod
    def assert_card(payment, payment_text):
        pm = payment["payment_method"]
        card = pm["card"]

        assert f"{card['expiry_month']}/{card['expiry_year']}" in payment_text
        assert pm["title"].split("*")[-1] in payment_text
        assert card["card_type"] in payment_text

    def test_render_widget(self):
        """Test widget on account's index page."""
        localize_datetime = self.app.jinja_env.filters["localize_datetime"]
        account = self.account_fixture

        response = self.client.get(url_for("view.info", id=account.id))

        soup = BeautifulSoup(response.text)

        self.prebilling.offers.list.assert_called_once_with(account.provider["id"])
        self.yakassa_service.payment_method.assert_called_with(account.id)
        self.yakassa_service.subscriptions.assert_called_with(account.id)

        for sub_info in self.subscriptions_info_fixture:
            status = sub_info["subscription_renew_status"]

            subscription_html = soup.find(attrs={"data-yakassa-sub-id": sub_info["id"]})

            assert subscription_html is not None

            subscription_text = subscription_html.text
            assert self.offer.name in subscription_text
            assert self.app.config["SUBSCRIPTION_RENEW_STATUS"][status] in subscription_text
            assert localize_datetime(sub_info["created_at"]) in subscription_text
            assert localize_datetime(sub_info["expires_at"]) in subscription_text
            assert localize_datetime(sub_info["updated_at"]) in subscription_text
            assert "Активна" in subscription_text  # FIXME: remove `SUBSCRIPTION_RENEW_STATUS` from config.py
            assert "Pause auto-renewal" in subscription_text

            footer = soup.select_one("div.yakassa")
            assert "Subscriptions archive" in footer.text

            self.assert_card(sub_info["last_payment"], footer.select_one("span.payment-method").text)

    def test_archived_subscriptions(self):
        """Test archived subscriptions page."""
        account = self.account_fixture
        localize_datetime = self.app.jinja_env.filters["localize_datetime"]

        url = url_for("view.yakassa_archived_subscriptions", id=account.id)
        response = self.client.get(url)
        soup = BeautifulSoup(response.text)

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.prebilling.offers.list.assert_called_once_with(account.provider["id"])
        self.yakassa_service.archived_subscriptions.assert_called_with(
            account.id, skip=0, limit=self.component.archived_subscriptions_page_size + 1
        )

        for sub_info in self.subscriptions_info_fixture:
            status = sub_info["subscription_renew_status"]
            status_name = self.app.config["SUBSCRIPTION_RENEW_STATUS"][status]

            sub_html = soup.find(attrs={"data-yakassa-sub-id": sub_info["id"]})

            assert sub_html is not None

            sub_text = sub_html.text
            assert self.offer.name in sub_text
            assert status_name in sub_text
            assert localize_datetime(sub_info["created_at"]) in sub_text
            assert localize_datetime(sub_info["expires_at"]) in sub_text
            assert localize_datetime(sub_info["updated_at"]) in sub_text

    def test_subscription_details(self):
        """Test subscription details page."""
        account = self.account_fixture
        localize_datetime = self.app.jinja_env.filters["localize_datetime"]

        self.payments[0]["status"] = "canceled"

        sub_info = self.subscriptions_info_fixture[0]
        sub_id = sub_info["id"]

        sub_create_request_info = sub_info["create_request_info"]
        sub_device = sub_create_request_info["req_info"]["device"]
        sub_app = sub_create_request_info["req_info"]["app"]

        sub_renew_status = sub_info["subscription_renew_status"]
        sub_renew_status_name = self.app.config["SUBSCRIPTION_RENEW_STATUS"][sub_renew_status]

        sub_last_payment = sub_info["last_payment"]
        sub_last_payment_status_name = self.app.config["SUBSCRIPTION_PAYMENT_STATUS"][sub_last_payment["status"]]

        url = url_for("view.yakassa_subscription_details", id=account.id, s_id=sub_id)
        response = self.client.get(url)

        soup = BeautifulSoup(response.text)

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.prebilling.offers.list.assert_called_once_with(account.provider["id"])
        self.yakassa_service.subscription.assert_called_with(account.id, sub_id)
        self.yakassa_service.payments.assert_called_with(
            account.id, sub_id, skip=0, limit=self.component.payments_page_size + 1
        )

        details_text = soup.select_one("div.subscription-details").text
        assert self.offer.name in details_text
        assert sub_renew_status_name in details_text
        assert localize_datetime(sub_info["created_at"]) in details_text
        assert localize_datetime(sub_info["expires_at"]) in details_text
        assert localize_datetime(sub_info["updated_at"]) in details_text
        assert sub_create_request_info["ip"] in details_text

        for value in sub_app.values():
            assert value in details_text

        for value in sub_device.values():
            assert value in details_text

        last_payment_text = soup.select_one("div.last-payment").text
        assert sub_last_payment_status_name in last_payment_text
        assert sub_last_payment["cancellation_details"]["reason"] in last_payment_text
        assert sub_last_payment["amount"]["value"] in last_payment_text
        assert sub_last_payment["amount"]["currency"] in last_payment_text
        assert localize_datetime(sub_last_payment["created_at"]) in last_payment_text
        assert localize_datetime(sub_last_payment["expires_at"]) in last_payment_text
        assert localize_datetime(sub_last_payment["captured_at"]) in last_payment_text
        self.assert_card(sub_last_payment, last_payment_text)

        assert "Last payment" in last_payment_text
        assert "Synchronize" in last_payment_text

        payment_history_div = soup.select_one("div.payments-history")
        for payment in self.payments:
            payment_text = payment_history_div.find(attrs={"data-payment-id": payment["id"]}).text

            assert payment["id"] in payment_text
            assert self.app.config["SUBSCRIPTION_PAYMENT_STATUS"][payment["status"]] in payment_text
            assert payment["amount"]["value"] in payment_text
            assert payment["amount"]["currency"] in payment_text
            assert localize_datetime(payment["created_at"]) in payment_text
            assert localize_datetime(payment["expires_at"]) in payment_text
            assert localize_datetime(payment["captured_at"]) in payment_text
            self.assert_card(payment, payment_text)

    def test_cancel_subscription(
        self,
    ):
        """Test cancel subscription page."""
        account = self.account_fixture
        sub_id = self.subscriptions_info_fixture[0]["id"]

        url = url_for("view.yakassa_cancel_subscription", id=account.id, s_id=sub_id)
        response = self.client.get(url, follow_redirects=False)
        assert response.status_code == 302

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.yakassa_service.cancel_subscription.assert_called_once_with(account.id, sub_id)

    def test_activate_subscription(self):
        """Test activate subscription page."""
        account = self.account_fixture
        sub_id = self.subscriptions_info_fixture[0]["id"]

        url = url_for("view.yakassa_activate_subscription", id=account.id, s_id=sub_id)
        response = self.client.get(url, follow_redirects=False)
        assert response.status_code == 302

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.yakassa_service.activate_subscription.assert_called_once_with(account.id, sub_id)

    def test_finish_subscription(self):
        account = self.account_fixture
        sub_id = self.subscriptions_info_fixture[0]["id"]

        url = url_for("view.yakassa_finish_subscription", id=account.id, s_id=sub_id)

        response = self.client.get(url, follow_redirects=False)
        assert response.status_code == 302

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.yakassa_service.finish_subscription.assert_called_once_with(sub_id)

    def test_refund_subscription(self):
        account = self.account_fixture
        sub_id = self.subscriptions_info_fixture[0]["id"]

        url = url_for("view.yakassa_refund_subscription", id=account.id, s_id=sub_id)

        current_user.login = "test_yakassa_current_user"
        response = self.client.get(url, follow_redirects=False)
        assert response.status_code == 302

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.yakassa_service.refund_subscription.assert_called_once_with(sub_id, "test_yakassa_current_user")

    def test_payment_method(self):
        """Test payment method page."""
        to_pretty_json = self.app.jinja_env.filters["to_pretty_json"]

        account = self.account_fixture

        url = url_for("view.yakassa_payment_method_view", id=account.id)
        response = self.client.get(url)

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.yakassa_service.payment_method.assert_called_with(account.id)

        payment_method_text = BeautifulSoup(response.text).text
        assert to_pretty_json(self.payment_method) in payment_method_text

    def test_payment_method_remove(self):
        """Test payment method remove page."""
        account = self.account_fixture

        url = url_for("view.yakassa_payment_method_remove", id=account.id)
        response = self.client.get(url, follow_redirects=False)

        assert isinstance(response, Response)
        assert response.status_code == 302

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.yakassa_service.remove_payment_method.assert_called_once_with(account.id)

    def test_payment(self):
        """Test payment page."""
        account = self.account_fixture

        localize_datetime = self.app.jinja_env.filters["localize_datetime"]
        to_pretty_json = self.app.jinja_env.filters["to_pretty_json"]

        sub_id = self.subscriptions_info_fixture[0]["id"]

        payment = self.payments[0]
        payment_id = payment["id"]

        payment["status"] = "canceled"

        url = url_for("view.yakassa_payment", id=account.id, subscription_id=sub_id, payment_id=payment_id)
        response = self.client.get(url)
        payment_text = BeautifulSoup(response.text).text

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.yakassa_service.payment.assert_called_once_with(account.id, payment_id)
        self.yakassa_service.payment_info.assert_called_once_with(account.id, sub_id, payment_id)

        payment_status_name = self.app.config["SUBSCRIPTION_PAYMENT_STATUS"][payment["status"]]
        assert payment_status_name in payment_text
        assert payment["cancellation_details"]["reason"] in payment_text
        assert payment["amount"]["value"] in payment_text
        assert payment["amount"]["currency"] in payment_text
        assert localize_datetime(payment["created_at"]) in payment_text
        assert localize_datetime(payment["expires_at"]) in payment_text
        assert localize_datetime(payment["captured_at"]) in payment_text
        self.assert_card(payment, payment_text)

        assert to_pretty_json(payment) in payment_text
        assert to_pretty_json(self.payment_info) in payment_text

    def test_sync_payment(self):
        """Test payment synchronization."""
        account = self.account_fixture
        sub_id = self.subscriptions_info_fixture[0]["id"]
        payment_id = self.payments[0]["id"]

        url = url_for("view.yakassa_sync_payment", id=account.id, s_id=sub_id, p_id=payment_id)
        response = self.client.get(url, follow_redirects=False)
        assert response.status_code == 302

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.yakassa_service.sync_payment.assert_called_once_with(account.id, sub_id, payment_id)

    def test_archive_subscription(self):
        """Test archive subscription page."""
        account = self.account_fixture
        sub_id = self.subscriptions_info_fixture[0]["id"]

        url = url_for("view.yakassa_archive_subscription", id=account.id, s_id=sub_id)
        response = self.client.get(url, follow_redirects=False)
        assert response.status_code == 302

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.yakassa_service.archive_subscription.assert_called_once_with(sub_id, current_user.login)
