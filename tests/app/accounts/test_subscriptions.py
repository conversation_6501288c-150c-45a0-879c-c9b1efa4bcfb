from datetime import (
    datetime,
    timedelta,
)
from typing import TYPE_CHECKING
from unittest.mock import Mock

from bs4 import BeautifulSoup
from flask import url_for
from prebilling.rest.accounts import Account
from prebilling.rest.offers import Offer
from prebilling.rest.providers import (
    PROVIDER_STATE_APPROVED,
    Provider,
)
from prebilling.rest.purchases import (
    Purchase,
    PurchaseRequest,
)
from werkzeug import Response
from werkzeug.test import TestResponse

from .base import ComponentTestCase

if TYPE_CHECKING:
    from app.accounts.components import Component
    from app.accounts.components.subscriptions import BatchSubscriptions as BatchSubscriptionsComponent


def purchases_offers(account: Account) -> tuple[list[Purchase], list[Offer]]:
    """Purchases and offers for tests."""
    offers = []
    for i in range(0, 3):
        offers.append(Offer.from_json({"id": f"offer_{i}_id", "name": f"offer_{i}_name"}))

    purchases = []
    for i in range(0, 3):
        purchases.append(
            Purchase.from_json(
                {
                    "id": f"purchase_{i}_id",
                    "accountId": account.id,
                    "offerId": offers[i].id,
                    "created": {"seconds": int(datetime.utcnow().timestamp())},
                    "expireTime": {"seconds": int((datetime.utcnow() + timedelta(days=1)).timestamp())},
                }
            )
        )

    return purchases, offers


class TestBatchSubscriptions(ComponentTestCase):
    use_real_prebilling = True

    @classmethod
    def setUpClass(cls) -> None:
        super().setUpClass()
        cls.app.config["OFFER_TYPE_CHOICES"] = {}
        cls.app.config["PURCHASES_INTEGRATION"] = True
        cls.app.config["PURCHASES_NOTIFICATION"] = True
        cls.app.config["TESTING"] = True

    @classmethod
    def get_component_view(cls) -> "BatchSubscriptionsComponent":
        with cls.app.app_context():
            from app.accounts.components.subscriptions import BatchSubscriptions as BatchSubscriptionsComponent
        component = BatchSubscriptionsComponent(
            prebilling_service=cls.prebilling,
            disable_cancel_subscription=False,
            view_only=False,
            offer_types=cls.app.config["OFFER_TYPE_CHOICES"],
            show_integration=cls.app.config["PURCHASES_INTEGRATION"],
            show_notification=cls.app.config["PURCHASES_NOTIFICATION"],
        )
        component.__class__.provider = Provider.from_json({"id": "provider_id"})
        return component

    def test_batch_get(self):
        # Test GET request
        _, offers = purchases_offers(self.account_fixture)

        self.patch_object(self.prebilling.offers, "list", Mock(return_value=offers))
        account_ids = [f"id_{i}" for i in range(0, 5)]

        url = url_for("view.batchsubscriptions_batch_subscriptions", action="create", offer_id=offers[0].id)
        url += "&" + "&".join([f"account_ids[]={account_id}" for account_id in account_ids])

        # Make request & validate prebilling calls.
        response = self.client.get(url)
        self.prebilling.offers.list.assert_called_once_with(self.component.provider.id)

        # Check if we have a form & submit button.
        soup = BeautifulSoup(response.text)
        form = soup.find("form")
        assert form is not None
        assert form.find("button", type="submit") is not None

    def test_batch_post(self):
        """Test POST request with `create` action."""
        # Prepare fixtures:
        def account_side_effect(account_id):
            return Account.from_json(
                {"id": account_id, "provider": {"id": self.component.provider.id, "state": PROVIDER_STATE_APPROVED}}
            )

        _, offers = purchases_offers(self.account_fixture)
        self.patch_object(self.prebilling.offers, "list", Mock(return_value=offers))
        self.patch_object(self.prebilling.purchases, "create_purchase", Mock(return_value=(Purchase(), None)))
        self.patch_object(self.prebilling.accounts, "get", Mock(side_effect=account_side_effect))
        account_ids = [f"id_{i}" for i in range(0, 5)]
        form_data = {"skip_notification": True, "skip_integration": True}

        url = url_for("view.batchsubscriptions_batch_subscriptions", action="create", offer_id=offers[0].id)
        url += "&" + "&".join([f"account_ids[]={account_id}" for account_id in account_ids])

        # Make request & validate response.
        response = self.client.post(url, data=form_data, follow_redirects=False)
        assert response.status_code == 302

        # validate some calls to prebilling.
        self.prebilling.offers.list.assert_called_once_with(self.component.provider.id)

        # Validate calls to `accounts.get()`
        accounts_get_calls = self.prebilling.accounts.get.call_args_list
        assert len(accounts_get_calls) == len(account_ids)
        assert len(account_ids) == len({call_arguments[0][0] for call_arguments in accounts_get_calls})

        # Validate calls to `purchases.create_purchase()`
        create_purchase_calls = self.prebilling.purchases.create_purchase.call_args_list
        assert len(create_purchase_calls) == len(account_ids)
        assert len(account_ids) == len({cp_arg[0][0].account_id for cp_arg in create_purchase_calls})
        for call_arguments in create_purchase_calls:
            call_arguments = call_arguments[0][0]
            assert isinstance(call_arguments, PurchaseRequest)
            assert call_arguments.offer_id == offers[0].id
            assert call_arguments.provider_id == self.component.provider.id
            assert call_arguments.skip_integration is True
            assert call_arguments.skip_notification is True

    def test_post_with_disable_action(self):
        # Prepare fixtures.
        def account_side_effect(account_id):
            return Account.from_json(
                {"id": account_id, "provider": {"id": self.component.provider.id, "state": PROVIDER_STATE_APPROVED}}
            )

        _, offers = purchases_offers(self.account_fixture)
        self.patch_object(self.prebilling.offers, "list", Mock(return_value=offers))
        self.patch_object(self.prebilling.accounts, "get", Mock(side_effect=account_side_effect))
        self.patch_object(self.prebilling.purchases, "disable_purchase", Mock(return_value=(True, None)))
        account_ids = [f"id_{i}" for i in range(0, 5)]
        url = url_for("view.batchsubscriptions_batch_subscriptions", action="disable", offer_id=offers[0].id)
        url += "&" + "&".join([f"account_ids[]={account_id}" for account_id in account_ids])
        form_data = {"skip_notification": True, "skip_integration": True}

        # Make request & validate response
        response = self.client.post(url, data=form_data, follow_redirects=False)
        assert response.status_code == 302

        # Validate calls to prebilling.
        self.prebilling.offers.list.assert_called_once_with(self.component.provider.id)

        accounts_get_calls = self.prebilling.accounts.get.call_args_list
        assert len(accounts_get_calls) == len(account_ids)
        assert len(account_ids) == len({call_arguments[0][0] for call_arguments in accounts_get_calls})

        disable_purchase_calls = self.prebilling.purchases.disable_purchase.call_args_list
        assert len(disable_purchase_calls) == len(account_ids)
        assert len(account_ids) == len({call_arguments[0][0].account_id for call_arguments in disable_purchase_calls})
        for call_argument in disable_purchase_calls:
            call_argument = call_argument[0][0]
            assert isinstance(call_argument, PurchaseRequest)
            assert call_argument.offer_id == offers[0].id
            assert call_argument.provider_id == self.component.provider.id
            assert call_argument.skip_integration is True
            assert call_argument.skip_notification is True


class TestSubscriptions(ComponentTestCase):
    use_real_prebilling = True

    @classmethod
    def setUpClass(cls) -> None:
        super().setUpClass()
        cls.app.config["OFFER_TYPE_CHOICES"] = {}
        cls.app.config["PURCHASES_INTEGRATION"] = True
        cls.app.config["PURCHASES_NOTIFICATION"] = True
        cls.app.config["TESTING"] = True

    @classmethod
    def get_component_view(cls) -> "Component":
        with cls.app.app_context():
            from app.accounts.components.subscriptions import Subscriptions as SubscriptionsComponent
        return SubscriptionsComponent(
            prebilling_service=cls.prebilling,
            disable_cancel_subscription=False,
            hide_subscription_details=False,
            view_only=False,
            offer_types=cls.app.config["OFFER_TYPE_CHOICES"],
            show_integration=cls.app.config["PURCHASES_INTEGRATION"],
            show_notification=cls.app.config["PURCHASES_NOTIFICATION"],
        )

    def test_render_widget(self):
        """Test widget on account's index page."""
        # prepare fixtures
        purchases, offers = purchases_offers(self.account_fixture)
        list_mock = self.patch_object(self.prebilling.offers, "list", Mock(return_value=offers))
        subscriptions_mock = self.patch_object(self.prebilling.purchases, "subscriptions", Mock(return_value=purchases))

        # Make request.
        response = self.client.get(url_for("view.info", id=self.account_fixture.id))
        assert response.status_code == 200

        # Make sure prebilling was called as expected.
        list_mock.assert_called_once_with(self.account_fixture.provider["id"])
        subscriptions_mock.assert_called_once_with(self.account_fixture.id, self.account_fixture.provider["id"])

        # Make sure all offers are rendered in response.
        for o in offers:
            assert o.name in response.text

        # Validate content of subscriptions list.
        soup = BeautifulSoup(response.text)
        localize_datetime = self.app.jinja_env.filters["localize_datetime"]

        for p in purchases:
            p_html = soup.find(attrs={"data-purchase-id": p.id})
            assert p_html is not None
            assert localize_datetime(p.created) in p_html.text
            assert localize_datetime(p.created) in p_html.text
            assert "Active" in p_html.text
            assert "Disable" in p_html.text

    def test_create(self):
        """Test creation of a purchase."""
        account = self.account_fixture
        purchases, offers = purchases_offers(account)
        self.patch_object(self.prebilling.offers, "list", Mock(return_value=offers))
        self.patch_object(self.prebilling.purchases, "subscriptions", Mock(return_value=[purchases[1]]))
        self.patch_object(self.prebilling.accounts, "get", Mock(return_value=account))

        # Check GET request
        url = url_for("view.subscriptions_manage_subscription", id=account.id)
        response = self.client.get(url)
        page = response.text

        self.prebilling.accounts.get.assert_called_once_with(account.id)
        self.prebilling.purchases.subscriptions.assert_called_once_with(account.id, account.provider["id"])
        self.prebilling.offers.list.assert_called_once_with(account.provider["id"])

        soup = BeautifulSoup(page)

        # Check if we have a form in reponse.
        form = soup.find("form")
        assert form is not None

        # Check input of the form
        select = form.find("select")
        assert select is not None

        # Check that we have offers only available to create.
        options = select.find_all("option")
        assert len(options) == len(offers) - 1
        assert purchases[1].offer_id not in [o["value"] for o in options]

        # Check if we have a submit button
        submit = form.find("button", type="submit")
        assert submit is not None

        # Check POST request
        form_data = {select["name"]: offers[0].id, "skip_notification": True, "skip_integration": True}

        self.prebilling.offers.list = Mock(return_value=offers)
        self.prebilling.purchases.subscriptions = Mock(return_value=[purchases[1]])
        self.prebilling.purchases.create_purchase = Mock(return_value=(Purchase.from_json({"id": "purchase_id"}), None))
        self.prebilling.accounts.get = Mock(return_value=account)

        response: TestResponse = self.client.post(url, data=form_data)

        # Ensure we have successfully parsed and validate form and have redirect response here
        assert isinstance(response, Response)
        assert response.history and response.history[0].status_code == 302

        self.prebilling.accounts.get.assert_called_with(account.id)
        self.prebilling.offers.list.assert_called_with(account.provider["id"])

        # Check request we sent to prebilling
        self.prebilling.purchases.create_purchase.assert_called_once()
        args, _ = self.prebilling.purchases.create_purchase.call_args
        assert len(args) == 1
        pb_req = args[0]
        assert isinstance(pb_req, PurchaseRequest)

        assert pb_req.account_id == account.id
        assert pb_req.offer_id == form_data[select["name"]]
        assert pb_req.provider_id == account.provider["id"]
        assert pb_req.skip_integration is True
        assert pb_req.skip_notification is True

    def test_disable_get(self):
        """Test disable subscription interface."""
        purchases, offers = purchases_offers(self.account_fixture)

        get_mock = self.patch_object(self.prebilling.accounts, "get", Mock(return_value=self.account_fixture))
        list_mock = self.patch_object(self.prebilling.offers, "list", Mock(return_value=offers))
        subscriptions_mock = self.patch_object(self.prebilling.purchases, "subscriptions", Mock(return_value=purchases))

        url = url_for("view.subscriptions_disable_subscription", id=self.account_fixture.id, offer_id=offers[1].id)

        response = self.client.get(url)

        get_mock.assert_called_once_with(self.account_fixture.id)
        list_mock.assert_called_once_with(self.account_fixture.provider["id"])
        subscriptions_mock.assert_called_once_with(self.account_fixture.id, self.account_fixture.provider["id"])

        soup = BeautifulSoup(response.text)

        # Check if we have a form in response.
        form = soup.find("form")
        assert form is not None

        # Check input of the form
        select = form.find("select")
        assert select is not None

        # Check if offer in the select is actually offer we passed in query param
        selected = select.find("option", selected=True)["value"]
        assert selected == offers[1].id

        # Check if we have a submit button
        submit = form.find("button", type="submit")
        assert submit is not None

    def test_disable_post(self):
        # Fixtures & Mocks.
        purchases, offers = purchases_offers(self.account_fixture)
        form_data = {"offer_id": "offer_1_id", "skip_integration": True, "skip_notification": True}
        get_mock = self.patch_object(self.prebilling.accounts, "get", Mock(return_value=self.account_fixture))
        list_mock = self.patch_object(self.prebilling.offers, "list", Mock(return_value=offers))
        self.patch_object(self.prebilling.purchases, "subscriptions", Mock(return_value=purchases))

        disable_purchase_mock = self.patch_object(
            self.prebilling.purchases, "disable_purchase", Mock(return_value=(True, None))
        )

        url = url_for("view.subscriptions_disable_subscription", id=self.account_fixture.id, offer_id=offers[1].id)

        response = self.client.post(url, data=form_data, follow_redirects=False)

        # Ensure we have successfully parsed and validate form and have redirect response here
        assert response.status_code == 302

        get_mock.assert_called_once_with(self.account_fixture.id)
        list_mock.assert_called_once_with(self.account_fixture.provider["id"])

        # Check request we sent to prebilling
        disable_purchase_mock.assert_called_once()
        args, _ = disable_purchase_mock.call_args
        assert len(args) == 1
        pb_req = args[0]
        assert isinstance(pb_req, PurchaseRequest)

        assert pb_req.account_id == self.account_fixture.id
        assert pb_req.offer_id == offers[1].id
        assert pb_req.provider_id == self.account_fixture.provider["id"]
        assert pb_req.skip_integration is True
        assert pb_req.skip_notification is True
