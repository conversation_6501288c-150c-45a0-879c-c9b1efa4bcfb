from datetime import datetime
from typing import TYPE_CHECKING
from unittest.mock import Mock

from bs4 import BeautifulSoup
from flask import url_for

from app.authhistory.proto.authhistory import (
    AuthhistoryItem,
    AuthhistoryResponse,
)
from app.proto.services import (
    AuthhistoryService,
    SessionsService,
    make_authhistory_service,
    make_sessions_service,
)
from app.sesskeeper.proto.sessions import (
    GetSessionsResponse,
    Session,
)

from .base import ComponentTestCase

if TYPE_CHECKING:
    from app.accounts.components import Component
    from app.accounts.components.base import ComponentHostMixin
    from app.accounts.components.sessions import Sessions as SessionsComponent


def generate_history_items_fixture(session: Session) -> AuthhistoryResponse:
    history_item = AuthhistoryItem(
        session_created_at=datetime.utcnow(),
        session_id=session.id,
        id="history_item_id",
    )

    deleted_history_item = AuthhistoryItem(
        session_created_at=datetime.utcnow(),
        session_id="deleted_session_id",
        id="deleted_history_item_id",
    )

    return AuthhistoryResponse(authhistory_items=[history_item, deleted_history_item])


class TestSessionsComponent(ComponentTestCase):
    # Local type hints for reusable fixtures.
    history_response_fixture: AuthhistoryResponse
    session_fixture: Session
    component: "SessionsComponent"

    @classmethod
    def get_view(cls) -> "ComponentHostMixin":
        cls._setup_constant_fixtures()
        make_sessions_service(cls.app)
        make_authhistory_service(cls.app)
        return super().get_view()

    @classmethod
    def get_component_view(cls) -> "Component":
        with cls.app.app_context():
            from app.accounts.components.sessions import Sessions as SessionsComponent

            return SessionsComponent(
                prebilling_service=cls.prebilling,
                view_only=False,
            )

    @classmethod
    def _setup_constant_fixtures(cls):
        cls.session_fixture = Session(id="sess_id")
        cls.history_response_fixture = generate_history_items_fixture(cls.session_fixture)

    def setUp(self) -> None:
        super().setUp()
        with self.app.app_context():
            sessions_service = SessionsService().get_instance()
            self.available_sessions: list[Session] = [self.session_fixture]
            self.mocked_get_sessions = self.patch_object(
                sessions_service,
                "get_sessions",
                Mock(return_value=GetSessionsResponse(sessions=self.available_sessions)),
            )
            self.mocked_delete_session = self.patch_object(sessions_service, "delete", Mock(return_value=None))
            self.prebilling_account_get = self.patch_object(
                self.prebilling.accounts, "get", Mock(return_value=self.account_fixture)
            )

    def test_render_widget(self):
        response = self.client.get(url_for("view.info"))
        self.mocked_get_sessions.assert_called_once_with(account=self.account_fixture.id)

        soup = BeautifulSoup(response.text)
        localize_datetime = self.app.jinja_env.filters["localize_datetime"]

        for session in self.available_sessions:
            s_html = soup.find(attrs={"data-session-id": session.id})

            assert s_html is not None

            text = s_html.text
            assert localize_datetime(session.created_at) in text
            assert localize_datetime(session.last_touch) in text
            assert self.component.render_client_info_labels(session.client_info, "device") in text
            assert self.component.render_client_info_labels(session.client_info, "app") in text
            assert session.ip in text

            footer = soup.select_one("div.sessions")
            assert "Session history" in footer.text

    def test_authhistory(self):
        """Test authhistory page."""
        authhistory_service = AuthhistoryService.get_instance()
        mocked_get_history = self.patch_object(
            authhistory_service, "get", Mock(return_value=self.history_response_fixture)
        )

        localize_datetime = self.app.jinja_env.filters["localize_datetime"]

        url = url_for("view.sessions_authhistory", id=self.account_fixture.id)
        response = self.client.get(url)
        assert response.status_code < 400
        soup = BeautifulSoup(response.text)
        mocked_get_history.assert_called_with(
            account_id=self.account_fixture.id, limit=self.component.authhistory_page_size + 1, skip=0
        )

        for history_item in self.history_response_fixture.authhistory_items:
            tds = soup.find(attrs={"data-history-id": history_item.session_id}).find_all("td")

            if history_item.session_id in {session.id for session in self.available_sessions}:
                assert tds[0].text.strip() == "yes"
            else:
                assert tds[0].text.strip() == "no"

            assert tds[1].text.strip() == localize_datetime(history_item.session_created_at)
            assert tds[2].text.strip() == self.component.render_client_info_labels(history_item.client_info, "device")
            assert tds[3].text.strip() == self.component.render_client_info_labels(history_item.client_info, "app")
            assert tds[4].text.strip() == history_item.user_agent
            assert tds[5].text.strip() == history_item.auth_method
            assert tds[6].text.strip() == history_item.ip_address

    def test_delete(self):
        """Test session delete action."""
        session_id = self.available_sessions[0].id
        url = url_for("view.sessions_delete_session", id=self.account_fixture.id, session_id=session_id)

        response = self.client.get(url, follow_redirects=False)
        assert response.status_code == 302
        self.prebilling_account_get.assert_called_once_with(self.account_fixture.id)
        self.mocked_delete_session.assert_called_with(id=session_id)
