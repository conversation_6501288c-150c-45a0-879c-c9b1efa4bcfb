export PATH := node_modules/.bin:$(PATH)
PYTHON := poetry run python
PYBABEL := poetry run pybabel
DEFAULT_BRANCH := origin/develop
export PYTHONPATH:= $($PATH):$(PYTHONPATH)

.ONESHELL:

init-venv:
	@rm .venv -rf
#	TEMPORARY change
#	echo "Creating venv with python3.10"
#	python3.10 -m venv .venv
	echo "Creating venv with python3.9"
	python3.9 -m venv .venv
	# --no-ansi option is required because `make` is special.
	poetry install --no-ansi
	# in case if this keep failing for some reason, try run 'poetry install' manually.


# Get lint target - all files, which were changed in current branch in comparison with DEFAULT_BRANCH.
# Thus we normally check and format not whole project, but only changed files.
setup_lint_target:
	@git fetch
	$(eval MERGE_BASE := $(shell git merge-base ${DEFAULT_BRANCH} HEAD))
	$(eval LINT_TARGET := $(shell git diff --name-only "${MERGE_BASE}" | grep -e ".py$$" | xargs ls -d 2>/dev/null))
	# Depending on execution environment, LINT_TARGET may contain "." or "". We use "." as empty value.
	$(eval LINT_TARGET := $(if $(LINT_TARGET),$(LINT_TARGET),.))
	echo "Merge base into '${DEFAULT_BRANCH}' branch is:"
	echo "${MERGE_BASE}"
	echo "LINT_TARGET is:"
	echo "${LINT_TARGET}"


# All the linters & formatters

lint-black: setup_lint_target
	@if [ "${LINT_TARGET}" = "." ]; \
	then \
        echo "skipping lint-black"; \
    else \
		${PYTHON} -m black --check --diff ${LINT_TARGET}; \
    fi;

lint-flake8: setup_lint_target
	@if [ "${LINT_TARGET}" = "." ]; \
	then \
		echo "skipping lint-flake8"; \
	else \
		${PYTHON} -m flake8 --statistics ${LINT_TARGET}; \
	fi;

lint-isort: setup_lint_target
	@if [ "${LINT_TARGET}" = "." ]; \
	then \
		echo "skipping lint-isort"; \
	else \
		${PYTHON} -m isort.main --diff --check-only ${LINT_TARGET}; \
	fi;

lint: lint-black lint-isort lint-flake8

# Format all changed in files with black.
format-black: setup_lint_target
	@if [ "${LINT_TARGET}" = "." ]; \
	then \
		echo "skipping format-black"; \
	else \
		${PYTHON} -m black ${LINT_TARGET}; \
	fi;

# Format all changed in files with isort.
format-isort: setup_lint_target
	@if [ "${LINT_TARGET}" = "." ]; \
	then \
		echo "skipping format-isort"; \
	else \
		${PYTHON} -m isort.main ${LINT_TARGET}; \
	fi;

format: format-black format-isort

# Force format all source files, use with care.
force-format:
	${PYTHON} -m isort.main app/ tests/
	${PYTHON} -m black app/ tests/

# Force lint all source files, use with care.
force-lint:
	${PYTHON} -m isort.main --diff --check-only app/ tests/
	${PYTHON} -m black --check --diff app/ tests/
	${PYTHON} -m flake8 --statistics app/ tests/

# Tests.

# For running local tests.
MONGODB_CONTAINER_NAME=mongodb-for-test
MONGODB_CONTAINER_PORT=57020
CLICKHOUSE_CONTAINER_NAME=clickhouse-for-test
CLICKHOUSE_CONTAINER_PORT=18123

run-mongodb:
	@echo "Starting MongoDB container ${MONGODB_CONTAINER_NAME}..."
	if docker ps --format '{{.Names}}' | grep -q "^${MONGODB_CONTAINER_NAME}\$$"; then \
		echo "Container ${MONGODB_CONTAINER_NAME} is already running"; \
	else \
		docker run -d --rm -p ${MONGODB_CONTAINER_PORT}:27017 --name ${MONGODB_CONTAINER_NAME} mongo:5; \
		echo "MongoDB container ${MONGODB_CONTAINER_NAME} is now running and available on port ${MONGODB_CONTAINER_PORT}"; \
	fi

mongo: run-mongodb  # alias

stop-mongodb:
	@echo "Stopping MongoDB container...";
	@if docker ps --format '{{.Names}}' | grep -q "^${MONGODB_CONTAINER_NAME}\$$"; then \
		docker stop ${MONGODB_CONTAINER_NAME}; \
		echo "Container ${MONGODB_CONTAINER_NAME} stopped."; \
    else \
		echo "Container ${MONGODB_CONTAINER_NAME} is not running"; \
	fi

run-clickhouse:
	@echo "Starting ClickHouse container ${CLICKHOUSE_CONTAINER_NAME}..."
	if docker ps --format '{{.Names}}' | grep -q "^${CLICKHOUSE_CONTAINER_NAME}\$$"; then \
		echo "Container ${CLICKHOUSE_CONTAINER_NAME} is already running"; \
	else \
		docker run -d --rm -p ${CLICKHOUSE_CONTAINER_PORT}:8123 --name ${CLICKHOUSE_CONTAINER_NAME} yandex/clickhouse-server; \
		echo "ClickHouse container ${CLICKHOUSE_CONTAINER_NAME} is now running and available on port ${CLICKHOUSE_CONTAINER_PORT}"; \
	fi

clickhouse: run-clickhouse  # alias

stop-clickhouse:
	@echo "Stopping ClickHouse container...";
	@if docker ps --format '{{.Names}}' | grep -q "^${CLICKHOUSE_CONTAINER_NAME}\$$"; then \
		docker stop ${CLICKHOUSE_CONTAINER_NAME}; \
		echo "Container ${CLICKHOUSE_CONTAINER_NAME} stopped."; \
    else \
		echo "Container ${CLICKHOUSE_CONTAINER_NAME} is not running"; \
	fi

run-test-containers: run-mongodb run-clickhouse

stop-test-containers: stop-mongodb stop-clickhouse

test-pytest:
	@${PYTHON} -m pytest --cov=app --cov=cmf --cov=epg --cov=api tests/ --junitxml=pytest_report.xml
	PYTEST_EXIT_CODE=$$?
	${PYTHON} -m coverage report | grep TOTAL
	${PYTHON} -m coverage xml
	exit $$PYTEST_EXIT_CODE

# Recipe for manual test run
test: run-test-containers test-pytest stop-test-containers

# Recipe for running tests within ci-cd
_test: test-integrity test-pytest

# used in "port-forward"
host := fe@b612-app4-devb

# Recipe to test integrity of submodules with protofiles.
# If `make proto` leaves some uncommitted changes - then this check is considered as failed.
test-integrity: proto
	@if [ -n "$$(git status --porcelain)" ]; then \
		echo "Integrity is broken! Uncommitted changes detected: "; \
		git status --porcelain; \
		echo "Check your submodules."; \
		echo "To fix problem with i18n, try `make babel` recipe"; \
		exit 1; \
	fi

# for local development.
# Port forward through custom host: `make port-forward host=<EMAIL>`
port-forward:
	ssh ${host} -A \
		-L 27017:mongorc:27017 \
		-L 8086:localhost:8086  \
		-L 15672:localhost:15672 \
		-L 8013:localhost:8013 \
		-L 8101:localhost:8101 \
		-L 6666:localhost:6666 \
		-L 9000:localhost:9000 \
		-L 9090:localhost:9090  \
		-L 8905:localhost:8905 \
		-L 8123:localhost:8123

port-forward-grpc:
	ssh ${host} -A \
		-L 7825:localhost:7825 \
		-L 8111:localhost:8111 \
		-L 8814:localhost:8814 \
		-L 8886:localhost:8886 \
		-L 8905:localhost:8905 \
		-L 9071:localhost:9071 \
		-L 9091:localhost:9091 \
		-L 9600:localhost:9600 \
		-L 9603:localhost:9603 \
		-L 9868:localhost:9868 \
		-L 10002:localhost:10002 \
		-L 10012:localhost:10012 \
		-L 10500:localhost:10500


# For celery-beat debugging, start not detached beat
celery-beat-debug:
	celery --app worker.celery beat --scheduler app.tasks.CustomScheduler --loglevel=debug

# For static
web:
	# This line is a crutch. It exists because this code, for some unknown reason, no longer runs:
	# https://github.com/lex009/flask-admin-lte/blob/7aa7b7bc222ca088ca5e2f67db57533948a96f82/setup.py#L20
	yarn --cwd .venv/src/flask-admin-lte
	yarn; NODE_ENV=production webpack --progress

# Shortcut forwards to build
update-build:
	make -C ../../build/ update-build

update-source:
	make -C ../../build/ update-source

install:
	make -C ../../build/ install

configure:
	make -C ../../build/ configure

link:
	make -C ../../build/ link

unlink:
	make -C ../../build/ unlink

debug:
	make -C ../../build/ debug


proto/megogo:
	@echo "make proto/megogo..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/megogo \
		--python_out=./app/vod/proto_megogo \
		--grpc_python_out=./app/vod/proto_megogo \
		./app/proto/megogo/control.proto

proto/sessions:
	@echo "make proto/sessions..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/sesskeeper \
		--python_betterproto_out=./app/sesskeeper/proto \
		./app/proto/sesskeeper/sessions.proto

	# Simplify structure for generated files.
	mv ./app/sesskeeper/proto/tightvideo/protobuf/*.py ./app/sesskeeper/proto/
	rm -rf ./app/sesskeeper/proto/tightvideo

	# Do not need this.
	rm -rf ./app/sesskeeper/proto/google
	@rmdir google  # It creates empty 'google' directory in root for no reason

proto/authhistory:
	@echo "make proto/authhistory..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/authhistory \
		--python_betterproto_out=./app/authhistory/proto \
		./app/proto/authhistory/authhistory.proto

	# Do not need this.
	rm -rf ./app/authhistory/proto/google
	@rmdir google  # It creates empty 'google' directory in root for no reason


proto/linkshortener:
	@echo "make proto/linkshortener..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/linkshortener \
		--python_betterproto_out=./app/user_utils/linkshortener/proto \
		./app/proto/linkshortener/linkshortener.proto
	@rm -rf ./app/user_utils/linkshortener/proto/google

	# It creates empty 'google' directory in root for no reason
	@rmdir google

proto/stores:
	@echo "make proto/stores..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/stores \
		--python_betterproto_out=./app/stores/proto \
		./app/proto/stores/*.proto
	# Simplify structure for generated files.
	mv ./app/stores/proto/tightvideo/protobuf/*.py ./app/stores/proto/
	rm -rf ./app/stores/proto/tightvideo

	# Do not need this.
	@rm -rf ./app/stores/proto/google
	@rmdir google  # It creates empty 'google' directory in root for no reason

proto/promo:
	@echo "make proto/promo..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/promo \
		--python_betterproto_out=./app/promo/proto \
		./app/proto/promo/*.proto
		
	rm -rf ./app/promo/proto/promo 

	# Do not need this.
	@rm -rf ./app/promo/proto/google
	@rmdir google  # It creates empty 'google' directory in root for no reason

proto/promo-pools:
	@echo "make proto/promo-pools..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto \
		-I ./app/proto/vendor \
		-I ./app/proto/promopoolproto/promopool \
		--python_betterproto_out=./app/promo/proto \
		./app/proto/promopoolproto/promopool/v1/*.proto

	# Simplify structure of generated files.
	mv ./app/promo/proto/promopool/v1.py ./app/promo/proto/promopool.py
	rm -rf ./app/promo/proto/promopool

	# Do not need this.
	@rm -rf ./app/promo/proto/google

	# I have no idea why these empty directories are created in root.
	@rmdir google
	@rmdir promopool

proto/prebilling-promo:
	@echo "make proto/prebilling-promo..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/prebillingproto \
		--python_betterproto_out=./app/promo/proto \
		./app/proto/prebillingproto/prebilling_promo.proto

	# Simplify structure of generated files.
	mv ./app/promo/proto/tightvideo/prebilling/purchases.py ./app/promo/proto/purchases.py
	rm -rf ./app/promo/proto/tightvideo

	# Do not need this.
	@rm -rf ./app/promo/proto/google
	@rmdir google  # It creates empty 'google' directory in root for no reason


proto/prebilling-purchases:
	@echo "make proto/prebilling-purchases..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/prebillingproto \
		--python_out=./app/purchases/proto \
		--grpc_python_out=./app/purchases/proto \
		./app/proto/prebillingproto/prebilling_purchases.proto

proto/seller:
	@echo "make proto/seller..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/seller \
		--python_betterproto_out=./app/seller/proto \
		./app/proto/seller/*.proto

	# Simplify structure for generated files.
	mv ./app/seller/proto/tightvideo/protobuf/*.py ./app/seller/proto/
	rm -rf ./app/seller/proto/tightvideo

	# Do not need this.
	@rm -rf ./app/seller/proto/google
	@rmdir google  # It creates empty 'google' directory in root for no reason

proto/vodproto:
	@echo "make proto/vodproto..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		-I ./app/proto/vodproto \
		--python_out=./app/vod/vod/proto \
		--grpc_python_out=./app/vod/vod/proto \
		./app/proto/vodproto/*.proto

proto/mamproto:
	@echo "make proto/mamproto..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto \
		-I ./app/proto/vendor \
		-I ./app/proto/mamproto \
		--python_betterproto_out=./app/media_assets_manager \
		./app/proto/mamproto/v1/*.proto

	# Extra actions, because mamproto has unusual structure.
	@rm -rf ./app/media_assets_manager/proto
	@mv ./app/media_assets_manager/mamproto ./app/media_assets_manager/proto
	# Do not need this.
	@rm -rf ./app/media_assets_manager/google
	@rmdir google  # It creates empty 'google' directory in root for no reason

proto/google:
	@echo "make proto/google..."
	@${PYTHON} -m grpc_tools.protoc \
		-I ./app/proto/vendor \
		--python_out=. \
		./app/proto/vendor/google/api/*.proto

	@rm -rf google  # it generates some files in root for some reason, we never asked for them.

update-submodules:
	git submodule update --init

proto/all:	proto/megogo \
			proto/sessions \
			proto/authhistory  \
			proto/linkshortener \
			proto/stores \
			proto/promo \
			proto/promo-pools \
			proto/prebilling-promo \
			proto/prebilling-purchases \
			proto/seller \
			proto/vodproto \
			proto/google


proto:	update-submodules \
		proto/all

_reset_proto_dir:
	@echo "Resetting app/proto directory..."
	rm -rf app/proto
	git checkout HEAD -- app/proto
	@echo "Reset complete!"

# Fix submodules after checkout to different branch/version. Use with care.
reset-proto: _reset_proto_dir \
			 update-submodules


PROJECT_DIR := $(shell pwd)


BABEL_TRANS=app/translations
PO_FILE=$(BABEL_TRANS)/ru/LC_MESSAGES/messages.po

_babel-extract:
	${PYBABEL} extract -F babel.cfg --omit-header --sort-by-file -o $(BABEL_TRANS)/messages.pot . >/dev/null 2>&1

_babel-update:
	${PYBABEL} update --omit-header --no-wrap -i $(BABEL_TRANS)/messages.pot -d $(BABEL_TRANS)

_babel-compile:
	${PYBABEL} compile -f -d $(BABEL_TRANS)

babel: _babel-extract _babel-update _babel-compile

babel-test/fuzzy: _babel-extract
	@echo "== Checking for fuzzy translations =="
	@if grep -R "#, fuzzy" $(PO_FILE); then \
		echo "❌ Fuzzy translations found"; \
		exit 1; \
	fi

babel-test/obsolete: _babel-extract
	@echo "== Checking for obsolete translations =="
	@if grep -R "^#~" $(PO_FILE); then \
		echo "❌ Obsolete translations detected"; \
		exit 1; \
	fi

babel-test/missing: _babel-extract
	@echo "== Checking for missing translations =="
	@if grep -R "^msgstr \"\"" $(PO_FILE); then \
		echo "❌ Missing translations detected"; \
		exit 1; \
	fi

# This recipe is here to help to get rid of old "translations". It may be deleted someday later.
babel-test/no-cyrillic: _babel-extract
	@echo "== Checking that msgid contains only ASCII characters =="
	@if grep -R --include="*.po" -nE 'msgid ".*[А-Яа-яЁё].*"' $(PO_FILE); then \
		echo "❌ Cyrillic characters found in msgid"; \
		exit 1; \
	fi

babel-test: babel-test/missing \
 			babel-test/obsolete \
 			babel-test/no-cyrillic \
 			babel-test/fuzzy
	@echo "✅ Localization is up to date"

release-notes-stub:
	${PYTHON} release_notes/main.py generate-stub

release-notes-validate:
	${PYTHON} release_notes/main.py validate

release-notes-generate-dry-run:
	${PYTHON} release_notes/main.py generate-release-notes

release-notes-generate:
	${PYTHON} release_notes/main.py generate-release-notes --save

release-notes-debug:
	${PYTHON} release_notes/main.py generate-release-notes --save --debug


# Docker stuff for testing.
# Build image.
docker-build:
	docker build --ssh default -t cms .

# Explore built image.
docker-it-bash:
	docker run -it --entrypoint /bin/bash cms
