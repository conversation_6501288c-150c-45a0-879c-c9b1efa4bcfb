#!/usr/bin/env python3
"""
Test script to validate episode detection logic works with real IVI feed structure.
This test doesn't require MongoDB and focuses on parsing and episode detection.
"""

import xml.etree.ElementTree as ET
from app.abcvod.providers.ivi.api import ContentInfo, CompilationInfo

def test_real_feed_episode_detection():
    """Test episode detection logic with real feed structure (compilation_id)."""
    
    print("🧪 Testing Real Feed Episode Detection Logic")
    print("=" * 60)
    
    # Create test XML that matches real feed structure
    real_feed_xml = """<?xml version="1.0" encoding="UTF-8"?>
<root>
    <compilation>
        <id>12345</id>
        <title>Test Series</title>
        <description>A test series</description>
    </compilation>
    
    <content>
        <id>67890</id>
        <title>Episode 1</title>
        <description>First episode</description>
        <compilation_id>12345</compilation_id>
        <season>1</season>
        <episode>1</episode>
        <duration>1800</duration>
    </content>
    
    <content>
        <id>67891</id>
        <title>Episode 2</title>
        <description>Second episode</description>
        <compilation_id>12345</compilation_id>
        <season>1</season>
        <episode>2</episode>
        <duration>1900</duration>
    </content>
    
    <content>
        <id>67892</id>
        <title>Standalone Movie</title>
        <description>A standalone movie</description>
        <duration>7200</duration>
    </content>
</root>"""
    
    # Parse the XML
    root = ET.fromstring(real_feed_xml)
    
    # Test parsing compilation (series)
    compilation_elem = root.find('.//compilation')
    series = CompilationInfo.from_xml(ET.tostring(compilation_elem))
    print(f"✅ Series parsed: ID={series.id}, title='{series.title}'")
    
    # Test parsing content elements
    content_elements = root.findall('.//content')
    episodes_detected = 0
    standalone_detected = 0
    
    for content_elem in content_elements:
        content = ContentInfo.from_xml(ET.tostring(content_elem))
        
        # Apply our episode detection logic
        is_episode = bool(content.compilation or content.compilation_id)
        
        if is_episode:
            episodes_detected += 1
            print(f"📺 Episode detected: ID={content.id}, title='{content.title}'")
            print(f"   compilation_id={content.compilation_id}, season={content.season}, episode={content.episode}")
        else:
            standalone_detected += 1
            print(f"🎬 Standalone content: ID={content.id}, title='{content.title}'")
    
    # Test results
    print(f"\n📊 Detection Results:")
    print(f"   Episodes detected: {episodes_detected}")
    print(f"   Standalone content: {standalone_detected}")
    
    # Verify expected results
    expected_episodes = 2
    expected_standalone = 1
    
    if episodes_detected == expected_episodes and standalone_detected == expected_standalone:
        print(f"\n✅ SUCCESS: Episode detection logic works correctly!")
        print(f"   - Correctly identified {episodes_detected} episodes with compilation_id")
        print(f"   - Correctly identified {standalone_detected} standalone content")
        return True
    else:
        print(f"\n❌ FAILURE: Episode detection logic failed!")
        print(f"   Expected: {expected_episodes} episodes, {expected_standalone} standalone")
        print(f"   Got: {episodes_detected} episodes, {standalone_detected} standalone")
        return False

def test_backward_compatibility():
    """Test that our logic still works with old test data structure (compilation field)."""
    
    print(f"\n🔄 Testing Backward Compatibility")
    print("=" * 40)
    
    # Create test XML that matches old test data structure
    old_test_xml = """<?xml version="1.0" encoding="UTF-8"?>
<root>
    <content>
        <id>12345</id>
        <title>Test Episode</title>
        <description>Test episode with old structure</description>
        <compilation>67890</compilation>
        <season>2</season>
        <episode>3</episode>
        <duration>2400</duration>
    </content>
</root>"""
    
    # Parse the XML
    root = ET.fromstring(old_test_xml)
    content_elem = root.find('.//content')
    content = ContentInfo.from_xml(ET.tostring(content_elem))
    
    # Apply our episode detection logic
    is_episode = bool(content.compilation or content.compilation_id)
    
    if is_episode:
        print(f"✅ Backward compatibility works: Episode detected with compilation={content.compilation}")
        return True
    else:
        print(f"❌ Backward compatibility failed: Episode not detected")
        return False

if __name__ == "__main__":
    success1 = test_real_feed_episode_detection()
    success2 = test_backward_compatibility()
    
    if success1 and success2:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   Episode detection logic works for both real feed and test data structures.")
        exit(0)
    else:
        print(f"\n💥 SOME TESTS FAILED!")
        exit(1)
